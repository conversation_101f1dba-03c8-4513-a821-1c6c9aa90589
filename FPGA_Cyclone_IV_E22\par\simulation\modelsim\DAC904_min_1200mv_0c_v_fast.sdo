// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Fast Corner delays for the design using part EP4CE6E22C8,
// with speed grade M, core voltage 1.2V, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 03:12:55")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (602:602:602) (644:644:644))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (724:724:724) (814:814:814))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (866:866:866) (990:990:990))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (819:819:819) (924:924:924))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (816:816:816) (923:923:923))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (816:816:816) (926:926:926))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (645:645:645) (744:744:744))
        (IOPATH i o (2496:2496:2496) (2557:2557:2557))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (686:686:686) (778:778:778))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (862:862:862) (957:957:957))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (904:904:904) (1025:1025:1025))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (712:712:712) (800:800:800))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (779:779:779) (877:877:877))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (514:514:514) (564:564:564))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (513:513:513) (561:561:561))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (555:555:555) (507:507:507))
        (IOPATH i o (1570:1570:1570) (1565:1565:1565))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_spi_valid\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (442:442:442) (506:506:506))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1153:1153:1153) (983:983:983))
        (IOPATH i o (1615:1615:1615) (1612:1612:1612))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (510:510:510) (584:584:584))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (915:915:915) (1074:1074:1074))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (495:495:495) (567:567:567))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1090:1090:1090) (1244:1244:1244))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (753:753:753) (860:860:860))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (596:596:596) (680:680:680))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (616:616:616) (705:705:705))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (620:620:620) (710:710:710))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (573:573:573) (649:649:649))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (794:794:794) (913:913:913))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (466:466:466) (530:530:530))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (652:652:652) (753:753:753))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (792:792:792) (906:906:906))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (774:774:774) (683:683:683))
        (IOPATH i o (1615:1615:1615) (1612:1612:1612))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (741:741:741) (641:641:641))
        (IOPATH i o (1615:1615:1615) (1612:1612:1612))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (108:108:108) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (610:610:610) (610:610:610))
        (PORT inclk[0] (1098:1098:1098) (1098:1098:1098))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (1121:1121:1121) (1119:1119:1119))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE spi_sclk\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (108:108:108) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT asdata (977:977:977) (886:886:886))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (124:124:124) (163:163:163))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (121:121:121) (159:159:159))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT asdata (301:301:301) (344:344:344))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (121:121:121) (161:161:161))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT asdata (298:298:298) (339:339:339))
        (PORT ena (2621:2621:2621) (2359:2359:2359))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT asdata (530:530:530) (599:599:599))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (161:161:161))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[10\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datad (121:121:121) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (121:121:121) (159:159:159))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[13\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datac (128:128:128) (169:169:169))
        (IOPATH datac combout (120:120:120) (125:125:125))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d1\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (2017:2017:2017) (2274:2274:2274))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d1)
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (760:760:760))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (752:752:752) (724:724:724))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d2)
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (760:760:760))
        (PORT asdata (298:298:298) (341:341:341))
        (PORT clrn (752:752:752) (724:724:724))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[0\]\~3)
    (DELAY
      (ABSOLUTE
        (IOPATH datac combout (190:190:190) (195:195:195))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (760:760:760))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (2299:2299:2299) (2545:2545:2545))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[1\]\~2)
    (DELAY
      (ABSOLUTE
        (PORT datad (131:131:131) (174:174:174))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (760:760:760))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (2299:2299:2299) (2545:2545:2545))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[2\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (199:199:199))
        (PORT datab (147:147:147) (201:201:201))
        (IOPATH dataa combout (188:188:188) (203:203:203))
        (IOPATH datab combout (190:190:190) (205:205:205))
        (IOPATH datac combout (190:190:190) (195:195:195))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (760:760:760))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (2299:2299:2299) (2545:2545:2545))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[3\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (196:196:196))
        (PORT datab (144:144:144) (197:197:197))
        (PORT datad (123:123:123) (162:162:162))
        (IOPATH dataa combout (158:158:158) (173:173:173))
        (IOPATH datab combout (160:160:160) (176:176:176))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (760:760:760))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (2299:2299:2299) (2545:2545:2545))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (141:141:141) (195:195:195))
        (PORT datab (145:145:145) (197:197:197))
        (PORT datac (116:116:116) (157:157:157))
        (PORT datad (121:121:121) (160:160:160))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always2\~1)
    (DELAY
      (ABSOLUTE
        (PORT datab (130:130:130) (177:177:177))
        (PORT datac (122:122:122) (166:166:166))
        (PORT datad (90:90:90) (108:108:108))
        (IOPATH datab combout (166:166:166) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT asdata (1244:1244:1244) (1447:1447:1447))
        (PORT clrn (761:761:761) (732:732:732))
        (PORT ena (1539:1539:1539) (1760:1760:1760))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT asdata (1033:1033:1033) (1172:1172:1172))
        (PORT clrn (761:761:761) (732:732:732))
        (PORT ena (1539:1539:1539) (1760:1760:1760))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[14\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (321:321:321) (387:387:387))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT asdata (376:376:376) (420:420:420))
        (PORT ena (2793:2793:2793) (2510:2510:2510))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT asdata (992:992:992) (1116:1116:1116))
        (PORT clrn (761:761:761) (732:732:732))
        (PORT ena (1539:1539:1539) (1760:1760:1760))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT asdata (1025:1025:1025) (1157:1157:1157))
        (PORT clrn (761:761:761) (732:732:732))
        (PORT ena (1539:1539:1539) (1760:1760:1760))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (210:210:210) (291:291:291))
        (PORT datab (210:210:210) (285:285:285))
        (PORT datac (192:192:192) (261:261:261))
        (PORT datad (183:183:183) (237:237:237))
        (IOPATH dataa combout (181:181:181) (184:184:184))
        (IOPATH datab combout (182:182:182) (188:188:188))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (408:408:408) (503:503:503))
        (PORT datab (629:629:629) (774:774:774))
        (PORT datac (377:377:377) (454:454:454))
        (PORT datad (617:617:617) (759:759:759))
        (IOPATH dataa combout (170:170:170) (165:165:165))
        (IOPATH datab combout (188:188:188) (193:193:193))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (214:214:214) (295:295:295))
        (PORT datab (208:208:208) (282:282:282))
        (PORT datac (190:190:190) (260:260:260))
        (PORT datad (183:183:183) (238:238:238))
        (IOPATH dataa combout (195:195:195) (203:203:203))
        (IOPATH datab combout (188:188:188) (177:177:177))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (222:222:222) (306:306:306))
        (PORT datab (196:196:196) (270:270:270))
        (PORT datac (190:190:190) (258:258:258))
        (PORT datad (188:188:188) (245:245:245))
        (IOPATH dataa combout (170:170:170) (165:165:165))
        (IOPATH datab combout (188:188:188) (177:177:177))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (221:221:221) (305:305:305))
        (PORT datab (198:198:198) (272:272:272))
        (PORT datac (190:190:190) (257:257:257))
        (PORT datad (187:187:187) (245:245:245))
        (IOPATH dataa combout (188:188:188) (179:179:179))
        (IOPATH datab combout (188:188:188) (177:177:177))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (215:215:215) (297:297:297))
        (PORT datab (208:208:208) (282:282:282))
        (PORT datac (189:189:189) (260:260:260))
        (PORT datad (184:184:184) (240:240:240))
        (IOPATH dataa combout (170:170:170) (165:165:165))
        (IOPATH datab combout (188:188:188) (193:193:193))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (223:223:223) (307:307:307))
        (PORT datab (195:195:195) (268:268:268))
        (PORT datac (191:191:191) (259:259:259))
        (PORT datad (189:189:189) (246:246:246))
        (IOPATH dataa combout (170:170:170) (165:165:165))
        (IOPATH datab combout (190:190:190) (188:188:188))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (219:219:219) (303:303:303))
        (PORT datab (203:203:203) (279:279:279))
        (PORT datac (189:189:189) (256:256:256))
        (PORT datad (185:185:185) (243:243:243))
        (IOPATH dataa combout (188:188:188) (184:184:184))
        (IOPATH datab combout (190:190:190) (188:188:188))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (206:206:206) (288:288:288))
        (PORT datab (212:212:212) (287:287:287))
        (PORT datac (194:194:194) (263:263:263))
        (PORT datad (182:182:182) (238:238:238))
        (IOPATH dataa combout (170:170:170) (165:165:165))
        (IOPATH datab combout (188:188:188) (181:181:181))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (220:220:220) (304:304:304))
        (PORT datab (200:200:200) (275:275:275))
        (PORT datac (190:190:190) (257:257:257))
        (PORT datad (187:187:187) (244:244:244))
        (IOPATH dataa combout (170:170:170) (165:165:165))
        (IOPATH datab combout (160:160:160) (176:176:176))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (219:219:219) (304:304:304))
        (PORT datab (201:201:201) (276:276:276))
        (PORT datac (189:189:189) (256:256:256))
        (PORT datad (186:186:186) (243:243:243))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (190:190:190) (205:205:205))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (207:207:207) (288:288:288))
        (PORT datab (211:211:211) (286:286:286))
        (PORT datac (193:193:193) (262:262:262))
        (PORT datad (182:182:182) (238:238:238))
        (IOPATH dataa combout (188:188:188) (196:196:196))
        (IOPATH datab combout (190:190:190) (197:197:197))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (211:211:211) (292:292:292))
        (PORT datab (209:209:209) (284:284:284))
        (PORT datac (191:191:191) (261:261:261))
        (PORT datad (183:183:183) (237:237:237))
        (IOPATH dataa combout (165:165:165) (159:159:159))
        (IOPATH datab combout (167:167:167) (174:174:174))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (212:212:212) (294:294:294))
        (PORT datab (209:209:209) (286:286:286))
        (PORT datad (194:194:194) (255:255:255))
        (IOPATH dataa combout (195:195:195) (203:203:203))
        (IOPATH datab combout (188:188:188) (177:177:177))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (452:452:452) (556:556:556))
        (PORT datab (211:211:211) (287:287:287))
        (PORT datad (182:182:182) (237:237:237))
        (IOPATH dataa combout (166:166:166) (157:157:157))
        (IOPATH datab combout (188:188:188) (177:177:177))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (223:223:223) (307:307:307))
        (PORT datab (209:209:209) (285:285:285))
        (PORT datad (189:189:189) (246:246:246))
        (IOPATH dataa combout (165:165:165) (159:159:159))
        (IOPATH datab combout (161:161:161) (174:174:174))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (218:218:218) (301:301:301))
        (PORT datab (204:204:204) (279:279:279))
        (PORT datad (185:185:185) (242:242:242))
        (IOPATH dataa combout (159:159:159) (173:173:173))
        (IOPATH datab combout (182:182:182) (193:193:193))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (425:425:425) (513:513:513))
        (PORT datab (128:128:128) (176:176:176))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (219:219:219) (275:275:275))
        (PORT datab (194:194:194) (234:234:234))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (403:403:403) (483:483:483))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (130:130:130) (181:181:181))
        (PORT datab (180:180:180) (222:222:222))
        (IOPATH dataa combout (166:166:166) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (176:176:176) (218:218:218))
        (PORT datab (130:130:130) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (130:130:130) (180:180:180))
        (PORT datab (197:197:197) (238:238:238))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (130:130:130) (180:180:180))
        (PORT datab (176:176:176) (215:215:215))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (257:257:257))
        (PORT datab (181:181:181) (222:222:222))
        (IOPATH dataa combout (166:166:166) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (257:257:257))
        (PORT datab (202:202:202) (244:244:244))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (199:199:199) (241:241:241))
        (PORT datab (130:130:130) (177:177:177))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (167:167:167) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (258:258:258))
        (PORT datab (190:190:190) (230:230:230))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (195:195:195) (234:234:234))
        (PORT datab (200:200:200) (257:257:257))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (258:258:258))
        (PORT datab (201:201:201) (243:243:243))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (130:130:130) (181:181:181))
        (PORT datab (191:191:191) (231:231:231))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (180:180:180) (223:223:223))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (129:129:129) (180:180:180))
        (PORT datab (174:174:174) (213:213:213))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (750:750:750) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (754:754:754) (851:851:851))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (594:594:594) (703:703:703))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (339:339:339) (401:401:401))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (167:167:167) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (199:199:199) (260:260:260))
        (PORT datab (566:566:566) (667:667:667))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (131:131:131) (181:181:181))
        (PORT datab (200:200:200) (242:242:242))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (413:413:413))
        (PORT datab (142:142:142) (190:190:190))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (451:451:451) (521:521:521))
        (PORT datab (206:206:206) (261:261:261))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (193:193:193))
        (PORT datab (567:567:567) (668:668:668))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (317:317:317) (392:392:392))
        (PORT datab (201:201:201) (244:244:244))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (406:406:406) (501:501:501))
        (PORT datab (631:631:631) (776:776:776))
        (PORT datac (379:379:379) (456:456:456))
        (PORT datad (616:616:616) (758:758:758))
        (IOPATH dataa combout (188:188:188) (184:184:184))
        (IOPATH datab combout (190:190:190) (188:188:188))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (414:414:414) (493:493:493))
        (PORT datab (142:142:142) (190:190:190))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (552:552:552) (659:659:659))
        (PORT datab (337:337:337) (402:402:402))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (407:407:407) (502:502:502))
        (PORT datab (630:630:630) (775:775:775))
        (PORT datac (378:378:378) (456:456:456))
        (PORT datad (616:616:616) (759:759:759))
        (IOPATH dataa combout (195:195:195) (193:193:193))
        (IOPATH datab combout (188:188:188) (177:177:177))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (195:195:195) (235:235:235))
        (PORT datab (142:142:142) (190:190:190))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (193:193:193))
        (PORT datab (202:202:202) (245:245:245))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (416:416:416))
        (PORT datab (142:142:142) (190:190:190))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (411:411:411) (507:507:507))
        (PORT datab (623:623:623) (767:767:767))
        (PORT datac (373:373:373) (447:447:447))
        (PORT datad (619:619:619) (761:761:761))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (194:194:194))
        (PORT datab (189:189:189) (228:228:228))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (407:407:407) (502:502:502))
        (PORT datab (632:632:632) (777:777:777))
        (PORT datac (380:380:380) (457:457:457))
        (PORT datad (616:616:616) (759:759:759))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (216:216:216) (272:272:272))
        (PORT datab (194:194:194) (234:234:234))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (193:193:193))
        (IOPATH dataa combout (195:195:195) (203:203:203))
        (IOPATH cin combout (187:187:187) (204:204:204))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (759:759:759) (858:858:858))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (783:783:783) (924:924:924))
        (PORT d[1] (770:770:770) (903:903:903))
        (PORT d[2] (748:748:748) (878:878:878))
        (PORT d[3] (898:898:898) (1049:1049:1049))
        (PORT d[4] (605:605:605) (710:710:710))
        (PORT d[5] (607:607:607) (718:718:718))
        (PORT d[6] (1234:1234:1234) (1423:1423:1423))
        (PORT d[7] (617:617:617) (727:727:727))
        (PORT d[8] (602:602:602) (709:709:709))
        (PORT d[9] (640:640:640) (766:766:766))
        (PORT d[10] (1040:1040:1040) (1205:1205:1205))
        (PORT d[11] (626:626:626) (740:740:740))
        (PORT clk (941:941:941) (974:974:974))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (974:974:974))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (942:942:942) (975:975:975))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (923:923:923) (955:955:955))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (675:675:675) (768:768:768))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (745:745:745) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (757:757:757) (728:728:728))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (681:681:681) (770:770:770))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (745:745:745) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (757:757:757) (728:728:728))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (578:578:578) (683:683:683))
        (PORT d[1] (754:754:754) (884:884:884))
        (PORT d[2] (589:589:589) (699:699:699))
        (PORT d[3] (1091:1091:1091) (1270:1270:1270))
        (PORT d[4] (432:432:432) (520:520:520))
        (PORT d[5] (420:420:420) (509:509:509))
        (PORT d[6] (1443:1443:1443) (1670:1670:1670))
        (PORT d[7] (431:431:431) (518:518:518))
        (PORT d[8] (418:418:418) (500:500:500))
        (PORT d[9] (449:449:449) (546:546:546))
        (PORT d[10] (1066:1066:1066) (1235:1235:1235))
        (PORT d[11] (427:427:427) (511:511:511))
        (PORT clk (940:940:940) (973:973:973))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (940:940:940) (973:973:973))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (974:974:974))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (922:922:922) (954:954:954))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (462:462:462) (486:486:486))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (864:864:864) (976:976:976))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (743:743:743) (762:762:762))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (754:754:754) (726:726:726))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (877:877:877) (1007:1007:1007))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (743:743:743) (762:762:762))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (754:754:754) (726:726:726))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (759:759:759) (888:888:888))
        (PORT d[1] (965:965:965) (1127:1127:1127))
        (PORT d[2] (775:775:775) (911:911:911))
        (PORT d[3] (894:894:894) (1045:1045:1045))
        (PORT d[4] (625:625:625) (737:737:737))
        (PORT d[5] (627:627:627) (745:745:745))
        (PORT d[6] (1232:1232:1232) (1425:1425:1425))
        (PORT d[7] (762:762:762) (885:885:885))
        (PORT d[8] (602:602:602) (707:707:707))
        (PORT d[9] (634:634:634) (756:756:756))
        (PORT d[10] (878:878:878) (1021:1021:1021))
        (PORT d[11] (656:656:656) (782:782:782))
        (PORT clk (941:941:941) (974:974:974))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (974:974:974))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (942:942:942) (975:975:975))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (923:923:923) (955:955:955))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (672:672:672) (762:762:762))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (762:762:762))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (754:754:754) (727:727:727))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (667:667:667) (752:752:752))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (762:762:762))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (754:754:754) (727:727:727))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (583:583:583) (695:695:695))
        (PORT d[1] (880:880:880) (1022:1022:1022))
        (PORT d[2] (561:561:561) (666:666:666))
        (PORT d[3] (1098:1098:1098) (1278:1278:1278))
        (PORT d[4] (420:420:420) (507:507:507))
        (PORT d[5] (408:408:408) (495:495:495))
        (PORT d[6] (1449:1449:1449) (1676:1676:1676))
        (PORT d[7] (406:406:406) (486:486:486))
        (PORT d[8] (417:417:417) (502:502:502))
        (PORT d[9] (571:571:571) (682:682:682))
        (PORT d[10] (742:742:742) (868:868:868))
        (PORT d[11] (414:414:414) (495:495:495))
        (PORT clk (940:940:940) (972:972:972))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (940:940:940) (972:972:972))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (973:973:973))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (922:922:922) (953:953:953))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (462:462:462) (485:485:485))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (749:749:749) (852:852:852))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (740:740:740) (759:759:759))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (751:751:751) (723:723:723))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (740:740:740) (759:759:759))
        (PORT asdata (1021:1021:1021) (1128:1128:1128))
        (PORT clrn (751:751:751) (723:723:723))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (404:404:404) (472:472:472))
        (PORT d[1] (388:388:388) (465:465:465))
        (PORT d[2] (412:412:412) (493:493:493))
        (PORT d[3] (1105:1105:1105) (1278:1278:1278))
        (PORT d[4] (577:577:577) (679:679:679))
        (PORT d[5] (571:571:571) (677:677:677))
        (PORT d[6] (604:604:604) (717:717:717))
        (PORT d[7] (582:582:582) (685:685:685))
        (PORT d[8] (389:389:389) (465:465:465))
        (PORT d[9] (395:395:395) (471:471:471))
        (PORT d[10] (1072:1072:1072) (1239:1239:1239))
        (PORT d[11] (574:574:574) (672:672:672))
        (PORT clk (939:939:939) (971:971:971))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (939:939:939) (971:971:971))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (940:940:940) (972:972:972))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (921:921:921) (952:952:952))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (461:461:461) (484:484:484))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (462:462:462) (485:485:485))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (462:462:462) (485:485:485))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (462:462:462) (485:485:485))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (733:733:733) (843:843:843))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (772:772:772))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (764:764:764) (735:735:735))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (714:714:714) (816:816:816))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (772:772:772))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (764:764:764) (735:735:735))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (799:799:799) (945:945:945))
        (PORT d[1] (779:779:779) (915:915:915))
        (PORT d[2] (775:775:775) (905:905:905))
        (PORT d[3] (731:731:731) (859:859:859))
        (PORT d[4] (613:613:613) (718:718:718))
        (PORT d[5] (615:615:615) (726:726:726))
        (PORT d[6] (1101:1101:1101) (1273:1273:1273))
        (PORT d[7] (611:611:611) (716:716:716))
        (PORT d[8] (608:608:608) (713:713:713))
        (PORT d[9] (627:627:627) (743:743:743))
        (PORT d[10] (739:739:739) (861:861:861))
        (PORT d[11] (644:644:644) (762:762:762))
        (PORT clk (940:940:940) (973:973:973))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (940:940:940) (973:973:973))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (974:974:974))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (922:922:922) (954:954:954))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (462:462:462) (486:486:486))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (570:570:570) (660:660:660))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (724:724:724))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (571:571:571) (661:661:661))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (724:724:724))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (577:577:577) (685:685:685))
        (PORT d[1] (580:580:580) (686:686:686))
        (PORT d[2] (592:592:592) (694:694:694))
        (PORT d[3] (1094:1094:1094) (1274:1274:1274))
        (PORT d[4] (426:426:426) (509:509:509))
        (PORT d[5] (601:601:601) (716:716:716))
        (PORT d[6] (1267:1267:1267) (1468:1468:1468))
        (PORT d[7] (425:425:425) (507:507:507))
        (PORT d[8] (584:584:584) (689:689:689))
        (PORT d[9] (448:448:448) (542:542:542))
        (PORT d[10] (903:903:903) (1049:1049:1049))
        (PORT d[11] (445:445:445) (533:533:533))
        (PORT clk (941:941:941) (973:973:973))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (973:973:973))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (942:942:942) (974:974:974))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (923:923:923) (954:954:954))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (487:487:487))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (844:844:844) (954:954:954))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (745:745:745) (764:764:764))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (756:756:756) (728:728:728))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (841:841:841) (945:945:945))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (745:745:745) (764:764:764))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (756:756:756) (728:728:728))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (121:121:121) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (124:124:124) (163:163:163))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (161:161:161))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT asdata (301:301:301) (344:344:344))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (123:123:123) (162:162:162))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT asdata (298:298:298) (340:340:340))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (132:132:132) (170:170:170))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (761:761:761))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (725:725:725))
        (PORT ena (705:705:705) (769:769:769))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (121:121:121) (159:159:159))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (750:750:750) (721:721:721))
        (PORT ena (998:998:998) (1121:1121:1121))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (121:121:121) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (750:750:750) (721:721:721))
        (PORT ena (998:998:998) (1121:1121:1121))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (161:161:161))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (750:750:750) (721:721:721))
        (PORT ena (998:998:998) (1121:1121:1121))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (122:122:122) (160:160:160))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (738:738:738) (758:758:758))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (750:750:750) (721:721:721))
        (PORT ena (998:998:998) (1121:1121:1121))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (356:356:356) (421:421:421))
        (PORT dataa[1] (342:342:342) (403:403:403))
        (PORT dataa[2] (299:299:299) (351:351:351))
        (PORT dataa[3] (299:299:299) (353:353:353))
        (PORT dataa[4] (345:345:345) (406:406:406))
        (PORT dataa[5] (296:296:296) (346:346:346))
        (PORT dataa[6] (341:341:341) (403:403:403))
        (PORT dataa[7] (299:299:299) (339:339:339))
        (PORT dataa[8] (514:514:514) (589:589:589))
        (PORT dataa[9] (509:509:509) (586:586:586))
        (PORT dataa[10] (348:348:348) (410:410:410))
        (PORT dataa[11] (357:357:357) (422:422:422))
        (PORT dataa[12] (296:296:296) (348:348:348))
        (PORT dataa[13] (423:423:423) (362:362:362))
        (PORT dataa[14] (449:449:449) (384:384:384))
        (PORT dataa[15] (453:453:453) (386:386:386))
        (PORT dataa[16] (423:423:423) (362:362:362))
        (PORT dataa[17] (449:449:449) (384:384:384))
        (PORT datab[6] (350:350:350) (416:416:416))
        (PORT datab[7] (355:355:355) (420:420:420))
        (PORT datab[8] (352:352:352) (416:416:416))
        (PORT datab[9] (470:470:470) (544:544:544))
        (PORT datab[10] (355:355:355) (411:411:411))
        (PORT datab[11] (336:336:336) (388:388:388))
        (PORT datab[12] (355:355:355) (413:413:413))
        (PORT datab[13] (348:348:348) (397:397:397))
        (PORT datab[14] (307:307:307) (358:358:358))
        (PORT datab[15] (201:201:201) (246:246:246))
        (PORT datab[16] (246:246:246) (202:202:202))
        (PORT datab[17] (231:231:231) (191:191:191))
        (IOPATH dataa dataout (1719:1719:1719) (1719:1719:1719))
        (IOPATH datab dataout (1676:1676:1676) (1676:1676:1676))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (61:61:61) (64:64:64))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (193:193:193) (236:236:236))
        (PORT dataa[7] (209:209:209) (256:256:256))
        (PORT dataa[8] (195:195:195) (237:237:237))
        (PORT dataa[9] (211:211:211) (250:250:250))
        (PORT dataa[10] (206:206:206) (251:251:251))
        (PORT dataa[11] (206:206:206) (251:251:251))
        (PORT dataa[12] (194:194:194) (237:237:237))
        (PORT dataa[13] (203:203:203) (242:242:242))
        (PORT dataa[14] (316:316:316) (370:370:370))
        (PORT dataa[15] (357:357:357) (421:421:421))
        (PORT dataa[16] (407:407:407) (351:351:351))
        (PORT dataa[17] (413:413:413) (349:349:349))
        (PORT datab[10] (368:368:368) (324:324:324))
        (PORT datab[11] (276:276:276) (231:231:231))
        (PORT datab[12] (280:280:280) (233:233:233))
        (PORT datab[13] (368:368:368) (324:324:324))
        (PORT datab[14] (276:276:276) (231:231:231))
        (PORT datab[15] (280:280:280) (233:233:233))
        (PORT datab[16] (368:368:368) (324:324:324))
        (PORT datab[17] (276:276:276) (231:231:231))
        (IOPATH dataa dataout (1719:1719:1719) (1719:1719:1719))
        (IOPATH datab dataout (1676:1676:1676) (1676:1676:1676))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (61:61:61) (64:64:64))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (168:168:168) (204:204:204))
        (PORT datab (434:434:434) (504:504:504))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (308:308:308) (365:365:365))
        (PORT datab (246:246:246) (292:292:292))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (265:265:265) (313:313:313))
        (PORT datab (292:292:292) (344:344:344))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (295:295:295) (348:348:348))
        (PORT datab (301:301:301) (361:361:361))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (286:286:286) (337:337:337))
        (PORT datab (310:310:310) (368:368:368))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (294:294:294) (348:348:348))
        (PORT datab (292:292:292) (345:345:345))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (311:311:311) (367:367:367))
        (PORT datab (285:285:285) (335:335:335))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (282:282:282) (330:330:330))
        (PORT datad (290:290:290) (337:337:337))
        (IOPATH datab combout (196:196:196) (205:205:205))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_valid)
    (DELAY
      (ABSOLUTE
        (PORT clk (741:741:741) (760:760:760))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (752:752:752) (724:724:724))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
)
