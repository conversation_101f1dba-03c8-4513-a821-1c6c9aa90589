module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	// SPI从机接口
	input  wire				 spi_sclk	,
	input  wire				 spi_mosi	,
	input  wire				 spi_cs_n	,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA,
	// 调试输出（可选，用于示波器观察）
	output wire				 debug_spi_valid,
	output wire [3:0]		 debug_freq_sel,
	output wire [11:0]		 debug_amplitude
);

// DAC904 digital to analog converter interface

wire	CLK_20M;

// SPI接收参数
reg [3:0]  spi_cnt = 4'd0;
reg [15:0] spi_data = 16'h1C00; // 默认：档位1(10KHz)，幅度75%
reg        sclk_d = 1'b0;

// 频率档位查找表 - 使用case语句实现
reg [31:0] freq_lut_reg;
always @(*) begin
	case (freq_sel)
		4'd0:  freq_lut_reg = 32'd214748;    // 1KHz
		4'd1:  freq_lut_reg = 32'd2147483;   // 10KHz
		4'd2:  freq_lut_reg = 32'd21474836;  // 100KHz
		4'd3:  freq_lut_reg = 32'd214748364; // 1MHz
		4'd4:  freq_lut_reg = 32'd322122547; // 1.5MHz
		4'd5:  freq_lut_reg = 32'd429496729; // 2MHz
		4'd6:  freq_lut_reg = 32'd536870912; // 2.5MHz
		4'd7:  freq_lut_reg = 32'd644245094; // 3MHz
		4'd8:  freq_lut_reg = 32'd751619277; // 3.5MHz
		4'd9:  freq_lut_reg = 32'd858993459; // 4MHz
		4'd10: freq_lut_reg = 32'd966367641; // 4.5MHz
		4'd11: freq_lut_reg = 32'd1073741824; // 5MHz
		4'd12: freq_lut_reg = 32'd1181116006; // 5.5MHz
		4'd13: freq_lut_reg = 32'd1288490189; // 6MHz
		4'd14: freq_lut_reg = 32'd1395864371; // 6.5MHz
		4'd15: freq_lut_reg = 32'd1503238553; // 7MHz
		default: freq_lut_reg = 32'd214748;  // 默认1KHz
	endcase
end

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;

// SPI接收逻辑 - 超简单可靠版本
reg [15:0] spi_shift_reg = 16'h1C00;
reg [4:0]  spi_bit_cnt = 5'd0;  // 用5位计数到16
reg        spi_cs_n_d1 = 1'b1;
reg        spi_cs_n_d2 = 1'b1;
reg        spi_data_valid = 1'b0;

// SPI时钟域：数据移位和位计数
always @(posedge spi_sclk or posedge spi_cs_n) begin
	if (spi_cs_n) begin
		// CS释放时复位
		spi_bit_cnt <= 5'd0;
		spi_shift_reg <= 16'h0000;  // 清零移位寄存器！
	end
	else begin
		// CS有效时移位和计数
		spi_shift_reg <= {spi_shift_reg[14:0], spi_mosi};
		spi_bit_cnt <= spi_bit_cnt + 1'b1;
	end
end

// 系统时钟域：数据锁存
always @(posedge CLK_20M or negedge SYS_RST) begin
	if (!SYS_RST) begin
		spi_cs_n_d1 <= 1'b1;
		spi_cs_n_d2 <= 1'b1;
		spi_data <= 16'h1C00;
		spi_data_valid <= 1'b0;
	end
	else begin
		spi_cs_n_d1 <= spi_cs_n;
		spi_cs_n_d2 <= spi_cs_n_d1;

		// 检测CS上升沿（传输结束）
		if (spi_cs_n_d1 && !spi_cs_n_d2) begin
			// 只有接收到16位数据才锁存
			if (spi_bit_cnt == 5'd16 || spi_bit_cnt == 5'd0) begin  // 16位或者刚复位
				spi_data <= spi_shift_reg;
				spi_data_valid <= 1'b1;
			end
			else begin
				spi_data_valid <= 1'b0;  // 位数不对，标记无效
			end
		end
		else begin
			spi_data_valid <= 1'b0;
		end
	end
end

// 从SPI数据中提取参数
wire [3:0]  freq_sel = spi_data[15:12];  // 频率档位选择
wire [11:0] amplitude = spi_data[11:0];  // 幅度控制(0-4095)

// 调试信号输出
assign debug_spi_valid = spi_data_valid;
assign debug_freq_sel = freq_sel;
assign debug_amplitude = amplitude;

// 根据档位选择频率控制字
assign fre_k = freq_lut_reg;

// Square wave generation
assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;

// PLL clock generation module
PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_20M	)
);

// Sine wave ROM lookup table
ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_z		)
);

// Triangle wave ROM lookup table
ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_s		)
);

// 32-bit frequency accumulator
add_32bit u_add_32bit(
	.clk	(CLK_20M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);

// 移除key_con模块，改为固定波形选择
assign sel = 2'b00; // 固定选择正弦波

// Waveform selection module
sel_wave u_sel_wave(
	.clk		(CLK_20M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_20M;

// SPI控制的幅度调节
localparam [13:0] DC_CENTER = 14'd8192;  // 14位中心值

// 幅度控制：使用SPI接收的12位幅度值
wire [25:0] scaled_temp;
wire [13:0] scaled_amplitude;

// 幅度缩放：(波形数据 - DC中心) × 幅度系数 / 4096 + DC中心
assign scaled_temp = (DATA_BUF - DC_CENTER) * amplitude;  // 14位 × 12位 = 26位
assign scaled_amplitude = scaled_temp[25:12];             // 取高14位
assign DAC_DATA = scaled_amplitude + DC_CENTER;           // 恢复DC偏置

// Alternative method (commented out)
// assign DAC_DATA = 14'd10000; // Fixed DC output for testing

endmodule