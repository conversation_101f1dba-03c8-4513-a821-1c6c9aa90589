module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	// SPI从机接口
	input  wire				 spi_sclk	,
	input  wire				 spi_mosi	,
	input  wire				 spi_cs_n	,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA,
	// 调试输出（可选，用于示波器观察）
	output wire				 debug_spi_valid,
	output wire [3:0]		 debug_freq_sel,
	output wire [11:0]		 debug_amplitude
);

// DAC904 digital to analog converter interface

wire	CLK_20M;

// SPI接收参数 - 完全重写版本
reg [15:0] spi_data = 16'h1C00; // 默认：档位1(10KHz)，幅度75%

// 频率档位查找表 - 使用case语句实现
reg [31:0] freq_lut_reg;
always @(*) begin
	case (freq_sel)
		4'd0:  freq_lut_reg = 32'd214748;    // 1KHz
		4'd1:  freq_lut_reg = 32'd2147483;   // 10KHz
		4'd2:  freq_lut_reg = 32'd21474836;  // 100KHz
		4'd3:  freq_lut_reg = 32'd214748364; // 1MHz
		4'd4:  freq_lut_reg = 32'd322122547; // 1.5MHz
		4'd5:  freq_lut_reg = 32'd429496729; // 2MHz
		4'd6:  freq_lut_reg = 32'd536870912; // 2.5MHz
		4'd7:  freq_lut_reg = 32'd644245094; // 3MHz
		4'd8:  freq_lut_reg = 32'd751619277; // 3.5MHz
		4'd9:  freq_lut_reg = 32'd858993459; // 4MHz
		4'd10: freq_lut_reg = 32'd966367641; // 4.5MHz
		4'd11: freq_lut_reg = 32'd1073741824; // 5MHz
		4'd12: freq_lut_reg = 32'd1181116006; // 5.5MHz
		4'd13: freq_lut_reg = 32'd1288490189; // 6MHz
		4'd14: freq_lut_reg = 32'd1395864371; // 6.5MHz
		4'd15: freq_lut_reg = 32'd1503238553; // 7MHz
		default: freq_lut_reg = 32'd214748;  // 默认1KHz
	endcase
end

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;

// SPI接收逻辑 - 双级同步器 + 完整帧锁存
// SPI信号双级同步器（消除亚稳态）
reg [1:0] sclk_sync = 2'b00;
reg [1:0] mosi_sync = 2'b00;
reg [1:0] cs_n_sync = 2'b11;

always @(posedge CLK_20M or negedge SYS_RST) begin
	if (!SYS_RST) begin
		sclk_sync <= 2'b00;
		mosi_sync <= 2'b00;
		cs_n_sync <= 2'b11;
	end
	else begin
		// 双级同步器
		sclk_sync <= {sclk_sync[0], spi_sclk};
		mosi_sync <= {mosi_sync[0], spi_mosi};
		cs_n_sync <= {cs_n_sync[0], spi_cs_n};
	end
end

// 同步后的信号
wire sclk_synced = sclk_sync[1];
wire mosi_synced = mosi_sync[1];
wire cs_n_synced = cs_n_sync[1];

// SPI接收寄存器
reg [15:0] spi_shift = 16'h0000;
reg [3:0]  spi_cnt = 4'd0;
reg [15:0] spi_data_reg = 16'h1C00;  // 默认：10kHz，75%幅度
reg        sclk_d1 = 1'b0;

// SPI数据接收逻辑（在系统时钟域）
always @(posedge CLK_20M or negedge SYS_RST) begin
	if (!SYS_RST) begin
		spi_shift <= 16'h0000;
		spi_cnt <= 4'd0;
		spi_data_reg <= 16'h1C00;
		sclk_d1 <= 1'b0;
	end
	else begin
		sclk_d1 <= sclk_synced;

		if (!cs_n_synced) begin
			// CS有效期间：检测SCLK上升沿
			if (sclk_synced && !sclk_d1) begin
				// SCLK上升沿：移入一位数据
				spi_shift <= {spi_shift[14:0], mosi_synced};
				spi_cnt <= spi_cnt + 1'b1;

				// 接收满16位：锁存完整帧
				if (spi_cnt == 4'd15) begin
					spi_data_reg <= {spi_shift[14:0], mosi_synced};
					spi_cnt <= 4'd0;
				end
			end
		end
		else begin
			// CS释放：复位计数器，但保持数据
			spi_cnt <= 4'd0;
		end
	end
end

// 从锁存的SPI数据中提取参数（避免传输过程中的错误解析）
wire [3:0]  freq_sel = spi_data_reg[15:12];  // 频率档位选择
wire [11:0] amplitude = spi_data_reg[11:0];  // 幅度控制(0-4095)

// 调试信号输出
assign debug_spi_valid = (spi_cnt == 4'd0) && !cs_n_synced;  // 传输完成标志
assign debug_freq_sel = freq_sel;
assign debug_amplitude = amplitude;

// 根据档位选择频率控制字
assign fre_k = freq_lut_reg;

// Square wave generation
assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;

// PLL clock generation module
PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_20M	)
);

// Sine wave ROM lookup table
ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_z		)
);

// Triangle wave ROM lookup table
ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_s		)
);

// 32-bit frequency accumulator
add_32bit u_add_32bit(
	.clk	(CLK_20M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);

// 移除key_con模块，改为固定波形选择
assign sel = 2'b00; // 固定选择正弦波

// Waveform selection module
sel_wave u_sel_wave(
	.clk		(CLK_20M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_20M;

// SPI控制的幅度调节 - 带符号处理和饱和运算
localparam [13:0] DC_CENTER = 14'd8192;  // 14位中心值 (2^13)
localparam [13:0] DAC_MAX = 14'd16383;   // 14位最大值 (2^14-1)
localparam [13:0] DAC_MIN = 14'd0;       // 14位最小值

// 符号扩展的幅度缩放
wire signed [14:0] wave_centered;        // 15位有符号数
wire signed [26:0] scaled_temp_signed;   // 27位有符号乘法结果
wire signed [14:0] scaled_result;        // 15位有符号缩放结果
wire [13:0] dac_output_temp;

// 步骤1：波形数据去中心化（转为有符号数）
assign wave_centered = {1'b0, DATA_BUF} - {1'b0, DC_CENTER};

// 步骤2：有符号乘法缩放
assign scaled_temp_signed = wave_centered * $signed({1'b0, amplitude});

// 步骤3：除以4096并恢复中心偏置
assign scaled_result = (scaled_temp_signed >>> 12) + $signed({1'b0, DC_CENTER});

// 步骤4：饱和运算（防止溢出）
assign dac_output_temp = (scaled_result > $signed({1'b0, DAC_MAX})) ? DAC_MAX :
                        (scaled_result < $signed({1'b0, DAC_MIN})) ? DAC_MIN :
                        scaled_result[13:0];

assign DAC_DATA = dac_output_temp;

endmodule