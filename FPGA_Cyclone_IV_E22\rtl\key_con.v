module key_con (
	input 	wire 					clk			, 
	input 	wire 					rst_n		,
	input 	wire 					key1_in	,
	input 	wire 					key2_in	,
	input		wire					key3_in	,
	output  wire	[1:0] 	sel_wave,
	output  wire	[31:0] 	fre_k
);

reg  [31:0] fre;
reg  [1:0]	cnt;
reg  [1:0]  sel;

wire key1_out;
wire key2_out;
wire key3_out;

wire freq_sel;

// delay 
key_delay u_key1_delay(
	.clk		(clk			),
	.kin		(key1_in	),
	.key_out(key1_out	)
);

// delay 
key_delay u_key2_delay(
	.clk		(clk			),
	.kin		(key2_in	),
	.key_out(key2_out	)
);

// delay 
key_delay u_key3_delay(
	.clk		(clk			),
	.kin		(key3_in	),
	.key_out(key3_out	)
);

assign freq_sel = (~key1_out) | (~key3_out);

// Frequency adjustment (based on 20MHz clock)
always @(posedge freq_sel or negedge rst_n)
begin
	if(!rst_n)begin
		fre <= 32'd214748; //1Khz @ 20MHz
	end
	else if(key1_out)begin
		if(fre < 32'd2147483)					// 10Khz
			fre <= fre + 32'd214748;			// Step 1KHz
		else if(fre < 32'd21474836)		// 100KHz
			fre <= fre + 32'd2147483;		// Step 10KHz
		else if(fre < 32'd214748364)	// 1Mhz
			fre <= fre + 32'd21474836;	// Step 100KHz
		else if(fre < 32'd322122547) 	// 1.5MHz (max output frequency)
			fre <= fre + 32'd214748364; // Step 1MHz
		else
			fre <= 32'd214748;
	end
	else if(key3_out)begin
		if(fre > 32'd214748364)				// 1Mhz
			fre <= fre - 32'd214748364; // Step 1MHz
		else if(fre > 32'd21474836)		// 100Khz
			fre <= fre - 32'd21474836; 	// Step 100KHz
		else if(fre > 32'd2147483)		// 10KHz
			fre <= fre - 32'd2147483;		// Step 10KHz
		else if(fre > 32'd214748)			// 1Khz
			fre <= fre - 32'd214748;		// Step 1KHz
		else
			fre <= 32'd214748;
	end
	else begin
		fre <= fre;
	end
end

// Output waveform selection
always @(negedge key2_out or negedge rst_n) 
begin 
	if(!rst_n)begin
		cnt <= 2'd0;
	end
	else if(cnt < 3) begin
		cnt <= cnt + 1'b1;
	end
	else begin 
		cnt <= 2'd0;
	end 
end

always @(posedge clk) begin 
	case (cnt) 
		2'b00 : sel <= 2'b00;
		2'b01 : sel <= 2'b01;
		2'b10 : sel <= 2'b10;
		default : sel <= 2'b00;
	endcase 
end 

assign  fre_k = fre;
assign  sel_wave = sel;

endmodule 