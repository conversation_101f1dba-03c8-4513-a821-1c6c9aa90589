/*
 * 超简化版本 - 如果你觉得上面的代码太复杂
 * 这个版本只有核心功能，更容易理解和调试
 */

#include "ti_msp_dl_config.h"

// 简化的SPI发送函数
void send_to_fpga(uint8_t freq_level, uint8_t amplitude_percent) {
    // 计算16位数据
    uint16_t amplitude = (amplitude_percent * 4095) / 100;  // 转换为12位
    uint16_t data = (freq_level << 12) | amplitude;
    
    // 发送SPI数据
    DL_GPIO_clearPins(GPIOA, DL_GPIO_PIN_4);  // CS = 0
    DL_SPI_transmitData16(SPI0, data);        // 发送16位
    while(DL_SPI_isBusy(SPI0));               // 等待完成
    DL_GPIO_setPins(GPIOA, DL_GPIO_PIN_4);    // CS = 1
}

int main(void) {
    SYSCFG_DL_init();  // 系统初始化（需要用SysConfig工具配置）
    
    while(1) {
        send_to_fpga(0, 25);   // 1KHz, 25%
        DL_Common_delayCycles(1000000);
        
        send_to_fpga(1, 50);   // 10KHz, 50%  
        DL_Common_delayCycles(1000000);
        
        send_to_fpga(2, 75);   // 100KHz, 75%
        DL_Common_delayCycles(1000000);
        
        send_to_fpga(3, 100);  // 1MHz, 100%
        DL_Common_delayCycles(1000000);
    }
}
