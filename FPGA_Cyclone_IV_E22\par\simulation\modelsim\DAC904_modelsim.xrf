vendor_name = ModelSim
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/sel_wave.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/key_delay.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/key_con.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.qip
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/doc/SDC1.sdc
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/DAC904.cbx.xml
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/aglobal131.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/stratix_pll.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/stratixii_pll.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/cycloneii_pll.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/cbx.lst
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/stratix_ram_block.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/lpm_mux.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/lpm_decode.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/a_rdenreg.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/altrom.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/altram.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/altdpram.inc
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_aj91.tdf
source_file = 1, sin_wave.mif
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf
source_file = 1, tri_wave.mif
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/lpm_mult.tdf
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/lpm_add_sub.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/multcore.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/bypassff.inc
source_file = 1, g:/altera/13.1/quartus/libraries/megafunctions/altshift.inc
source_file = 1, C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/mult_9dt.tdf
design_name = DAC904_TOP
instance = comp, \PD~output , PD~output, DAC904_TOP, 1
instance = comp, \DAC_CLK~output , DAC_CLK~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[0]~output , DAC_DATA[0]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[1]~output , DAC_DATA[1]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[2]~output , DAC_DATA[2]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[3]~output , DAC_DATA[3]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[4]~output , DAC_DATA[4]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[5]~output , DAC_DATA[5]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[6]~output , DAC_DATA[6]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[7]~output , DAC_DATA[7]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[8]~output , DAC_DATA[8]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[9]~output , DAC_DATA[9]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[10]~output , DAC_DATA[10]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[11]~output , DAC_DATA[11]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[12]~output , DAC_DATA[12]~output, DAC904_TOP, 1
instance = comp, \DAC_DATA[13]~output , DAC_DATA[13]~output, DAC904_TOP, 1
instance = comp, \debug_spi_valid~output , debug_spi_valid~output, DAC904_TOP, 1
instance = comp, \debug_freq_sel[0]~output , debug_freq_sel[0]~output, DAC904_TOP, 1
instance = comp, \debug_freq_sel[1]~output , debug_freq_sel[1]~output, DAC904_TOP, 1
instance = comp, \debug_freq_sel[2]~output , debug_freq_sel[2]~output, DAC904_TOP, 1
instance = comp, \debug_freq_sel[3]~output , debug_freq_sel[3]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[0]~output , debug_amplitude[0]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[1]~output , debug_amplitude[1]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[2]~output , debug_amplitude[2]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[3]~output , debug_amplitude[3]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[4]~output , debug_amplitude[4]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[5]~output , debug_amplitude[5]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[6]~output , debug_amplitude[6]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[7]~output , debug_amplitude[7]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[8]~output , debug_amplitude[8]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[9]~output , debug_amplitude[9]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[10]~output , debug_amplitude[10]~output, DAC904_TOP, 1
instance = comp, \debug_amplitude[11]~output , debug_amplitude[11]~output, DAC904_TOP, 1
instance = comp, \SYS_RST~input , SYS_RST~input, DAC904_TOP, 1
instance = comp, \SYS_RST~inputclkctrl , SYS_RST~inputclkctrl, DAC904_TOP, 1
instance = comp, \SYS_CLK~input , SYS_CLK~input, DAC904_TOP, 1
instance = comp, \u_PLL_CLK|altpll_component|auto_generated|pll1 , u_PLL_CLK|altpll_component|auto_generated|pll1, DAC904_TOP, 1
instance = comp, \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl , u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl, DAC904_TOP, 1
instance = comp, \spi_sclk~input , spi_sclk~input, DAC904_TOP, 1
instance = comp, \spi_sclk~inputclkctrl , spi_sclk~inputclkctrl, DAC904_TOP, 1
instance = comp, \spi_mosi~input , spi_mosi~input, DAC904_TOP, 1
instance = comp, \spi_cs_n~input , spi_cs_n~input, DAC904_TOP, 1
instance = comp, \spi_shift_reg[0] , spi_shift_reg[0], DAC904_TOP, 1
instance = comp, \spi_shift_reg[1]~feeder , spi_shift_reg[1]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[1] , spi_shift_reg[1], DAC904_TOP, 1
instance = comp, \spi_shift_reg[2]~feeder , spi_shift_reg[2]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[2] , spi_shift_reg[2], DAC904_TOP, 1
instance = comp, \spi_shift_reg[3]~feeder , spi_shift_reg[3]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[3] , spi_shift_reg[3], DAC904_TOP, 1
instance = comp, \spi_shift_reg[4]~feeder , spi_shift_reg[4]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[4] , spi_shift_reg[4], DAC904_TOP, 1
instance = comp, \spi_shift_reg[5] , spi_shift_reg[5], DAC904_TOP, 1
instance = comp, \spi_shift_reg[6]~feeder , spi_shift_reg[6]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[6] , spi_shift_reg[6], DAC904_TOP, 1
instance = comp, \spi_shift_reg[7] , spi_shift_reg[7], DAC904_TOP, 1
instance = comp, \spi_shift_reg[8] , spi_shift_reg[8], DAC904_TOP, 1
instance = comp, \spi_shift_reg[9]~feeder , spi_shift_reg[9]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[9] , spi_shift_reg[9], DAC904_TOP, 1
instance = comp, \spi_shift_reg[10]~1 , spi_shift_reg[10]~1, DAC904_TOP, 1
instance = comp, \spi_shift_reg[10] , spi_shift_reg[10], DAC904_TOP, 1
instance = comp, \spi_shift_reg[11]~feeder , spi_shift_reg[11]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[11] , spi_shift_reg[11], DAC904_TOP, 1
instance = comp, \spi_shift_reg[12]~feeder , spi_shift_reg[12]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[12] , spi_shift_reg[12], DAC904_TOP, 1
instance = comp, \spi_shift_reg[13]~0 , spi_shift_reg[13]~0, DAC904_TOP, 1
instance = comp, \spi_shift_reg[13] , spi_shift_reg[13], DAC904_TOP, 1
instance = comp, \spi_cs_n_d1~0 , spi_cs_n_d1~0, DAC904_TOP, 1
instance = comp, \spi_bit_cnt[0]~3 , spi_bit_cnt[0]~3, DAC904_TOP, 1
instance = comp, \spi_bit_cnt[0] , spi_bit_cnt[0], DAC904_TOP, 1
instance = comp, \spi_bit_cnt[1]~2 , spi_bit_cnt[1]~2, DAC904_TOP, 1
instance = comp, \spi_bit_cnt[1] , spi_bit_cnt[1], DAC904_TOP, 1
instance = comp, \spi_bit_cnt[2]~1 , spi_bit_cnt[2]~1, DAC904_TOP, 1
instance = comp, \spi_bit_cnt[2] , spi_bit_cnt[2], DAC904_TOP, 1
instance = comp, \spi_bit_cnt[3]~0 , spi_bit_cnt[3]~0, DAC904_TOP, 1
instance = comp, \spi_bit_cnt[3] , spi_bit_cnt[3], DAC904_TOP, 1
instance = comp, \always2~0 , always2~0, DAC904_TOP, 1
instance = comp, \always2~1 , always2~1, DAC904_TOP, 1
instance = comp, \spi_data[13] , spi_data[13], DAC904_TOP, 1
instance = comp, \spi_data[12] , spi_data[12], DAC904_TOP, 1
instance = comp, \spi_shift_reg[14]~feeder , spi_shift_reg[14]~feeder, DAC904_TOP, 1
instance = comp, \spi_shift_reg[14] , spi_shift_reg[14], DAC904_TOP, 1
instance = comp, \spi_shift_reg[15] , spi_shift_reg[15], DAC904_TOP, 1
instance = comp, \spi_data[15] , spi_data[15], DAC904_TOP, 1
instance = comp, \spi_data[14] , spi_data[14], DAC904_TOP, 1
instance = comp, \WideOr2~0 , WideOr2~0, DAC904_TOP, 1
instance = comp, \WideOr3~0 , WideOr3~0, DAC904_TOP, 1
instance = comp, \WideOr8~0 , WideOr8~0, DAC904_TOP, 1
instance = comp, \WideOr13~0 , WideOr13~0, DAC904_TOP, 1
instance = comp, \WideOr14~0 , WideOr14~0, DAC904_TOP, 1
instance = comp, \WideOr15~0 , WideOr15~0, DAC904_TOP, 1
instance = comp, \WideOr16~0 , WideOr16~0, DAC904_TOP, 1
instance = comp, \WideOr17~0 , WideOr17~0, DAC904_TOP, 1
instance = comp, \WideOr19~0 , WideOr19~0, DAC904_TOP, 1
instance = comp, \WideOr20~0 , WideOr20~0, DAC904_TOP, 1
instance = comp, \WideOr23~0 , WideOr23~0, DAC904_TOP, 1
instance = comp, \WideOr24~0 , WideOr24~0, DAC904_TOP, 1
instance = comp, \WideOr5~0 , WideOr5~0, DAC904_TOP, 1
instance = comp, \WideOr26~0 , WideOr26~0, DAC904_TOP, 1
instance = comp, \WideOr28~0 , WideOr28~0, DAC904_TOP, 1
instance = comp, \WideOr9~0 , WideOr9~0, DAC904_TOP, 1
instance = comp, \WideOr30~0 , WideOr30~0, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[0]~32 , u_add_32bit|add[0]~32, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[0] , u_add_32bit|add[0], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[1]~34 , u_add_32bit|add[1]~34, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[1] , u_add_32bit|add[1], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[2]~36 , u_add_32bit|add[2]~36, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[2] , u_add_32bit|add[2], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[3]~38 , u_add_32bit|add[3]~38, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[3] , u_add_32bit|add[3], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[4]~40 , u_add_32bit|add[4]~40, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[4] , u_add_32bit|add[4], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[5]~42 , u_add_32bit|add[5]~42, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[5] , u_add_32bit|add[5], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[6]~44 , u_add_32bit|add[6]~44, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[6] , u_add_32bit|add[6], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[7]~46 , u_add_32bit|add[7]~46, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[7] , u_add_32bit|add[7], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[8]~48 , u_add_32bit|add[8]~48, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[8] , u_add_32bit|add[8], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[9]~50 , u_add_32bit|add[9]~50, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[9] , u_add_32bit|add[9], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[10]~52 , u_add_32bit|add[10]~52, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[10] , u_add_32bit|add[10], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[11]~54 , u_add_32bit|add[11]~54, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[11] , u_add_32bit|add[11], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[12]~56 , u_add_32bit|add[12]~56, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[12] , u_add_32bit|add[12], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[13]~58 , u_add_32bit|add[13]~58, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[13] , u_add_32bit|add[13], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[14]~60 , u_add_32bit|add[14]~60, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[14] , u_add_32bit|add[14], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[15]~62 , u_add_32bit|add[15]~62, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[15] , u_add_32bit|add[15], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[16]~64 , u_add_32bit|add[16]~64, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[16] , u_add_32bit|add[16], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[17]~66 , u_add_32bit|add[17]~66, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[17] , u_add_32bit|add[17], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[18]~68 , u_add_32bit|add[18]~68, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[18] , u_add_32bit|add[18], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[19]~70 , u_add_32bit|add[19]~70, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[19] , u_add_32bit|add[19], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[20]~72 , u_add_32bit|add[20]~72, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[20] , u_add_32bit|add[20], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[21]~74 , u_add_32bit|add[21]~74, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[21] , u_add_32bit|add[21], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[22]~76 , u_add_32bit|add[22]~76, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[22] , u_add_32bit|add[22], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[23]~78 , u_add_32bit|add[23]~78, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[23] , u_add_32bit|add[23], DAC904_TOP, 1
instance = comp, \WideOr6~0 , WideOr6~0, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[24]~80 , u_add_32bit|add[24]~80, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[24] , u_add_32bit|add[24], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[25]~82 , u_add_32bit|add[25]~82, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[25] , u_add_32bit|add[25], DAC904_TOP, 1
instance = comp, \WideOr4~0 , WideOr4~0, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[26]~84 , u_add_32bit|add[26]~84, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[26] , u_add_32bit|add[26], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[27]~86 , u_add_32bit|add[27]~86, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[27] , u_add_32bit|add[27], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[28]~88 , u_add_32bit|add[28]~88, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[28] , u_add_32bit|add[28], DAC904_TOP, 1
instance = comp, \WideOr1~0 , WideOr1~0, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[29]~90 , u_add_32bit|add[29]~90, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[29] , u_add_32bit|add[29], DAC904_TOP, 1
instance = comp, \WideOr0~0 , WideOr0~0, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[30]~92 , u_add_32bit|add[30]~92, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[30] , u_add_32bit|add[30], DAC904_TOP, 1
instance = comp, \u_add_32bit|add[31]~94 , u_add_32bit|add[31]~94, DAC904_TOP, 1
instance = comp, \u_add_32bit|add[31] , u_add_32bit|add[31], DAC904_TOP, 1
instance = comp, \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 , u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[0]~feeder , u_sel_wave|da_out_reg[0]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[0] , u_sel_wave|da_out_reg[0], DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[1]~feeder , u_sel_wave|da_out_reg[1]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[1] , u_sel_wave|da_out_reg[1], DAC904_TOP, 1
instance = comp, \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 , u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[2]~feeder , u_sel_wave|da_out_reg[2]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[2] , u_sel_wave|da_out_reg[2], DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[3]~feeder , u_sel_wave|da_out_reg[3]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[3] , u_sel_wave|da_out_reg[3], DAC904_TOP, 1
instance = comp, \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 , u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[4]~feeder , u_sel_wave|da_out_reg[4]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[4] , u_sel_wave|da_out_reg[4], DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[5]~feeder , u_sel_wave|da_out_reg[5]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[5] , u_sel_wave|da_out_reg[5], DAC904_TOP, 1
instance = comp, \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 , u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[6]~feeder , u_sel_wave|da_out_reg[6]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[6] , u_sel_wave|da_out_reg[6], DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[7] , u_sel_wave|da_out_reg[7], DAC904_TOP, 1
instance = comp, \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 , u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[8]~feeder , u_sel_wave|da_out_reg[8]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[8] , u_sel_wave|da_out_reg[8], DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[9]~feeder , u_sel_wave|da_out_reg[9]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[9] , u_sel_wave|da_out_reg[9], DAC904_TOP, 1
instance = comp, \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 , u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[10]~feeder , u_sel_wave|da_out_reg[10]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[10] , u_sel_wave|da_out_reg[10], DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[11]~feeder , u_sel_wave|da_out_reg[11]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[11] , u_sel_wave|da_out_reg[11], DAC904_TOP, 1
instance = comp, \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 , u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[12]~feeder , u_sel_wave|da_out_reg[12]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[12] , u_sel_wave|da_out_reg[12], DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[13]~feeder , u_sel_wave|da_out_reg[13]~feeder, DAC904_TOP, 1
instance = comp, \u_sel_wave|da_out_reg[13] , u_sel_wave|da_out_reg[13], DAC904_TOP, 1
instance = comp, \spi_data[0]~feeder , spi_data[0]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[0] , spi_data[0], DAC904_TOP, 1
instance = comp, \spi_data[1]~feeder , spi_data[1]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[1] , spi_data[1], DAC904_TOP, 1
instance = comp, \spi_data[2]~feeder , spi_data[2]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[2] , spi_data[2], DAC904_TOP, 1
instance = comp, \spi_data[3]~feeder , spi_data[3]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[3] , spi_data[3], DAC904_TOP, 1
instance = comp, \spi_data[4] , spi_data[4], DAC904_TOP, 1
instance = comp, \spi_data[5]~feeder , spi_data[5]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[5] , spi_data[5], DAC904_TOP, 1
instance = comp, \spi_data[6] , spi_data[6], DAC904_TOP, 1
instance = comp, \spi_data[7]~feeder , spi_data[7]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[7] , spi_data[7], DAC904_TOP, 1
instance = comp, \spi_data[8]~feeder , spi_data[8]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[8] , spi_data[8], DAC904_TOP, 1
instance = comp, \spi_data[9]~feeder , spi_data[9]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[9] , spi_data[9], DAC904_TOP, 1
instance = comp, \spi_data[10]~feeder , spi_data[10]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[10] , spi_data[10], DAC904_TOP, 1
instance = comp, \spi_data[11]~feeder , spi_data[11]~feeder, DAC904_TOP, 1
instance = comp, \spi_data[11] , spi_data[11], DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|mac_mult1 , Mult0|auto_generated|mac_mult1, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|mac_out2 , Mult0|auto_generated|mac_out2, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|mac_mult3 , Mult0|auto_generated|mac_mult3, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|mac_out4 , Mult0|auto_generated|mac_out4, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~0 , Mult0|auto_generated|op_1~0, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~2 , Mult0|auto_generated|op_1~2, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~4 , Mult0|auto_generated|op_1~4, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~6 , Mult0|auto_generated|op_1~6, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~8 , Mult0|auto_generated|op_1~8, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~10 , Mult0|auto_generated|op_1~10, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~12 , Mult0|auto_generated|op_1~12, DAC904_TOP, 1
instance = comp, \Mult0|auto_generated|op_1~14 , Mult0|auto_generated|op_1~14, DAC904_TOP, 1
instance = comp, \KEY_IN[0]~input , KEY_IN[0]~input, DAC904_TOP, 1
instance = comp, \KEY_IN[1]~input , KEY_IN[1]~input, DAC904_TOP, 1
instance = comp, \KEY_IN[2]~input , KEY_IN[2]~input, DAC904_TOP, 1
