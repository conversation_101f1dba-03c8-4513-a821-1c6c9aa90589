Assembler report for DAC904
Fri Aug 01 10:31:20 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Assembler Summary
  3. Assembler Settings
  4. Assembler Generated Files
  5. Assembler Device Options: C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/output_files/DAC904.sof
  6. Assembler Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+---------------------------------------------------------------+
; Assembler Summary                                             ;
+-----------------------+---------------------------------------+
; Assembler Status      ; Successful - Fri Aug 01 10:31:20 2025 ;
; Revision Name         ; DAC904                                ;
; Top-level Entity Name ; DAC904_TOP                            ;
; Family                ; Cyclone IV E                          ;
; Device                ; EP4CE6E22C8                           ;
+-----------------------+---------------------------------------+


+--------------------------------------------------------------------------------------------------------+
; Assembler Settings                                                                                     ;
+-----------------------------------------------------------------------------+----------+---------------+
; Option                                                                      ; Setting  ; Default Value ;
+-----------------------------------------------------------------------------+----------+---------------+
; Use smart compilation                                                       ; Off      ; Off           ;
; Enable parallel Assembler and TimeQuest Timing Analyzer during compilation  ; On       ; On            ;
; Enable compact report table                                                 ; Off      ; Off           ;
; Generate compressed bitstreams                                              ; On       ; On            ;
; Compression mode                                                            ; Off      ; Off           ;
; Clock source for configuration device                                       ; Internal ; Internal      ;
; Clock frequency of the configuration device                                 ; 10 MHZ   ; 10 MHz        ;
; Divide clock frequency by                                                   ; 1        ; 1             ;
; Auto user code                                                              ; On       ; On            ;
; Use configuration device                                                    ; Off      ; Off           ;
; Configuration device                                                        ; Auto     ; Auto          ;
; Configuration device auto user code                                         ; Off      ; Off           ;
; Generate Tabular Text File (.ttf) For Target Device                         ; Off      ; Off           ;
; Generate Raw Binary File (.rbf) For Target Device                           ; Off      ; Off           ;
; Generate Hexadecimal (Intel-Format) Output File (.hexout) for Target Device ; Off      ; Off           ;
; Hexadecimal Output File start address                                       ; 0        ; 0             ;
; Hexadecimal Output File count direction                                     ; Up       ; Up            ;
; Release clears before tri-states                                            ; Off      ; Off           ;
; Auto-restart configuration after error                                      ; On       ; On            ;
; Enable OCT_DONE                                                             ; Off      ; Off           ;
; Generate Serial Vector Format File (.svf) for Target Device                 ; Off      ; Off           ;
; Generate a JEDEC STAPL Format File (.jam) for Target Device                 ; Off      ; Off           ;
; Generate a compressed Jam STAPL Byte Code 2.0 File (.jbc) for Target Device ; Off      ; Off           ;
; Generate a compressed Jam STAPL Byte Code 2.0 File (.jbc) for Target Device ; On       ; On            ;
+-----------------------------------------------------------------------------+----------+---------------+


+---------------------------------------------------------------------------------------+
; Assembler Generated Files                                                             ;
+---------------------------------------------------------------------------------------+
; File Name                                                                             ;
+---------------------------------------------------------------------------------------+
; C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/output_files/DAC904.sof ;
+---------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Assembler Device Options: C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/output_files/DAC904.sof ;
+----------------+------------------------------------------------------------------------------------------------+
; Option         ; Setting                                                                                        ;
+----------------+------------------------------------------------------------------------------------------------+
; Device         ; EP4CE6E22C8                                                                                    ;
; JTAG usercode  ; 0x0018B489                                                                                     ;
; Checksum       ; 0x0018B489                                                                                     ;
+----------------+------------------------------------------------------------------------------------------------+


+--------------------+
; Assembler Messages ;
+--------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit Assembler
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Fri Aug 01 10:31:20 2025
Info: Command: quartus_asm --read_settings_files=off --write_settings_files=off DAC904 -c DAC904
Info (115031): Writing out detailed assembly data for power analysis
Info (115030): Assembler is generating device programming files
Info: Quartus II 64-Bit Assembler was successful. 0 errors, 0 warnings
    Info: Peak virtual memory: 4593 megabytes
    Info: Processing ended: Fri Aug 01 10:31:20 2025
    Info: Elapsed time: 00:00:00
    Info: Total CPU time (on all processors): 00:00:00


