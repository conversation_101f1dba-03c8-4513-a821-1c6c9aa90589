# DAC904时钟优化总结

## 修改概述
将DAC904系统时钟从165MHz降低到20MHz，以满足最大输出频率1.5MHz的需求。

## 修改详情

### 1. PLL配置修改
**文件**: `ip_core/PLL/PLL_CLK.v` 和 `par/db/PLL_CLK_altpll.v`

**修改前**:
```verilog
altpll_component.clk0_divide_by = 10,
altpll_component.clk0_multiply_by = 33,
// 输出频率 = 50MHz × (33/10) = 165MHz
```

**修改后**:
```verilog
altpll_component.clk0_divide_by = 5,
altpll_component.clk0_multiply_by = 2,
// 输出频率 = 50MHz × (2/5) = 20MHz
```

### 2. 顶层模块时钟信号修改
**文件**: `rtl/DAC904_TOP.v`

**修改内容**:
- `CLK_165M` → `CLK_20M`
- 所有模块的时钟输入都改为20MHz
- DAC_CLK输出改为20MHz

### 3. 频率控制字重新计算
**文件**: `rtl/key_con.v`

**计算公式**: `频率控制字 = (目标频率 × 2^32) / 时钟频率`

**新的频率控制字** (基于20MHz时钟):
- 1KHz: 214,748
- 10KHz: 2,147,483  
- 100KHz: 21,474,836
- 1MHz: 214,748,364
- 1.5MHz: 322,122,547 (最大输出频率)

### 4. 按键消抖延时调整
**文件**: `rtl/key_delay.v`

**修改前**:
```verilog
localparam del = (165_000_000 / 50) - 1;  // 165MHz时钟
```

**修改后**:
```verilog
localparam del = (20_000_000 / 50) - 1;   // 20MHz时钟
```

## 优化效果

### 性能对比
| 参数 | 修改前 | 修改后 | 改善 |
|------|--------|--------|------|
| DAC时钟频率 | 165MHz | 20MHz | -87.9% |
| 最大输出频率 | 82.5MHz | 10MHz | 满足1.5MHz需求 |
| 功耗 | 高 | 低 | 显著降低 |
| EMI干扰 | 高 | 低 | 显著改善 |

### 技术优势
1. **功耗优化**: 时钟频率降低87.9%，功耗显著下降
2. **EMI改善**: 低频时钟减少电磁干扰
3. **设计简化**: 降低PCB布线难度
4. **成本节约**: 减少高频器件需求
5. **可靠性提升**: 低频设计更稳定

## 验证要点

### 功能验证
- [ ] 按键响应正常
- [ ] 频率调节功能正常
- [ ] 波形切换功能正常
- [ ] 输出信号质量良好

### 频率测试
- [ ] 1KHz输出测试
- [ ] 10KHz输出测试  
- [ ] 100KHz输出测试
- [ ] 1MHz输出测试
- [ ] 1.5MHz输出测试

### 信号质量检查
- [ ] 正弦波失真度
- [ ] 三角波线性度
- [ ] 方波上升/下降时间
- [ ] 频率精度测试

## 注意事项

1. **重新编译**: 修改PLL配置后需要重新编译整个项目
2. **时序约束**: 可能需要更新SDC时序约束文件
3. **仿真验证**: 建议进行功能仿真验证
4. **硬件测试**: 实际硬件测试确认功能正常

## 后续优化建议

1. **幅度控制**: 可以添加数字幅度控制功能
2. **相位控制**: 实现多通道相位可调
3. **任意波形**: 支持用户自定义波形
4. **通信接口**: 添加UART/SPI控制接口
