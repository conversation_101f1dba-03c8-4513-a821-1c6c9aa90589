/*
 * MSPM0G3507 SPI主机控制DAC904 FPGA
 * 功能：通过SPI控制FPGA输出波形的频率和幅度
 *
 * 硬件连接：
 * MSPM0G3507    →    FPGA (Cyclone IV)
 * ─────────────────────────────────────
 * PA6 (SPI_SCLK) →   PIN_23 (spi_sclk)
 * PA5 (SPI_MOSI) →   PIN_25 (spi_mosi)
 * PA4 (SPI_CS)   →   PIN_31 (spi_cs_n)
 * GND           →   GND
 * 3.3V          →   3.3V
 */

#include "ti_msp_dl_config.h"
#include <stdint.h>

// 频率档位定义
typedef enum {
    FREQ_1KHZ = 0,      // 1KHz
    FREQ_10KHZ = 1,     // 10KHz  
    FREQ_100KHZ = 2,    // 100KHz
    FREQ_1MHZ = 3,      // 1MHz
    FREQ_1_5MHZ = 4,    // 1.5MHz
    FREQ_2MHZ = 5,      // 2MHz
    FREQ_2_5MHZ = 6,    // 2.5MHz
    FREQ_3MHZ = 7,      // 3MHz
    FREQ_3_5MHZ = 8,    // 3.5MHz
    FREQ_4MHZ = 9,      // 4MHz
    FREQ_4_5MHZ = 10,   // 4.5MHz
    FREQ_5MHZ = 11,     // 5MHz
    FREQ_5_5MHZ = 12,   // 5.5MHz
    FREQ_6MHZ = 13,     // 6MHz
    FREQ_6_5MHZ = 14,   // 6.5MHz
    FREQ_7MHZ = 15      // 7MHz
} freq_level_t;

// 函数声明
void spi_init(void);
void set_dac_params(freq_level_t freq_level, uint16_t amplitude_percent);
void delay_ms(uint32_t ms);

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // SPI初始化
    spi_init();
    
    // 延时等待系统稳定
    delay_ms(100);
    
    // 主循环演示
    while(1) {
        // 示例1：1KHz, 25%幅度
        set_dac_params(FREQ_1KHZ, 25);
        delay_ms(2000);
        
        // 示例2：10KHz, 50%幅度  
        set_dac_params(FREQ_10KHZ, 50);
        delay_ms(2000);
        
        // 示例3：100KHz, 75%幅度
        set_dac_params(FREQ_100KHZ, 75);
        delay_ms(2000);
        
        // 示例4：1MHz, 100%幅度
        set_dac_params(FREQ_1MHZ, 100);
        delay_ms(2000);
        
        // 示例5：扫频演示
        for(int freq = FREQ_1KHZ; freq <= FREQ_1MHZ; freq++) {
            set_dac_params((freq_level_t)freq, 60);
            delay_ms(500);
        }
    }
}

/**
 * SPI初始化函数
 */
void spi_init(void)
{
    // 配置SPI为主机模式
    DL_SPI_setClockConfig(SPI_0_INST, (DL_SPI_ClockConfig *) &gSPI_0ClockConfig);
    
    // 使能SPI
    DL_SPI_enable(SPI_0_INST);
    
    // 配置CS引脚为高电平（空闲状态）
    DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
}

/**
 * 设置DAC参数
 * @param freq_level: 频率档位 (0-15)
 * @param amplitude_percent: 幅度百分比 (0-100)
 */
void set_dac_params(freq_level_t freq_level, uint16_t amplitude_percent)
{
    // 参数检查
    if(freq_level > FREQ_7MHZ) freq_level = FREQ_1KHZ;
    if(amplitude_percent > 100) amplitude_percent = 100;
    
    // 计算幅度值 (0-100% 映射到 0-4095)
    uint16_t amplitude = (amplitude_percent * 4095) / 100;
    
    // 组合16位数据：[15:12]频率档位 + [11:0]幅度
    uint16_t spi_data = ((uint16_t)freq_level << 12) | (amplitude & 0x0FFF);
    
    // SPI传输
    DL_GPIO_clearPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);  // CS拉低
    
    // 发送16位数据
    DL_SPI_transmitData16(SPI_0_INST, spi_data);
    
    // 等待传输完成
    while(DL_SPI_isBusy(SPI_0_INST));
    
    DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);    // CS拉高
    
    // 短暂延时确保FPGA处理完成
    delay_ms(1);
}

/**
 * 毫秒延时函数
 * @param ms: 延时毫秒数
 */
void delay_ms(uint32_t ms)
{
    // 假设系统时钟32MHz，每毫秒需要32000个时钟周期
    uint32_t cycles = ms * 32000;
    DL_Common_delayCycles(cycles);
}

/**
 * 高级控制函数示例
 */
void advanced_control_demo(void)
{
    // 频率扫描
    for(int i = 0; i < 16; i++) {
        set_dac_params((freq_level_t)i, 50);
        delay_ms(200);
    }
    
    // 幅度扫描
    set_dac_params(FREQ_10KHZ, 0);
    for(int amp = 0; amp <= 100; amp += 5) {
        set_dac_params(FREQ_10KHZ, amp);
        delay_ms(100);
    }
}
