<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\bin\tiarmlnk -IG:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib -o DAC904_spi_controller_command_data_control.out -mDAC904_spi_controller_command_data_control.map -iC:/TI/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/DAC904_spi_controller_command_data_control -iC:/Users/<USER>/workspace_ccstheia/DAC904_spi_controller_command_data_control/Debug/syscfg -iG:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=DAC904_spi_controller_command_data_control_linkInfo.xml --rom_model ./spi_controller_command_data_control.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688c2636</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\DAC904_spi_controller_command_data_control\Debug\DAC904_spi_controller_command_data_control.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x4cd</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\DAC904_spi_controller_command_data_control\Debug\.\</path>
         <kind>object</kind>
         <file>spi_controller_command_data_control.o</file>
         <name>spi_controller_command_data_control.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\DAC904_spi_controller_command_data_control\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\DAC904_spi_controller_command_data_control\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\DAC904_spi_controller_command_data_control\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-29">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d3">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.send_dac_params</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0x184</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x184</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.delay_ms_safe</name>
         <load_address>0x244</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x244</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x2d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d0</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text.SYSCFG_DL_SPI_0_init</name>
         <load_address>0x320</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x320</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.DL_SPI_init</name>
         <load_address>0x370</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x370</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x3b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3b4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x3f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3f8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x438</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x438</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x474</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x474</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x4a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4a4</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x4cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4cc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x4f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4f4</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.DL_SPI_setControllerCommandDataModeConfig</name>
         <load_address>0x518</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x518</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x53c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x53c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x55c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x55c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x57c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x57c</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x59a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x59a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x59c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x59c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text.DL_SPI_enableControllerCommandDataMode</name>
         <load_address>0x5b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x5d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x5f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x608</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x608</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x620</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x620</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x638</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x638</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x650</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x650</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text.DL_GPIO_togglePins</name>
         <load_address>0x668</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x668</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x680</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x680</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x698</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x698</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.DL_SPI_isBusy</name>
         <load_address>0x6b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x6c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x6e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.DL_SPI_transmitData8</name>
         <load_address>0x6f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f8</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x70e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x724</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x724</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x738</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x738</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x74c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x74c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x760</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x760</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x770</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x770</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-87">
         <name>.text:abort</name>
         <load_address>0x77a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x77a</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x780</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x780</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.HOSTexit</name>
         <load_address>0x784</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x784</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x788</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x78c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x78c</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.cinit..bss.load</name>
         <load_address>0x7a0</load_address>
         <readonly>true</readonly>
         <run_address>0x7a0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-13d">
         <name>__TI_handler_table</name>
         <load_address>0x7a8</load_address>
         <readonly>true</readonly>
         <run_address>0x7a8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-13e">
         <name>__TI_cinit_table</name>
         <load_address>0x7ac</load_address>
         <readonly>true</readonly>
         <run_address>0x7ac</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e3">
         <name>.rodata.gSPI_0_config</name>
         <load_address>0x790</load_address>
         <readonly>true</readonly>
         <run_address>0x790</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.rodata.gSPI_0_clockConfig</name>
         <load_address>0x79a</load_address>
         <readonly>true</readonly>
         <run_address>0x79a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-107">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-ab">
         <name>.common:gSPI_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-141">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x151</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_abbrev</name>
         <load_address>0x151</load_address>
         <run_address>0x151</run_address>
         <size>0x1bf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x310</load_address>
         <run_address>0x310</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_abbrev</name>
         <load_address>0x37d</load_address>
         <run_address>0x37d</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_abbrev</name>
         <load_address>0x3df</load_address>
         <run_address>0x3df</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x656</load_address>
         <run_address>0x656</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_abbrev</name>
         <load_address>0x705</load_address>
         <run_address>0x705</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_abbrev</name>
         <load_address>0x875</load_address>
         <run_address>0x875</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x8ae</load_address>
         <run_address>0x8ae</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_abbrev</name>
         <load_address>0x970</load_address>
         <run_address>0x970</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_abbrev</name>
         <load_address>0xa08</load_address>
         <run_address>0xa08</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_abbrev</name>
         <load_address>0xa34</load_address>
         <run_address>0xa34</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_abbrev</name>
         <load_address>0xa5b</load_address>
         <run_address>0xa5b</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_abbrev</name>
         <load_address>0xab4</load_address>
         <run_address>0xab4</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_info</name>
         <load_address>0xdbf</load_address>
         <run_address>0xdbf</run_address>
         <size>0x19ab</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x276a</load_address>
         <run_address>0x276a</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_info</name>
         <load_address>0x27ea</load_address>
         <run_address>0x27ea</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_info</name>
         <load_address>0x285f</load_address>
         <run_address>0x285f</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x39a1</load_address>
         <run_address>0x39a1</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0x3dc4</load_address>
         <run_address>0x3dc4</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x4508</load_address>
         <run_address>0x4508</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_info</name>
         <load_address>0x454e</load_address>
         <run_address>0x454e</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_info</name>
         <load_address>0x46e0</load_address>
         <run_address>0x46e0</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_info</name>
         <load_address>0x47d8</load_address>
         <run_address>0x47d8</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_info</name>
         <load_address>0x4813</load_address>
         <run_address>0x4813</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_info</name>
         <load_address>0x49cf</load_address>
         <run_address>0x49cf</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_info</name>
         <load_address>0x4a54</load_address>
         <run_address>0x4a54</run_address>
         <size>0xca</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_ranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x118</load_address>
         <run_address>0x118</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_ranges</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x4c0</load_address>
         <run_address>0x4c0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_ranges</name>
         <load_address>0x508</load_address>
         <run_address>0x508</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_ranges</name>
         <load_address>0x550</load_address>
         <run_address>0x550</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_ranges</name>
         <load_address>0x568</load_address>
         <run_address>0x568</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_ranges</name>
         <load_address>0x580</load_address>
         <run_address>0x580</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x694</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_str</name>
         <load_address>0x694</load_address>
         <run_address>0x694</run_address>
         <size>0x1647</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x1cdb</load_address>
         <run_address>0x1cdb</run_address>
         <size>0x181</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_str</name>
         <load_address>0x1e5c</load_address>
         <run_address>0x1e5c</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_str</name>
         <load_address>0x1fc9</load_address>
         <run_address>0x1fc9</run_address>
         <size>0xc3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x2c04</load_address>
         <run_address>0x2c04</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_str</name>
         <load_address>0x2e29</load_address>
         <run_address>0x2e29</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_str</name>
         <load_address>0x3158</load_address>
         <run_address>0x3158</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0x324d</load_address>
         <run_address>0x324d</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_str</name>
         <load_address>0x33e8</load_address>
         <run_address>0x33e8</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_str</name>
         <load_address>0x3530</load_address>
         <run_address>0x3530</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_str</name>
         <load_address>0x3619</load_address>
         <run_address>0x3619</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_frame</name>
         <load_address>0xb4</load_address>
         <run_address>0xb4</run_address>
         <size>0x264</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x318</load_address>
         <run_address>0x318</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_frame</name>
         <load_address>0x348</load_address>
         <run_address>0x348</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_frame</name>
         <load_address>0x368</load_address>
         <run_address>0x368</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x59c</load_address>
         <run_address>0x59c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_frame</name>
         <load_address>0x62c</load_address>
         <run_address>0x62c</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_frame</name>
         <load_address>0x72c</load_address>
         <run_address>0x72c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_frame</name>
         <load_address>0x74c</load_address>
         <run_address>0x74c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_frame</name>
         <load_address>0x784</load_address>
         <run_address>0x784</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_frame</name>
         <load_address>0x7b4</load_address>
         <run_address>0x7b4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_frame</name>
         <load_address>0x7d4</load_address>
         <run_address>0x7d4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x495</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_line</name>
         <load_address>0x495</load_address>
         <run_address>0x495</run_address>
         <size>0x6cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0xb64</load_address>
         <run_address>0xb64</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_line</name>
         <load_address>0xc1c</load_address>
         <run_address>0xc1c</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_line</name>
         <load_address>0xd94</load_address>
         <run_address>0xd94</run_address>
         <size>0xc1a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x19ae</load_address>
         <run_address>0x19ae</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_line</name>
         <load_address>0x1b8a</load_address>
         <run_address>0x1b8a</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_line</name>
         <load_address>0x20a4</load_address>
         <run_address>0x20a4</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_line</name>
         <load_address>0x20e2</load_address>
         <run_address>0x20e2</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_line</name>
         <load_address>0x21e0</load_address>
         <run_address>0x21e0</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x2247</load_address>
         <run_address>0x2247</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_line</name>
         <load_address>0x2288</load_address>
         <run_address>0x2288</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_line</name>
         <load_address>0x234a</load_address>
         <run_address>0x234a</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x829</load_address>
         <run_address>0x829</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_loc</name>
         <load_address>0x901</load_address>
         <run_address>0x901</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0xd25</load_address>
         <run_address>0xd25</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_loc</name>
         <load_address>0xe91</load_address>
         <run_address>0xe91</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_loc</name>
         <load_address>0xeb7</load_address>
         <run_address>0xeb7</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x6d0</size>
         <contents>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x7a0</load_address>
         <run_address>0x7a0</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-13e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x790</load_address>
         <run_address>0x790</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-e2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-107"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-ab"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-141"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fe" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ff" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-100" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-101" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-102" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-103" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-105" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-121" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xac3</size>
         <contents>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-143"/>
         </contents>
      </logical_group>
      <logical_group id="lg-123" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4b1e</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-142"/>
         </contents>
      </logical_group>
      <logical_group id="lg-125" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x598</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-e6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-127" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x37ac</size>
         <contents>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-fc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-129" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x804</size>
         <contents>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-e5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12b" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x23ff</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-e8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12d" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xed7</size>
         <contents>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-fd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-137" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-b0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-140" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-147" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7b8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-148" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-149" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x7b8</used_space>
         <unused_space>0x1f848</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x6d0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x790</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x7a0</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x7b8</start_address>
               <size>0x1f848</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x228</used_space>
         <unused_space>0x7dd8</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-103"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-105"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200028</start_address>
               <size>0x7dd8</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x7a0</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x28</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x7ac</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x7b4</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x7b4</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x7a8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x7ac</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-50">
         <name>delay_ms_safe</name>
         <value>0x245</value>
         <object_component_ref idref="oc-81"/>
      </symbol>
      <symbol id="sm-51">
         <name>send_dac_params</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-52">
         <name>main</name>
         <value>0x185</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-b6">
         <name>SYSCFG_DL_init</name>
         <value>0x55d</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-b7">
         <name>SYSCFG_DL_initPower</name>
         <value>0x3b5</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-b8">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x2d1</value>
         <object_component_ref idref="oc-a8"/>
      </symbol>
      <symbol id="sm-b9">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x57d</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-ba">
         <name>SYSCFG_DL_SPI_0_init</name>
         <value>0x321</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-bb">
         <name>gSPI_0Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-c6">
         <name>Default_Handler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c7">
         <name>Reset_Handler</name>
         <value>0x789</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-c8">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-c9">
         <name>NMI_Handler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ca">
         <name>HardFault_Handler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cb">
         <name>SVC_Handler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cc">
         <name>PendSV_Handler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cd">
         <name>SysTick_Handler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ce">
         <name>GROUP0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cf">
         <name>GROUP1_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d0">
         <name>TIMG8_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d1">
         <name>UART3_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d2">
         <name>ADC0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d3">
         <name>ADC1_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d4">
         <name>CANFD0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d5">
         <name>DAC0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d6">
         <name>SPI0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d7">
         <name>SPI1_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d8">
         <name>UART1_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d9">
         <name>UART2_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-da">
         <name>UART0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-db">
         <name>TIMG0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-dc">
         <name>TIMG6_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-dd">
         <name>TIMA0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-de">
         <name>TIMA1_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-df">
         <name>TIMG7_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e0">
         <name>TIMG12_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e1">
         <name>I2C0_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e2">
         <name>I2C1_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e3">
         <name>AES_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e4">
         <name>RTC_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e5">
         <name>DMA_IRQHandler</name>
         <value>0x781</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e6">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e7">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e8">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-e9">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ea">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-eb">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ec">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ed">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ee">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f7">
         <name>DL_Common_delayCycles</name>
         <value>0x771</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-104">
         <name>DL_SPI_init</name>
         <value>0x371</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-105">
         <name>DL_SPI_setClockConfig</name>
         <value>0x74d</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-110">
         <name>_c_int00_noargs</name>
         <value>0x4cd</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-111">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-11d">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x439</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-125">
         <name>_system_pre_init</name>
         <value>0x78d</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-130">
         <name>__TI_zero_init_nomemset</name>
         <value>0x70f</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-13c">
         <name>abort</name>
         <value>0x77b</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-146">
         <name>HOSTexit</name>
         <value>0x785</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-147">
         <name>C$$EXIT</name>
         <value>0x784</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-14d">
         <name>__aeabi_uidiv</name>
         <value>0x3f9</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-14e">
         <name>__aeabi_uidivmod</name>
         <value>0x3f9</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-158">
         <name>__aeabi_idiv0</name>
         <value>0x59b</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-159">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-15b">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-15c">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
