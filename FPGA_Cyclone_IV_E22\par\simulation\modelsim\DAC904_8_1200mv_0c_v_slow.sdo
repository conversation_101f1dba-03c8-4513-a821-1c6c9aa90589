// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 10:31:23")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1158:1158:1158) (1153:1153:1153))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1389:1389:1389) (1170:1170:1170))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1367:1367:1367) (1134:1134:1134))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1559:1559:1559) (1346:1346:1346))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1550:1550:1550) (1339:1339:1339))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1605:1605:1605) (1387:1387:1387))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1151:1151:1151) (976:976:976))
        (IOPATH i o (3985:3985:3985) (4026:4026:4026))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1229:1229:1229) (1064:1064:1064))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1147:1147:1147) (985:985:985))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1205:1205:1205) (1040:1040:1040))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1180:1180:1180) (1012:1012:1012))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1063:1063:1063) (884:884:884))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (834:834:834) (708:708:708))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (826:826:826) (696:696:696))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (692:692:692) (834:834:834))
        (IOPATH i o (2696:2696:2696) (2717:2717:2717))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_spi_valid\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1485:1485:1485) (1262:1262:1262))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1149:1149:1149) (1361:1361:1361))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1958:1958:1958) (1646:1646:1646))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1665:1665:1665) (1434:1434:1434))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1070:1070:1070) (924:924:924))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2200:2200:2200) (1935:1935:1935))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1851:1851:1851) (1609:1609:1609))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1806:1806:1806) (1559:1559:1559))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1759:1759:1759) (1516:1516:1516))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2006:2006:2006) (1722:1722:1722))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1897:1897:1897) (1757:1757:1757))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1712:1712:1712) (1481:1481:1481))
        (IOPATH i o (4073:4073:4073) (4093:4093:4093))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2014:2014:2014) (1717:1717:1717))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1525:1525:1525) (1362:1362:1362))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1801:1801:1801) (1560:1560:1560))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1504:1504:1504) (1755:1755:1755))
        (IOPATH i o (2696:2696:2696) (2717:2717:2717))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1828:1828:1828) (2110:2110:2110))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1167:1167:1167) (1167:1167:1167))
        (PORT inclk[0] (2039:2039:2039) (2039:2039:2039))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2044:2044:2044) (2010:2010:2010))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE mosi_sync\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1412:1412:1412) (1645:1645:1645))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE mosi_sync\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE mosi_sync\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (278:278:278) (333:333:333))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE mosi_sync\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE sclk_sync\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT asdata (1436:1436:1436) (1614:1614:1614))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE sclk_sync\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT asdata (701:701:701) (762:762:762))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE sclk_d1)
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT asdata (938:938:938) (925:925:925))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE cs_n_sync\[0\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (3602:3602:3602) (3617:3617:3617))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE cs_n_sync\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE cs_n_sync\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT asdata (701:701:701) (762:762:762))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[11\]\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (327:327:327) (389:389:389))
        (PORT datad (529:529:529) (526:526:526))
        (IOPATH dataa combout (374:374:374) (392:392:392))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT asdata (723:723:723) (785:785:785))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (868:868:868) (797:797:797))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2740:2740:2740) (2421:2421:2421))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (287:287:287) (346:346:346))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2740:2740:2740) (2421:2421:2421))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (288:288:288) (348:348:348))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2740:2740:2740) (2421:2421:2421))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (283:283:283) (341:341:341))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2740:2740:2740) (2421:2421:2421))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (899:899:899) (826:826:826))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT asdata (724:724:724) (786:786:786))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT asdata (723:723:723) (785:785:785))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE debug_spi_valid\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (525:525:525) (523:523:523))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cnt\[3\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (557:557:557) (540:540:540))
        (PORT datad (286:286:286) (346:346:346))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cnt\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (PORT ena (1012:1012:1012) (982:982:982))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cnt\~1)
    (DELAY
      (ABSOLUTE
        (PORT dataa (293:293:293) (306:306:306))
        (PORT datab (559:559:559) (534:534:534))
        (PORT datad (534:534:534) (531:531:531))
        (IOPATH dataa combout (373:373:373) (380:380:380))
        (IOPATH datab combout (377:377:377) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cnt\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (PORT ena (1012:1012:1012) (982:982:982))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Add0\~0)
    (DELAY
      (ABSOLUTE
        (PORT datab (335:335:335) (395:395:395))
        (PORT datac (512:512:512) (496:496:496))
        (IOPATH datab combout (423:423:423) (391:391:391))
        (IOPATH datac combout (305:305:305) (285:285:285))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cnt\~3)
    (DELAY
      (ABSOLUTE
        (PORT dataa (283:283:283) (299:299:299))
        (PORT datab (511:511:511) (455:455:455))
        (PORT datad (533:533:533) (530:530:530))
        (IOPATH dataa combout (375:375:375) (371:371:371))
        (IOPATH datab combout (384:384:384) (386:386:386))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cnt\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (PORT ena (1012:1012:1012) (982:982:982))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cnt\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (282:282:282) (299:299:299))
        (PORT datab (336:336:336) (395:395:395))
        (PORT datad (532:532:532) (529:529:529))
        (IOPATH dataa combout (374:374:374) (392:392:392))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cnt\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1511:1511:1511))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1534:1534:1534) (1461:1461:1461))
        (PORT ena (1012:1012:1012) (982:982:982))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[0\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (327:327:327) (388:388:388))
        (PORT datab (332:332:332) (391:391:391))
        (PORT datac (512:512:512) (497:497:497))
        (PORT datad (290:290:290) (352:352:352))
        (IOPATH dataa combout (351:351:351) (371:371:371))
        (IOPATH datab combout (357:357:357) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[0\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT dataa (555:555:555) (539:539:539))
        (PORT datab (513:513:513) (457:457:457))
        (PORT datad (526:526:526) (522:522:522))
        (IOPATH dataa combout (349:349:349) (371:371:371))
        (IOPATH datab combout (354:354:354) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1440:1440:1440) (1481:1481:1481))
        (PORT asdata (1234:1234:1234) (1141:1141:1141))
        (PORT clrn (1504:1504:1504) (1432:1432:1432))
        (PORT ena (2233:2233:2233) (1990:1990:1990))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1440:1440:1440) (1481:1481:1481))
        (PORT asdata (928:928:928) (919:919:919))
        (PORT clrn (1504:1504:1504) (1432:1432:1432))
        (PORT ena (2233:2233:2233) (1990:1990:1990))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT asdata (723:723:723) (784:784:784))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (PORT ena (2335:2335:2335) (2059:2059:2059))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1440:1440:1440) (1481:1481:1481))
        (PORT asdata (912:912:912) (893:893:893))
        (PORT clrn (1504:1504:1504) (1432:1432:1432))
        (PORT ena (2233:2233:2233) (1990:1990:1990))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[12\]\~3)
    (DELAY
      (ABSOLUTE
        (PORT datad (506:506:506) (491:491:491))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1440:1440:1440) (1481:1481:1481))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1504:1504:1504) (1432:1432:1432))
        (PORT ena (2233:2233:2233) (1990:1990:1990))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (471:471:471) (584:584:584))
        (PORT datab (461:461:461) (556:556:556))
        (PORT datac (397:397:397) (492:492:492))
        (PORT datad (409:409:409) (500:500:500))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1059:1059:1059) (973:973:973))
        (PORT datab (1605:1605:1605) (1378:1378:1378))
        (PORT datac (1266:1266:1266) (1125:1125:1125))
        (PORT datad (901:901:901) (846:846:846))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (423:423:423) (380:380:380))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (471:471:471) (585:585:585))
        (PORT datab (462:462:462) (558:558:558))
        (PORT datac (397:397:397) (493:493:493))
        (PORT datad (410:410:410) (502:502:502))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (423:423:423) (386:386:386))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (472:472:472) (586:586:586))
        (PORT datab (463:463:463) (559:559:559))
        (PORT datac (398:398:398) (493:493:493))
        (PORT datad (411:411:411) (503:503:503))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (473:473:473) (586:586:586))
        (PORT datab (464:464:464) (560:560:560))
        (PORT datac (398:398:398) (493:493:493))
        (PORT datad (413:413:413) (504:504:504))
        (IOPATH dataa combout (420:420:420) (444:444:444))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1050:1050:1050) (963:963:963))
        (PORT datab (1602:1602:1602) (1375:1375:1375))
        (PORT datac (1264:1264:1264) (1123:1123:1123))
        (PORT datad (898:898:898) (843:843:843))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (465:465:465) (578:578:578))
        (PORT datab (452:452:452) (546:546:546))
        (PORT datac (395:395:395) (490:490:490))
        (PORT datad (398:398:398) (489:489:489))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (469:469:469) (583:583:583))
        (PORT datab (458:458:458) (553:553:553))
        (PORT datac (397:397:397) (492:492:492))
        (PORT datad (406:406:406) (497:497:497))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (470:470:470) (583:583:583))
        (PORT datab (459:459:459) (554:554:554))
        (PORT datac (397:397:397) (492:492:492))
        (PORT datad (407:407:407) (498:498:498))
        (IOPATH dataa combout (435:435:435) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (470:470:470) (583:583:583))
        (PORT datab (460:460:460) (555:555:555))
        (PORT datac (397:397:397) (492:492:492))
        (PORT datad (408:408:408) (499:499:499))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (468:468:468) (582:582:582))
        (PORT datab (457:457:457) (552:552:552))
        (PORT datac (396:396:396) (491:491:491))
        (PORT datad (404:404:404) (495:495:495))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (463:463:463) (575:575:575))
        (PORT datab (448:448:448) (543:543:543))
        (PORT datac (394:394:394) (489:489:489))
        (PORT datad (394:394:394) (484:484:484))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (393:393:393) (431:431:431))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (466:466:466) (579:579:579))
        (PORT datab (453:453:453) (548:548:548))
        (PORT datac (395:395:395) (490:490:490))
        (PORT datad (400:400:400) (491:491:491))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (464:464:464) (577:577:577))
        (PORT datab (450:450:450) (545:545:545))
        (PORT datac (394:394:394) (489:489:489))
        (PORT datad (396:396:396) (487:487:487))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (464:464:464) (576:576:576))
        (PORT datab (443:443:443) (530:530:530))
        (PORT datad (395:395:395) (486:486:486))
        (IOPATH dataa combout (404:404:404) (450:450:450))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (569:569:569) (552:552:552))
        (PORT datab (452:452:452) (547:547:547))
        (PORT datad (399:399:399) (489:489:489))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (467:467:467) (580:580:580))
        (PORT datab (455:455:455) (549:549:549))
        (PORT datad (401:401:401) (492:492:492))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (400:400:400) (391:391:391))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (486:486:486) (419:419:419))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (540:540:540) (448:448:448))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (828:828:828) (659:659:659))
        (PORT datab (315:315:315) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (494:494:494) (430:430:430))
        (PORT datab (533:533:533) (506:506:506))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (783:783:783) (643:643:643))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (783:783:783) (631:631:631))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (725:725:725) (601:601:601))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (494:494:494) (430:430:430))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (738:738:738) (615:615:615))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (792:792:792) (642:642:642))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (781:781:781) (630:630:630))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (530:530:530) (435:435:435))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (737:737:737) (614:614:614))
        (PORT datab (316:316:316) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (377:377:377))
        (PORT datab (472:472:472) (406:406:406))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (473:473:473) (409:409:409))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (318:318:318) (377:377:377))
        (PORT datab (864:864:864) (731:731:731))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2270:2270:2270) (2113:2113:2113))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (735:735:735) (614:614:614))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (813:813:813) (708:708:708))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1382:1382:1382) (1129:1129:1129))
        (PORT datab (316:316:316) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (493:493:493) (431:431:431))
        (PORT datab (535:535:535) (507:507:507))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (559:559:559) (559:559:559))
        (PORT datab (824:824:824) (715:715:715))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (825:825:825) (706:706:706))
        (PORT datab (553:553:553) (536:536:536))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (365:365:365) (425:425:425))
        (PORT datab (1159:1159:1159) (946:946:946))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (494:494:494) (433:433:433))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1057:1057:1057) (971:971:971))
        (PORT datab (1604:1604:1604) (1377:1377:1377))
        (PORT datac (1266:1266:1266) (1125:1125:1125))
        (PORT datad (901:901:901) (845:845:845))
        (IOPATH dataa combout (435:435:435) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (535:535:535) (437:437:437))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1170:1170:1170) (961:961:961))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1060:1060:1060) (974:974:974))
        (PORT datab (1605:1605:1605) (1378:1378:1378))
        (PORT datac (1266:1266:1266) (1126:1126:1126))
        (PORT datad (901:901:901) (846:846:846))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (827:827:827) (658:658:658))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (495:495:495) (434:434:434))
        (PORT datab (813:813:813) (736:736:736))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (548:548:548) (536:536:536))
        (PORT datab (827:827:827) (719:719:719))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1055:1055:1055) (968:968:968))
        (PORT datab (1604:1604:1604) (1377:1377:1377))
        (PORT datac (1265:1265:1265) (1125:1125:1125))
        (PORT datad (900:900:900) (845:845:845))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (486:486:486) (420:420:420))
        (PORT datab (546:546:546) (540:540:540))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1062:1062:1062) (976:976:976))
        (PORT datab (1605:1605:1605) (1378:1378:1378))
        (PORT datac (1266:1266:1266) (1126:1126:1126))
        (PORT datad (902:902:902) (847:847:847))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (537:537:537) (438:438:438))
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (403:403:403))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2304:2304:2304) (2137:2137:2137))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1274:1274:1274) (1175:1175:1175))
        (PORT d[1] (1570:1570:1570) (1396:1396:1396))
        (PORT d[2] (1340:1340:1340) (1228:1228:1228))
        (PORT d[3] (2045:2045:2045) (1829:1829:1829))
        (PORT d[4] (1369:1369:1369) (1244:1244:1244))
        (PORT d[5] (1573:1573:1573) (1408:1408:1408))
        (PORT d[6] (1326:1326:1326) (1220:1220:1220))
        (PORT d[7] (1311:1311:1311) (1195:1195:1195))
        (PORT d[8] (1556:1556:1556) (1384:1384:1384))
        (PORT d[9] (1299:1299:1299) (1189:1189:1189))
        (PORT d[10] (2534:2534:2534) (2155:2155:2155))
        (PORT d[11] (1571:1571:1571) (1405:1405:1405))
        (PORT clk (1819:1819:1819) (1884:1884:1884))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1819:1819:1819) (1884:1884:1884))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1820:1820:1820) (1885:1885:1885))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1774:1774:1774) (1837:1837:1837))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1451:1451:1451) (1494:1494:1494))
        (PORT asdata (1246:1246:1246) (1073:1073:1073))
        (PORT clrn (1517:1517:1517) (1443:1443:1443))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1111:1111:1111) (868:868:868))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1451:1451:1451) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1517:1517:1517) (1443:1443:1443))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (928:928:928) (879:879:879))
        (PORT d[1] (890:890:890) (816:816:816))
        (PORT d[2] (959:959:959) (892:892:892))
        (PORT d[3] (2789:2789:2789) (2458:2458:2458))
        (PORT d[4] (1029:1029:1029) (964:964:964))
        (PORT d[5] (1928:1928:1928) (1722:1722:1722))
        (PORT d[6] (944:944:944) (892:892:892))
        (PORT d[7] (1517:1517:1517) (1307:1307:1307))
        (PORT d[8] (2236:2236:2236) (1972:1972:1972))
        (PORT d[9] (916:916:916) (866:866:866))
        (PORT d[10] (2943:2943:2943) (2501:2501:2501))
        (PORT d[11] (1967:1967:1967) (1752:1752:1752))
        (PORT clk (1816:1816:1816) (1882:1882:1882))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1816:1816:1816) (1882:1882:1882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1817:1817:1817) (1883:1883:1883))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1771:1771:1771) (1835:1835:1835))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (869:869:869))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (870:870:870))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (870:870:870))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (870:870:870))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1113:1113:1113) (917:917:917))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1518:1518:1518) (1445:1445:1445))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1065:1065:1065) (885:885:885))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1518:1518:1518) (1445:1445:1445))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1309:1309:1309) (1198:1198:1198))
        (PORT d[1] (1233:1233:1233) (1110:1110:1110))
        (PORT d[2] (1307:1307:1307) (1187:1187:1187))
        (PORT d[3] (2427:2427:2427) (2157:2157:2157))
        (PORT d[4] (1312:1312:1312) (1197:1197:1197))
        (PORT d[5] (1885:1885:1885) (1683:1683:1683))
        (PORT d[6] (1990:1990:1990) (1750:1750:1750))
        (PORT d[7] (2010:2010:2010) (1765:1765:1765))
        (PORT d[8] (1907:1907:1907) (1689:1689:1689))
        (PORT d[9] (1301:1301:1301) (1190:1190:1190))
        (PORT d[10] (2887:2887:2887) (2456:2456:2456))
        (PORT d[11] (1967:1967:1967) (1745:1745:1745))
        (PORT clk (1818:1818:1818) (1883:1883:1883))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1883:1883:1883))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1819:1819:1819) (1884:1884:1884))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1773:1773:1773) (1836:1836:1836))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (870:870:870))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1187:1187:1187) (963:963:963))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1518:1518:1518) (1444:1444:1444))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1106:1106:1106) (912:912:912))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1518:1518:1518) (1444:1444:1444))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1300:1300:1300) (1194:1194:1194))
        (PORT d[1] (1301:1301:1301) (1201:1201:1201))
        (PORT d[2] (1384:1384:1384) (1251:1251:1251))
        (PORT d[3] (1360:1360:1360) (1238:1238:1238))
        (PORT d[4] (1289:1289:1289) (1169:1169:1169))
        (PORT d[5] (1411:1411:1411) (1295:1295:1295))
        (PORT d[6] (1282:1282:1282) (1155:1155:1155))
        (PORT d[7] (1984:1984:1984) (1780:1780:1780))
        (PORT d[8] (1261:1261:1261) (1136:1136:1136))
        (PORT d[9] (2013:2013:2013) (1801:1801:1801))
        (PORT d[10] (1971:1971:1971) (1754:1754:1754))
        (PORT d[11] (1308:1308:1308) (1204:1204:1204))
        (PORT clk (1833:1833:1833) (1897:1897:1897))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1833:1833:1833) (1897:1897:1897))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1834:1834:1834) (1898:1898:1898))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1788:1788:1788) (1850:1850:1850))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (860:860:860) (884:884:884))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (861:861:861) (885:885:885))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (861:861:861) (885:885:885))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (861:861:861) (885:885:885))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1324:1324:1324) (1135:1135:1135))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1446:1446:1446) (1489:1489:1489))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1496:1496:1496) (1426:1426:1426))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1252:1252:1252) (1084:1084:1084))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1446:1446:1446) (1489:1489:1489))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1496:1496:1496) (1426:1426:1426))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1711:1711:1711) (1555:1555:1555))
        (PORT d[1] (1768:1768:1768) (1603:1603:1603))
        (PORT d[2] (1749:1749:1749) (1585:1585:1585))
        (PORT d[3] (1748:1748:1748) (1581:1581:1581))
        (PORT d[4] (1921:1921:1921) (1717:1717:1717))
        (PORT d[5] (1650:1650:1650) (1482:1482:1482))
        (PORT d[6] (1609:1609:1609) (1450:1450:1450))
        (PORT d[7] (1933:1933:1933) (1732:1732:1732))
        (PORT d[8] (1634:1634:1634) (1463:1463:1463))
        (PORT d[9] (1920:1920:1920) (1720:1720:1720))
        (PORT d[10] (1648:1648:1648) (1460:1460:1460))
        (PORT d[11] (1701:1701:1701) (1542:1542:1542))
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1830:1830:1830) (1895:1895:1895))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1784:1784:1784) (1847:1847:1847))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1463:1463:1463) (1504:1504:1504))
        (PORT asdata (1282:1282:1282) (1163:1163:1163))
        (PORT clrn (1527:1527:1527) (1455:1455:1455))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (929:929:929) (798:798:798))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1463:1463:1463) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1527:1527:1527) (1455:1455:1455))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1687:1687:1687) (1536:1536:1536))
        (PORT d[1] (1671:1671:1671) (1527:1527:1527))
        (PORT d[2] (1720:1720:1720) (1563:1563:1563))
        (PORT d[3] (1741:1741:1741) (1593:1593:1593))
        (PORT d[4] (1720:1720:1720) (1540:1540:1540))
        (PORT d[5] (1655:1655:1655) (1488:1488:1488))
        (PORT d[6] (1609:1609:1609) (1450:1450:1450))
        (PORT d[7] (1613:1613:1613) (1444:1444:1444))
        (PORT d[8] (1639:1639:1639) (1469:1469:1469))
        (PORT d[9] (1602:1602:1602) (1434:1434:1434))
        (PORT d[10] (1572:1572:1572) (1407:1407:1407))
        (PORT d[11] (1746:1746:1746) (1585:1585:1585))
        (PORT clk (1818:1818:1818) (1881:1881:1881))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1881:1881:1881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1819:1819:1819) (1882:1882:1882))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1773:1773:1773) (1834:1834:1834))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (868:868:868))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (869:869:869))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (869:869:869))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (869:869:869))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1322:1322:1322) (1133:1133:1133))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1445:1445:1445) (1489:1489:1489))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1512:1512:1512) (1437:1437:1437))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1445:1445:1445) (1489:1489:1489))
        (PORT asdata (1730:1730:1730) (1541:1541:1541))
        (PORT clrn (1512:1512:1512) (1437:1437:1437))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2112:2112:2112) (1903:1903:1903))
        (PORT d[1] (1738:1738:1738) (1560:1560:1560))
        (PORT d[2] (1737:1737:1737) (1570:1570:1570))
        (PORT d[3] (1753:1753:1753) (1595:1595:1595))
        (PORT d[4] (1658:1658:1658) (1489:1489:1489))
        (PORT d[5] (1636:1636:1636) (1467:1467:1467))
        (PORT d[6] (1639:1639:1639) (1473:1473:1473))
        (PORT d[7] (2020:2020:2020) (1808:1808:1808))
        (PORT d[8] (1621:1621:1621) (1449:1449:1449))
        (PORT d[9] (1933:1933:1933) (1734:1734:1734))
        (PORT d[10] (2009:2009:2009) (1779:1779:1779))
        (PORT d[11] (1695:1695:1695) (1535:1535:1535))
        (PORT clk (1828:1828:1828) (1893:1893:1893))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1828:1828:1828) (1893:1893:1893))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1783:1783:1783) (1846:1846:1846))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (880:880:880))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1490:1490:1490))
        (PORT asdata (1715:1715:1715) (1537:1537:1537))
        (PORT clrn (1513:1513:1513) (1439:1439:1439))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1490:1490:1490))
        (PORT asdata (1716:1716:1716) (1537:1537:1537))
        (PORT clrn (1513:1513:1513) (1439:1439:1439))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (893:893:893) (823:823:823))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (904:904:904) (830:830:830))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (900:900:900) (827:827:827))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (867:867:867) (797:797:797))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (287:287:287) (346:346:346))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (287:287:287) (346:346:346))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (288:288:288) (346:346:346))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT asdata (724:724:724) (786:786:786))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT asdata (1326:1326:1326) (1252:1252:1252))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT asdata (1323:1323:1323) (1253:1253:1253))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[10\]\~4)
    (DELAY
      (ABSOLUTE
        (PORT datac (904:904:904) (834:834:834))
        (IOPATH datac combout (301:301:301) (283:283:283))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data_reg\[11\]\~5)
    (DELAY
      (ABSOLUTE
        (PORT datac (948:948:948) (866:866:866))
        (IOPATH datac combout (301:301:301) (283:283:283))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1493:1493:1493))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1516:1516:1516) (1442:1442:1442))
        (PORT ena (2669:2669:2669) (2344:2344:2344))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[4] (805:805:805) (709:709:709))
        (PORT dataa[5] (751:751:751) (670:670:670))
        (PORT dataa[6] (778:778:778) (695:695:695))
        (PORT dataa[7] (761:761:761) (677:677:677))
        (PORT dataa[8] (763:763:763) (682:682:682))
        (PORT dataa[9] (777:777:777) (687:687:687))
        (PORT dataa[10] (850:850:850) (761:761:761))
        (PORT dataa[11] (750:750:750) (664:664:664))
        (PORT dataa[12] (863:863:863) (787:787:787))
        (PORT dataa[13] (897:897:897) (818:818:818))
        (PORT dataa[14] (765:765:765) (687:687:687))
        (PORT dataa[15] (751:751:751) (680:680:680))
        (PORT dataa[16] (509:509:509) (491:491:491))
        (PORT dataa[17] (447:447:447) (472:472:472))
        (PORT datab[5] (486:486:486) (474:474:474))
        (PORT datab[6] (526:526:526) (518:518:518))
        (PORT datab[7] (517:517:517) (506:506:506))
        (PORT datab[8] (482:482:482) (473:473:473))
        (PORT datab[9] (492:492:492) (486:486:486))
        (PORT datab[10] (490:490:490) (480:480:480))
        (PORT datab[11] (515:515:515) (506:506:506))
        (PORT datab[12] (523:523:523) (517:517:517))
        (PORT datab[13] (488:488:488) (477:477:477))
        (PORT datab[14] (518:518:518) (504:504:504))
        (PORT datab[15] (503:503:503) (511:511:511))
        (PORT datab[16] (473:473:473) (482:482:482))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1652:1652:1652) (1349:1349:1349))
        (PORT datac (1636:1636:1636) (1320:1320:1320))
        (PORT datad (1265:1265:1265) (1005:1005:1005))
        (IOPATH dataa combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~1)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1653:1653:1653) (1350:1350:1350))
        (PORT datac (1636:1636:1636) (1319:1319:1319))
        (PORT datad (1279:1279:1279) (1020:1020:1020))
        (IOPATH dataa combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~2)
    (DELAY
      (ABSOLUTE
        (PORT datab (1232:1232:1232) (975:975:975))
        (PORT datac (1605:1605:1605) (1304:1304:1304))
        (PORT datad (1581:1581:1581) (1290:1290:1290))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~3)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1652:1652:1652) (1349:1349:1349))
        (PORT datac (1637:1637:1637) (1321:1321:1321))
        (PORT datad (1316:1316:1316) (1046:1046:1046))
        (IOPATH dataa combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1657:1657:1657) (1354:1354:1354))
        (PORT datac (1629:1629:1629) (1313:1313:1313))
        (PORT datad (1267:1267:1267) (1011:1011:1011))
        (IOPATH dataa combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~5)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1654:1654:1654) (1352:1352:1352))
        (PORT datac (1537:1537:1537) (1223:1223:1223))
        (PORT datad (1580:1580:1580) (1289:1289:1289))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1635:1635:1635) (1295:1295:1295))
        (PORT datac (1606:1606:1606) (1305:1305:1305))
        (PORT datad (1579:1579:1579) (1289:1289:1289))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~7)
    (DELAY
      (ABSOLUTE
        (PORT datab (1350:1350:1350) (1075:1075:1075))
        (PORT datac (1607:1607:1607) (1306:1306:1306))
        (PORT datad (1578:1578:1578) (1288:1288:1288))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1657:1657:1657) (1355:1355:1355))
        (PORT datac (1629:1629:1629) (1312:1312:1312))
        (PORT datad (1289:1289:1289) (1051:1051:1051))
        (IOPATH dataa combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~9)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1574:1574:1574) (1254:1254:1254))
        (PORT datac (1604:1604:1604) (1303:1303:1303))
        (PORT datad (1582:1582:1582) (1291:1291:1291))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1651:1651:1651) (1349:1349:1349))
        (PORT datac (1638:1638:1638) (1322:1322:1322))
        (PORT datad (1495:1495:1495) (1203:1203:1203))
        (IOPATH dataa combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~11)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1654:1654:1654) (1351:1351:1351))
        (PORT datac (1634:1634:1634) (1318:1318:1318))
        (PORT datad (1596:1596:1596) (1268:1268:1268))
        (IOPATH dataa combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1655:1655:1655) (1353:1353:1353))
        (PORT datac (1586:1586:1586) (1257:1257:1257))
        (PORT datad (1579:1579:1579) (1288:1288:1288))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE dac_output_temp\~13)
    (DELAY
      (ABSOLUTE
        (PORT datac (1604:1604:1604) (1303:1303:1303))
        (PORT datad (1582:1582:1582) (1291:1291:1291))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE debug_spi_valid\~1)
    (DELAY
      (ABSOLUTE
        (PORT dataa (270:270:270) (282:282:282))
        (PORT datab (331:331:331) (391:391:391))
        (PORT datac (283:283:283) (350:350:350))
        (PORT datad (292:292:292) (354:354:354))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
)
