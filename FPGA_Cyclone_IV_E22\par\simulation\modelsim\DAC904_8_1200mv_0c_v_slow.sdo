// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 03:33:33")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1158:1158:1158) (1153:1153:1153))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2078:2078:2078) (1738:1738:1738))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2065:2065:2065) (1738:1738:1738))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2169:2169:2169) (1753:1753:1753))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2312:2312:2312) (1856:1856:1856))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1789:1789:1789) (1492:1492:1492))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1355:1355:1355) (1109:1109:1109))
        (IOPATH i o (3985:3985:3985) (4026:4026:4026))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1377:1377:1377) (1159:1159:1159))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1683:1683:1683) (1372:1372:1372))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1451:1451:1451) (1201:1201:1201))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1634:1634:1634) (1350:1350:1350))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1387:1387:1387) (1145:1145:1145))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2054:2054:2054) (1699:1699:1699))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1805:1805:1805) (1531:1531:1531))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1150:1150:1150) (1359:1359:1359))
        (IOPATH i o (2696:2696:2696) (2717:2717:2717))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_spi_valid\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1422:1422:1422) (1230:1230:1230))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1066:1066:1066) (1200:1200:1200))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1443:1443:1443) (1270:1270:1270))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1477:1477:1477) (1263:1263:1263))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1423:1423:1423) (1225:1225:1225))
        (IOPATH i o (4073:4073:4073) (4093:4093:4093))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1610:1610:1610) (1465:1465:1465))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2146:2146:2146) (1864:1864:1864))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1879:1879:1879) (1664:1664:1664))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2061:2061:2061) (1769:1769:1769))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1547:1547:1547) (1360:1360:1360))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1565:1565:1565) (1402:1402:1402))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1905:1905:1905) (1669:1669:1669))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2114:2114:2114) (1852:1852:1852))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1844:1844:1844) (1626:1626:1626))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2072:2072:2072) (1785:1785:1785))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1703:1703:1703) (1928:1928:1928))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1343:1343:1343) (1501:1501:1501))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1167:1167:1167) (1167:1167:1167))
        (PORT inclk[0] (2039:2039:2039) (2039:2039:2039))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2044:2044:2044) (2010:2010:2010))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE spi_sclk\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (775:775:775) (984:984:984))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1204:1204:1204) (1079:1079:1079))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4215:4215:4215) (4100:4100:4100))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (285:285:285) (343:343:343))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4215:4215:4215) (4100:4100:4100))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT asdata (712:712:712) (777:777:777))
        (PORT ena (4215:4215:4215) (4100:4100:4100))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (284:284:284) (342:342:342))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4215:4215:4215) (4100:4100:4100))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1177:1177:1177) (1060:1060:1060))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT asdata (725:725:725) (788:788:788))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT asdata (723:723:723) (784:784:784))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (355:355:355))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT asdata (723:723:723) (785:785:785))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[10\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT asdata (724:724:724) (786:786:786))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d1\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (3334:3334:3334) (3426:3426:3426))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d1)
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1535:1535:1535) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d2\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (286:286:286) (345:345:345))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d2)
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1535:1535:1535) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always2\~0)
    (DELAY
      (ABSOLUTE
        (PORT datab (318:318:318) (373:373:373))
        (PORT datad (284:284:284) (342:342:342))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1454:1454:1454) (1496:1496:1496))
        (PORT asdata (1722:1722:1722) (1592:1592:1592))
        (PORT clrn (1519:1519:1519) (1446:1446:1446))
        (PORT ena (1945:1945:1945) (1744:1744:1744))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[13\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datac (300:300:300) (364:364:364))
        (IOPATH datac combout (301:301:301) (283:283:283))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[14\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1454:1454:1454) (1496:1496:1496))
        (PORT asdata (1677:1677:1677) (1556:1556:1556))
        (PORT clrn (1519:1519:1519) (1446:1446:1446))
        (PORT ena (1945:1945:1945) (1744:1744:1744))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[15\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4208:4208:4208) (4079:4079:4079))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1454:1454:1454) (1496:1496:1496))
        (PORT asdata (1688:1688:1688) (1554:1554:1554))
        (PORT clrn (1519:1519:1519) (1446:1446:1446))
        (PORT ena (1945:1945:1945) (1744:1744:1744))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1454:1454:1454) (1496:1496:1496))
        (PORT asdata (1743:1743:1743) (1597:1597:1597))
        (PORT clrn (1519:1519:1519) (1446:1446:1446))
        (PORT ena (1945:1945:1945) (1744:1744:1744))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (776:776:776) (742:742:742))
        (PORT datab (464:464:464) (560:560:560))
        (PORT datac (387:387:387) (481:481:481))
        (PORT datad (404:404:404) (494:494:494))
        (IOPATH dataa combout (392:392:392) (419:419:419))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (777:777:777) (743:743:743))
        (PORT datab (463:463:463) (559:559:559))
        (PORT datac (387:387:387) (481:481:481))
        (PORT datad (404:404:404) (495:495:495))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (779:779:779) (745:745:745))
        (PORT datab (462:462:462) (558:558:558))
        (PORT datac (388:388:388) (481:481:481))
        (PORT datad (405:405:405) (496:496:496))
        (IOPATH dataa combout (420:420:420) (380:380:380))
        (IOPATH datab combout (423:423:423) (386:386:386))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (779:779:779) (745:745:745))
        (PORT datab (460:460:460) (556:556:556))
        (PORT datac (388:388:388) (481:481:481))
        (PORT datad (405:405:405) (496:496:496))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (952:952:952) (898:898:898))
        (PORT datab (1272:1272:1272) (1155:1155:1155))
        (PORT datac (939:939:939) (868:868:868))
        (PORT datad (924:924:924) (866:866:866))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (769:769:769) (734:734:734))
        (PORT datab (470:470:470) (567:567:567))
        (PORT datac (386:386:386) (480:480:480))
        (PORT datad (401:401:401) (492:492:492))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (765:765:765) (730:730:730))
        (PORT datab (474:474:474) (572:572:572))
        (PORT datac (386:386:386) (479:479:479))
        (PORT datad (400:400:400) (490:490:490))
        (IOPATH dataa combout (392:392:392) (419:419:419))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (766:766:766) (731:731:731))
        (PORT datab (473:473:473) (571:571:571))
        (PORT datac (386:386:386) (479:479:479))
        (PORT datad (400:400:400) (491:491:491))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (767:767:767) (733:733:733))
        (PORT datab (472:472:472) (568:568:568))
        (PORT datac (386:386:386) (480:480:480))
        (PORT datad (401:401:401) (491:491:491))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (781:781:781) (747:747:747))
        (PORT datab (459:459:459) (555:555:555))
        (PORT datac (388:388:388) (481:481:481))
        (PORT datad (406:406:406) (496:496:496))
        (IOPATH dataa combout (392:392:392) (419:419:419))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (782:782:782) (748:748:748))
        (PORT datab (458:458:458) (554:554:554))
        (PORT datac (388:388:388) (481:481:481))
        (PORT datad (406:406:406) (497:497:497))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (771:771:771) (736:736:736))
        (PORT datab (468:468:468) (565:565:565))
        (PORT datac (386:386:386) (480:480:480))
        (PORT datad (402:402:402) (493:493:493))
        (IOPATH dataa combout (404:404:404) (450:450:450))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (785:785:785) (751:751:751))
        (PORT datab (456:456:456) (552:552:552))
        (PORT datac (389:389:389) (482:482:482))
        (PORT datad (407:407:407) (498:498:498))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (593:593:593) (585:585:585))
        (PORT datab (349:349:349) (407:407:407))
        (PORT datad (407:407:407) (497:497:497))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (592:592:592) (585:585:585))
        (PORT datab (469:469:469) (566:566:566))
        (PORT datad (305:305:305) (364:364:364))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (593:593:593) (585:585:585))
        (PORT datab (475:475:475) (573:573:573))
        (PORT datad (399:399:399) (490:490:490))
        (IOPATH dataa combout (373:373:373) (380:380:380))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (772:772:772) (738:738:738))
        (PORT datab (466:466:466) (563:563:563))
        (PORT datad (403:403:403) (493:493:493))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (483:483:483) (414:414:414))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (537:537:537) (443:443:443))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (536:536:536) (438:438:438))
        (PORT datab (315:315:315) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (377:377:377))
        (PORT datab (854:854:854) (707:707:707))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (781:781:781) (656:656:656))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (494:494:494) (432:432:432))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (833:833:833) (691:691:691))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (559:559:559) (466:466:466))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (557:557:557) (469:469:469))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (534:534:534) (524:524:524))
        (PORT datab (841:841:841) (711:711:711))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (778:778:778) (651:651:651))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (478:478:478) (403:403:403))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (557:557:557) (469:469:469))
        (PORT datab (316:316:316) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (377:377:377))
        (PORT datab (474:474:474) (408:408:408))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (536:536:536) (438:438:438))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (318:318:318) (377:377:377))
        (PORT datab (828:828:828) (677:677:677))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2490:2490:2490) (2244:2244:2244))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (484:484:484) (418:418:418))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1125:1125:1125) (961:961:961))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (529:529:529) (521:521:521))
        (PORT datab (901:901:901) (753:753:753))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (1234:1234:1234) (1034:1034:1034))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (921:921:921) (775:775:775))
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (864:864:864) (722:722:722))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (900:900:900) (752:752:752))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (564:564:564) (543:543:543))
        (PORT datab (1233:1233:1233) (1033:1033:1033))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (954:954:954) (901:901:901))
        (PORT datab (1271:1271:1271) (1154:1154:1154))
        (PORT datac (942:942:942) (872:872:872))
        (PORT datad (929:929:929) (872:872:872))
        (IOPATH dataa combout (435:435:435) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (534:534:534) (435:435:435))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (539:539:539) (534:534:534))
        (PORT datab (811:811:811) (682:682:682))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (953:953:953) (900:900:900))
        (PORT datab (1272:1272:1272) (1155:1155:1155))
        (PORT datac (941:941:941) (870:870:870))
        (PORT datad (926:926:926) (868:868:868))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (535:535:535) (437:437:437))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (1231:1231:1231) (1031:1031:1031))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (925:925:925) (779:779:779))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (954:954:954) (901:901:901))
        (PORT datab (1272:1272:1272) (1154:1154:1154))
        (PORT datac (942:942:942) (871:871:871))
        (PORT datad (928:928:928) (870:870:870))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (479:479:479) (404:404:404))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (954:954:954) (900:900:900))
        (PORT datab (1272:1272:1272) (1155:1155:1155))
        (PORT datac (942:942:942) (871:871:871))
        (PORT datad (927:927:927) (870:870:870))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (473:473:473) (410:410:410))
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (403:403:403))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2068:2068:2068) (1871:1871:1871))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1651:1651:1651) (1499:1499:1499))
        (PORT d[1] (1412:1412:1412) (1328:1328:1328))
        (PORT d[2] (2059:2059:2059) (1859:1859:1859))
        (PORT d[3] (1422:1422:1422) (1325:1325:1325))
        (PORT d[4] (1772:1772:1772) (1628:1628:1628))
        (PORT d[5] (1804:1804:1804) (1655:1655:1655))
        (PORT d[6] (2070:2070:2070) (1865:1865:1865))
        (PORT d[7] (1657:1657:1657) (1521:1521:1521))
        (PORT d[8] (1706:1706:1706) (1539:1539:1539))
        (PORT d[9] (2246:2246:2246) (2039:2039:2039))
        (PORT d[10] (1830:1830:1830) (1684:1684:1684))
        (PORT d[11] (1702:1702:1702) (1553:1553:1553))
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1830:1830:1830) (1895:1895:1895))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1784:1784:1784) (1847:1847:1847))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (942:942:942) (816:816:816))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1493:1493:1493) (1420:1420:1420))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (897:897:897) (782:782:782))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1493:1493:1493) (1420:1420:1420))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1288:1288:1288) (1186:1186:1186))
        (PORT d[1] (1016:1016:1016) (976:976:976))
        (PORT d[2] (1396:1396:1396) (1272:1272:1272))
        (PORT d[3] (1467:1467:1467) (1360:1360:1360))
        (PORT d[4] (2184:2184:2184) (1987:1987:1987))
        (PORT d[5] (1421:1421:1421) (1318:1318:1318))
        (PORT d[6] (2089:2089:2089) (1885:1885:1885))
        (PORT d[7] (2032:2032:2032) (1849:1849:1849))
        (PORT d[8] (2143:2143:2143) (1930:1930:1930))
        (PORT d[9] (2277:2277:2277) (2067:2067:2067))
        (PORT d[10] (2214:2214:2214) (2027:2027:2027))
        (PORT d[11] (1352:1352:1352) (1247:1247:1247))
        (PORT clk (1815:1815:1815) (1878:1878:1878))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1815:1815:1815) (1878:1878:1878))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1816:1816:1816) (1879:1879:1879))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1770:1770:1770) (1831:1831:1831))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (842:842:842) (865:865:865))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1228:1228:1228) (1075:1075:1075))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1461:1461:1461) (1502:1502:1502))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1525:1525:1525) (1453:1453:1453))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1461:1461:1461) (1502:1502:1502))
        (PORT asdata (1672:1672:1672) (1502:1502:1502))
        (PORT clrn (1525:1525:1525) (1453:1453:1453))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1630:1630:1630) (1476:1476:1476))
        (PORT d[1] (993:993:993) (958:958:958))
        (PORT d[2] (1340:1340:1340) (1216:1216:1216))
        (PORT d[3] (985:985:985) (942:942:942))
        (PORT d[4] (1385:1385:1385) (1283:1283:1283))
        (PORT d[5] (1752:1752:1752) (1605:1605:1605))
        (PORT d[6] (2088:2088:2088) (1885:1885:1885))
        (PORT d[7] (1300:1300:1300) (1208:1208:1208))
        (PORT d[8] (2142:2142:2142) (1929:1929:1929))
        (PORT d[9] (1468:1468:1468) (1357:1357:1357))
        (PORT d[10] (1396:1396:1396) (1292:1292:1292))
        (PORT d[11] (1339:1339:1339) (1239:1239:1239))
        (PORT clk (1816:1816:1816) (1879:1879:1879))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1816:1816:1816) (1879:1879:1879))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1817:1817:1817) (1880:1880:1880))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1771:1771:1771) (1832:1832:1832))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (867:867:867))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT asdata (1652:1652:1652) (1491:1491:1491))
        (PORT clrn (1527:1527:1527) (1454:1454:1454))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT asdata (1684:1684:1684) (1520:1520:1520))
        (PORT clrn (1527:1527:1527) (1454:1454:1454))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1717:1717:1717) (1555:1555:1555))
        (PORT d[1] (1384:1384:1384) (1306:1306:1306))
        (PORT d[2] (1687:1687:1687) (1536:1536:1536))
        (PORT d[3] (1396:1396:1396) (1304:1304:1304))
        (PORT d[4] (1781:1781:1781) (1637:1637:1637))
        (PORT d[5] (1847:1847:1847) (1690:1690:1690))
        (PORT d[6] (1678:1678:1678) (1528:1528:1528))
        (PORT d[7] (1699:1699:1699) (1551:1551:1551))
        (PORT d[8] (1731:1731:1731) (1570:1570:1570))
        (PORT d[9] (1819:1819:1819) (1665:1665:1665))
        (PORT d[10] (1845:1845:1845) (1689:1689:1689))
        (PORT d[11] (1730:1730:1730) (1580:1580:1580))
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1830:1830:1830) (1895:1895:1895))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1784:1784:1784) (1847:1847:1847))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (947:947:947) (817:817:817))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1507:1507:1507) (1430:1430:1430))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (951:951:951) (823:823:823))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1507:1507:1507) (1430:1430:1430))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1669:1669:1669) (1510:1510:1510))
        (PORT d[1] (1040:1040:1040) (978:978:978))
        (PORT d[2] (927:927:927) (875:875:875))
        (PORT d[3] (1029:1029:1029) (962:962:962))
        (PORT d[4] (2191:2191:2191) (1995:1995:1995))
        (PORT d[5] (1757:1757:1757) (1610:1610:1610))
        (PORT d[6] (1653:1653:1653) (1517:1517:1517))
        (PORT d[7] (1017:1017:1017) (954:954:954))
        (PORT d[8] (1731:1731:1731) (1552:1552:1552))
        (PORT d[9] (1023:1023:1023) (969:969:969))
        (PORT d[10] (980:980:980) (931:931:931))
        (PORT d[11] (1679:1679:1679) (1535:1535:1535))
        (PORT clk (1815:1815:1815) (1878:1878:1878))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1815:1815:1815) (1878:1878:1878))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1816:1816:1816) (1879:1879:1879))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1770:1770:1770) (1831:1831:1831))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (842:842:842) (865:865:865))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1274:1274:1274) (1100:1100:1100))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1535:1535:1535) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1228:1228:1228) (1064:1064:1064))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1535:1535:1535) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1656:1656:1656) (1495:1495:1495))
        (PORT d[1] (975:975:975) (927:927:927))
        (PORT d[2] (1643:1643:1643) (1479:1479:1479))
        (PORT d[3] (963:963:963) (918:918:918))
        (PORT d[4] (2147:2147:2147) (1961:1961:1961))
        (PORT d[5] (1788:1788:1788) (1629:1629:1629))
        (PORT d[6] (1653:1653:1653) (1516:1516:1516))
        (PORT d[7] (2032:2032:2032) (1850:1850:1850))
        (PORT d[8] (1381:1381:1381) (1255:1255:1255))
        (PORT d[9] (1340:1340:1340) (1242:1242:1242))
        (PORT d[10] (2288:2288:2288) (2088:2088:2088))
        (PORT d[11] (1329:1329:1329) (1233:1233:1233))
        (PORT clk (1813:1813:1813) (1877:1877:1877))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1813:1813:1813) (1877:1877:1877))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1814:1814:1814) (1878:1878:1878))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1768:1768:1768) (1830:1830:1830))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (840:840:840) (864:864:864))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (841:841:841) (865:865:865))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (841:841:841) (865:865:865))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (841:841:841) (865:865:865))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1621:1621:1621) (1366:1366:1366))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1444:1444:1444) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1521:1521:1521) (1301:1301:1301))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1444:1444:1444) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1679:1679:1679) (1520:1520:1520))
        (PORT d[1] (1059:1059:1059) (1015:1015:1015))
        (PORT d[2] (1303:1303:1303) (1206:1206:1206))
        (PORT d[3] (1016:1016:1016) (969:969:969))
        (PORT d[4] (1762:1762:1762) (1619:1619:1619))
        (PORT d[5] (1808:1808:1808) (1655:1655:1655))
        (PORT d[6] (1288:1288:1288) (1195:1195:1195))
        (PORT d[7] (1390:1390:1390) (1277:1277:1277))
        (PORT d[8] (2093:2093:2093) (1889:1889:1889))
        (PORT d[9] (1835:1835:1835) (1672:1672:1672))
        (PORT d[10] (2193:2193:2193) (2003:2003:2003))
        (PORT d[11] (1308:1308:1308) (1215:1215:1215))
        (PORT clk (1817:1817:1817) (1879:1879:1879))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1817:1817:1817) (1879:1879:1879))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1880:1880:1880))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1772:1772:1772) (1832:1832:1832))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (866:866:866))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (867:867:867))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1668:1668:1668) (1448:1448:1448))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1436:1436:1436) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1428:1428:1428))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1436:1436:1436) (1475:1475:1475))
        (PORT asdata (2089:2089:2089) (1868:1868:1868))
        (PORT clrn (1498:1498:1498) (1428:1428:1428))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1202:1202:1202) (1077:1077:1077))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (285:285:285) (344:344:344))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT asdata (713:713:713) (778:778:778))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (286:286:286) (344:344:344))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (509:509:509) (492:492:492))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT asdata (1633:1633:1633) (1499:1499:1499))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT asdata (1651:1651:1651) (1514:1514:1514))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1262:1262:1262) (1121:1121:1121))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1239:1239:1239) (1104:1104:1104))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1252:1252:1252) (1114:1114:1114))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1210:1210:1210) (1082:1082:1082))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1293:1293:1293) (1144:1144:1144))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1508:1508:1508) (1433:1433:1433))
        (PORT ena (1891:1891:1891) (1687:1687:1687))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (755:755:755) (683:683:683))
        (PORT dataa[1] (820:820:820) (742:742:742))
        (PORT dataa[2] (828:828:828) (767:767:767))
        (PORT dataa[3] (832:832:832) (774:774:774))
        (PORT dataa[4] (849:849:849) (772:772:772))
        (PORT dataa[5] (873:873:873) (785:785:785))
        (PORT dataa[6] (773:773:773) (698:698:698))
        (PORT dataa[7] (743:743:743) (669:669:669))
        (PORT dataa[8] (1242:1242:1242) (1105:1105:1105))
        (PORT dataa[9] (1161:1161:1161) (1048:1048:1048))
        (PORT dataa[10] (754:754:754) (659:659:659))
        (PORT dataa[11] (855:855:855) (793:793:793))
        (PORT dataa[12] (872:872:872) (812:812:812))
        (PORT dataa[13] (814:814:814) (883:883:883))
        (PORT dataa[14] (1061:1061:1061) (1187:1187:1187))
        (PORT dataa[15] (823:823:823) (889:889:889))
        (PORT dataa[16] (814:814:814) (883:883:883))
        (PORT dataa[17] (1061:1061:1061) (1187:1187:1187))
        (PORT datab[6] (494:494:494) (490:490:490))
        (PORT datab[7] (507:507:507) (495:495:495))
        (PORT datab[8] (477:477:477) (464:464:464))
        (PORT datab[9] (490:490:490) (475:475:475))
        (PORT datab[10] (533:533:533) (516:516:516))
        (PORT datab[11] (500:500:500) (482:482:482))
        (PORT datab[12] (483:483:483) (477:477:477))
        (PORT datab[13] (507:507:507) (494:494:494))
        (PORT datab[14] (490:490:490) (484:484:484))
        (PORT datab[15] (481:481:481) (473:473:473))
        (PORT datab[16] (510:510:510) (520:520:520))
        (PORT datab[17] (490:490:490) (500:500:500))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (915:915:915) (842:842:842))
        (PORT dataa[7] (879:879:879) (827:827:827))
        (PORT dataa[8] (927:927:927) (862:862:862))
        (PORT dataa[9] (860:860:860) (812:812:812))
        (PORT dataa[10] (1181:1181:1181) (1054:1054:1054))
        (PORT dataa[11] (1128:1128:1128) (1002:1002:1002))
        (PORT dataa[12] (903:903:903) (843:843:843))
        (PORT dataa[13] (879:879:879) (826:826:826))
        (PORT dataa[14] (857:857:857) (807:807:807))
        (PORT dataa[15] (849:849:849) (797:797:797))
        (PORT dataa[16] (843:843:843) (897:897:897))
        (PORT dataa[17] (1070:1070:1070) (1208:1208:1208))
        (PORT datab[10] (722:722:722) (803:803:803))
        (PORT datab[11] (758:758:758) (822:822:822))
        (PORT datab[12] (767:767:767) (827:827:827))
        (PORT datab[13] (722:722:722) (803:803:803))
        (PORT datab[14] (758:758:758) (822:822:822))
        (PORT datab[15] (767:767:767) (827:827:827))
        (PORT datab[16] (722:722:722) (803:803:803))
        (PORT datab[17] (758:758:758) (822:822:822))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (486:486:486) (379:379:379))
        (PORT datab (1116:1116:1116) (917:917:917))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (423:423:423) (351:351:351))
        (PORT datab (1089:1089:1089) (880:880:880))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (702:702:702) (571:571:571))
        (PORT datab (864:864:864) (708:708:708))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (697:697:697) (580:580:580))
        (PORT datab (785:785:785) (667:667:667))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (701:701:701) (569:569:569))
        (PORT datab (827:827:827) (677:677:677))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (762:762:762) (606:606:606))
        (PORT datab (793:793:793) (661:661:661))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (748:748:748) (597:597:597))
        (PORT datab (811:811:811) (686:686:686))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (753:753:753) (598:598:598))
        (PORT datad (1087:1087:1087) (874:874:874))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_valid)
    (DELAY
      (ABSOLUTE
        (PORT clk (1469:1469:1469) (1512:1512:1512))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1535:1535:1535) (1461:1461:1461))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
)
