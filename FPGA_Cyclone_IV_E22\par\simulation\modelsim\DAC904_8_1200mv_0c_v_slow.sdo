// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 03:21:18")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1169:1169:1169) (1169:1169:1169))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2078:2078:2078) (1738:1738:1738))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2065:2065:2065) (1738:1738:1738))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2169:2169:2169) (1753:1753:1753))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2316:2316:2316) (1860:1860:1860))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1789:1789:1789) (1492:1492:1492))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1362:1362:1362) (1117:1117:1117))
        (IOPATH i o (3985:3985:3985) (4026:4026:4026))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1636:1636:1636) (1354:1354:1354))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1686:1686:1686) (1391:1391:1391))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1972:1972:1972) (1600:1600:1600))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1154:1154:1154) (1000:1000:1000))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1762:1762:1762) (1455:1455:1455))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1616:1616:1616) (1330:1330:1330))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1974:1974:1974) (1587:1587:1587))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1360:1360:1360) (1657:1657:1657))
        (IOPATH i o (2696:2696:2696) (2717:2717:2717))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1275:1275:1275) (1461:1461:1461))
        (IOPATH i o (2782:2782:2782) (2820:2820:2820))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1392:1392:1392) (1208:1208:1208))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1144:1144:1144) (1036:1036:1036))
        (IOPATH i o (4073:4073:4073) (4093:4093:4093))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1768:1768:1768) (1532:1532:1532))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1754:1754:1754) (1513:1513:1513))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1792:1792:1792) (1538:1538:1538))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1826:1826:1826) (1602:1602:1602))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1522:1522:1522) (1355:1355:1355))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (3102:3102:3102) (2889:2889:2889))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2124:2124:2124) (1834:1834:1834))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2145:2145:2145) (1882:1882:1882))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1739:1739:1739) (1521:1521:1521))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2770:2770:2770) (2606:2606:2606))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2509:2509:2509) (2140:2140:2140))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1849:1849:1849) (2165:2165:2165))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1353:1353:1353) (1523:1523:1523))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1167:1167:1167) (1167:1167:1167))
        (PORT inclk[0] (2039:2039:2039) (2039:2039:2039))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2044:2044:2044) (2010:2010:2010))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE spi_sclk\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (952:952:952) (1120:1120:1120))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (538:538:538) (513:513:513))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4526:4526:4526) (4468:4468:4468))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT asdata (711:711:711) (775:775:775))
        (PORT ena (4526:4526:4526) (4468:4468:4468))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (285:285:285) (343:343:343))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4526:4526:4526) (4468:4468:4468))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (285:285:285) (343:343:343))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4526:4526:4526) (4468:4468:4468))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (494:494:494) (478:478:478))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT asdata (926:926:926) (908:908:908))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT asdata (723:723:723) (785:785:785))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[10\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datac (299:299:299) (363:363:363))
        (IOPATH datac combout (301:301:301) (283:283:283))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (302:302:302) (358:358:358))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[13\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (304:304:304) (360:360:360))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d1\~0)
    (DELAY
      (ABSOLUTE
        (PORT datac (3719:3719:3719) (3746:3746:3746))
        (IOPATH datac combout (301:301:301) (283:283:283))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d1)
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1425:1425:1425))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d2)
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT asdata (932:932:932) (917:917:917))
        (PORT clrn (1499:1499:1499) (1425:1425:1425))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (549:549:549) (531:531:531))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datac combout (415:415:415) (429:429:429))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1481:1481:1481))
        (PORT asdata (1950:1950:1950) (1758:1758:1758))
        (PORT clrn (1504:1504:1504) (1431:1431:1431))
        (PORT ena (2202:2202:2202) (1953:1953:1953))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT asdata (724:724:724) (785:785:785))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1481:1481:1481))
        (PORT asdata (1990:1990:1990) (1793:1793:1793))
        (PORT clrn (1504:1504:1504) (1431:1431:1431))
        (PORT ena (2202:2202:2202) (1953:1953:1953))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[15\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (356:356:356))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1433:1433:1433) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (4487:4487:4487) (4429:4429:4429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1481:1481:1481))
        (PORT asdata (1866:1866:1866) (1693:1693:1693))
        (PORT clrn (1504:1504:1504) (1431:1431:1431))
        (PORT ena (2202:2202:2202) (1953:1953:1953))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1481:1481:1481))
        (PORT asdata (1933:1933:1933) (1747:1747:1747))
        (PORT clrn (1504:1504:1504) (1431:1431:1431))
        (PORT ena (2202:2202:2202) (1953:1953:1953))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (479:479:479) (595:595:595))
        (PORT datab (461:461:461) (558:558:558))
        (PORT datac (386:386:386) (480:480:480))
        (PORT datad (615:615:615) (625:625:625))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (479:479:479) (596:596:596))
        (PORT datab (460:460:460) (557:557:557))
        (PORT datac (386:386:386) (479:479:479))
        (PORT datad (616:616:616) (625:625:625))
        (IOPATH dataa combout (435:435:435) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (990:990:990) (927:927:927))
        (PORT datab (982:982:982) (910:910:910))
        (PORT datac (901:901:901) (845:845:845))
        (PORT datad (1545:1545:1545) (1383:1383:1383))
        (IOPATH dataa combout (420:420:420) (380:380:380))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (480:480:480) (596:596:596))
        (PORT datab (459:459:459) (556:556:556))
        (PORT datac (385:385:385) (479:479:479))
        (PORT datad (617:617:617) (626:626:626))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (483:483:483) (600:600:600))
        (PORT datab (453:453:453) (549:549:549))
        (PORT datac (383:383:383) (476:476:476))
        (PORT datad (620:620:620) (630:630:630))
        (IOPATH dataa combout (420:420:420) (444:444:444))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (481:481:481) (598:598:598))
        (PORT datab (457:457:457) (553:553:553))
        (PORT datac (384:384:384) (478:478:478))
        (PORT datad (618:618:618) (628:628:628))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (474:474:474) (590:590:590))
        (PORT datab (471:471:471) (569:569:569))
        (PORT datac (391:391:391) (485:485:485))
        (PORT datad (609:609:609) (618:618:618))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (477:477:477) (593:593:593))
        (PORT datab (466:466:466) (563:563:563))
        (PORT datac (388:388:388) (482:482:482))
        (PORT datad (613:613:613) (622:622:622))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (475:475:475) (591:591:591))
        (PORT datab (469:469:469) (566:566:566))
        (PORT datac (390:390:390) (484:484:484))
        (PORT datad (610:610:610) (619:619:619))
        (IOPATH dataa combout (435:435:435) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (473:473:473) (589:589:589))
        (PORT datab (472:472:472) (570:570:570))
        (PORT datac (392:392:392) (485:485:485))
        (PORT datad (608:608:608) (617:617:617))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (482:482:482) (598:598:598))
        (PORT datab (455:455:455) (552:552:552))
        (PORT datac (383:383:383) (477:477:477))
        (PORT datad (619:619:619) (628:628:628))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (474:474:474) (590:590:590))
        (PORT datab (470:470:470) (567:567:567))
        (PORT datac (390:390:390) (484:484:484))
        (PORT datad (609:609:609) (619:619:619))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (393:393:393) (431:431:431))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (477:477:477) (593:593:593))
        (PORT datab (464:464:464) (561:561:561))
        (PORT datac (388:388:388) (481:481:481))
        (PORT datad (613:613:613) (623:623:623))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (482:482:482) (599:599:599))
        (PORT datab (577:577:577) (570:570:570))
        (PORT datad (619:619:619) (629:629:629))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (655:655:655) (670:670:670))
        (PORT datab (580:580:580) (573:573:573))
        (PORT datad (422:422:422) (515:515:515))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (423:423:423) (391:391:391))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (475:475:475) (591:591:591))
        (PORT datab (581:581:581) (573:573:573))
        (PORT datad (424:424:424) (516:516:516))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (384:384:384) (386:386:386))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (480:480:480) (597:597:597))
        (PORT datab (458:458:458) (554:554:554))
        (PORT datad (617:617:617) (627:627:627))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (400:400:400) (391:391:391))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (752:752:752) (616:616:616))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (592:592:592) (541:541:541))
        (PORT datab (543:543:543) (458:458:458))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (802:802:802) (637:637:637))
        (PORT datab (315:315:315) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (758:758:758) (622:622:622))
        (PORT datab (533:533:533) (506:506:506))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (529:529:529) (513:513:513))
        (PORT datab (771:771:771) (623:623:623))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (543:543:543) (458:458:458))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (733:733:733) (588:588:588))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (757:757:757) (621:621:621))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (533:533:533) (512:512:512))
        (PORT datab (495:495:495) (439:439:439))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (488:488:488) (436:436:436))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (840:840:840) (727:727:727))
        (PORT datab (543:543:543) (454:454:454))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (489:489:489) (435:435:435))
        (PORT datab (525:525:525) (507:507:507))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1523:1523:1523) (1311:1311:1311))
        (PORT datab (494:494:494) (439:439:439))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (377:377:377))
        (PORT datab (540:540:540) (450:450:450))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (489:489:489) (435:435:435))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (318:318:318) (377:377:377))
        (PORT datab (540:540:540) (449:449:449))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1438:1438:1438) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2166:2166:2166) (2003:2003:2003))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (794:794:794) (627:627:627))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (864:864:864) (746:746:746))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1109:1109:1109) (939:939:939))
        (PORT datab (547:547:547) (460:460:460))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1189:1189:1189) (968:968:968))
        (PORT datab (539:539:539) (514:514:514))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (892:892:892) (759:759:759))
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (883:883:883) (749:749:749))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (548:548:548) (461:461:461))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1191:1191:1191) (970:970:970))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (989:989:989) (927:927:927))
        (PORT datab (981:981:981) (909:909:909))
        (PORT datac (901:901:901) (845:845:845))
        (PORT datad (1545:1545:1545) (1383:1383:1383))
        (IOPATH dataa combout (435:435:435) (419:419:419))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (487:487:487) (433:433:433))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (552:552:552) (541:541:541))
        (PORT datab (871:871:871) (752:752:752))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (988:988:988) (926:926:926))
        (PORT datab (981:981:981) (908:908:908))
        (PORT datac (901:901:901) (846:846:846))
        (PORT datad (1545:1545:1545) (1383:1383:1383))
        (IOPATH dataa combout (420:420:420) (371:371:371))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (735:735:735) (602:602:602))
        (PORT datab (363:363:363) (418:418:418))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1193:1193:1193) (972:972:972))
        (PORT datab (547:547:547) (542:542:542))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (896:896:896) (762:762:762))
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (980:980:980) (917:917:917))
        (PORT datab (976:976:976) (903:903:903))
        (PORT datac (905:905:905) (850:850:850))
        (PORT datad (1547:1547:1547) (1385:1385:1385))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (789:789:789) (627:627:627))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (987:987:987) (924:924:924))
        (PORT datab (980:980:980) (908:908:908))
        (PORT datac (902:902:902) (847:847:847))
        (PORT datad (1545:1545:1545) (1383:1383:1383))
        (IOPATH dataa combout (420:420:420) (371:371:371))
        (IOPATH datab combout (423:423:423) (380:380:380))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (554:554:554) (544:544:544))
        (PORT datab (541:541:541) (450:450:450))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (403:403:403))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2556:2556:2556) (2333:2333:2333))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1399:1399:1399) (1279:1279:1279))
        (PORT d[1] (1329:1329:1329) (1220:1220:1220))
        (PORT d[2] (1714:1714:1714) (1551:1551:1551))
        (PORT d[3] (1365:1365:1365) (1250:1250:1250))
        (PORT d[4] (1358:1358:1358) (1248:1248:1248))
        (PORT d[5] (1356:1356:1356) (1244:1244:1244))
        (PORT d[6] (1333:1333:1333) (1218:1218:1218))
        (PORT d[7] (2006:2006:2006) (1778:1778:1778))
        (PORT d[8] (1330:1330:1330) (1223:1223:1223))
        (PORT d[9] (1667:1667:1667) (1474:1474:1474))
        (PORT d[10] (2022:2022:2022) (1822:1822:1822))
        (PORT d[11] (1038:1038:1038) (978:978:978))
        (PORT clk (1819:1819:1819) (1884:1884:1884))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1819:1819:1819) (1884:1884:1884))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1820:1820:1820) (1885:1885:1885))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1774:1774:1774) (1837:1837:1837))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1477:1477:1477))
        (PORT asdata (1549:1549:1549) (1357:1357:1357))
        (PORT clrn (1500:1500:1500) (1426:1426:1426))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1171:1171:1171) (966:966:966))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1477:1477:1477))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1500:1500:1500) (1426:1426:1426))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (957:957:957) (897:897:897))
        (PORT d[1] (968:968:968) (915:915:915))
        (PORT d[2] (1708:1708:1708) (1545:1545:1545))
        (PORT d[3] (1033:1033:1033) (967:967:967))
        (PORT d[4] (957:957:957) (915:915:915))
        (PORT d[5] (994:994:994) (937:937:937))
        (PORT d[6] (954:954:954) (896:896:896))
        (PORT d[7] (2000:2000:2000) (1772:1772:1772))
        (PORT d[8] (945:945:945) (901:901:901))
        (PORT d[9] (956:956:956) (904:904:904))
        (PORT d[10] (924:924:924) (878:878:878))
        (PORT d[11] (951:951:951) (907:907:907))
        (PORT clk (1818:1818:1818) (1883:1883:1883))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1883:1883:1883))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1819:1819:1819) (1884:1884:1884))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1773:1773:1773) (1836:1836:1836))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (870:870:870))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT asdata (1802:1802:1802) (1570:1570:1570))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT asdata (1823:1823:1823) (1568:1568:1568))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2395:2395:2395) (2120:2120:2120))
        (PORT d[1] (2315:2315:2315) (2054:2054:2054))
        (PORT d[2] (2341:2341:2341) (2073:2073:2073))
        (PORT d[3] (1938:1938:1938) (1715:1715:1715))
        (PORT d[4] (2014:2014:2014) (1815:1815:1815))
        (PORT d[5] (2393:2393:2393) (2138:2138:2138))
        (PORT d[6] (2010:2010:2010) (1791:1791:1791))
        (PORT d[7] (2023:2023:2023) (1807:1807:1807))
        (PORT d[8] (1961:1961:1961) (1749:1749:1749))
        (PORT d[9] (2117:2117:2117) (1858:1858:1858))
        (PORT d[10] (1596:1596:1596) (1430:1430:1430))
        (PORT d[11] (2022:2022:2022) (1809:1809:1809))
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1830:1830:1830) (1895:1895:1895))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1784:1784:1784) (1847:1847:1847))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT asdata (2104:2104:2104) (1887:1887:1887))
        (PORT clrn (1493:1493:1493) (1420:1420:1420))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT asdata (2113:2113:2113) (1896:1896:1896))
        (PORT clrn (1493:1493:1493) (1420:1420:1420))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1692:1692:1692) (1534:1534:1534))
        (PORT d[1] (1341:1341:1341) (1232:1232:1232))
        (PORT d[2] (1373:1373:1373) (1255:1255:1255))
        (PORT d[3] (1660:1660:1660) (1498:1498:1498))
        (PORT d[4] (1351:1351:1351) (1251:1251:1251))
        (PORT d[5] (1393:1393:1393) (1281:1281:1281))
        (PORT d[6] (1386:1386:1386) (1271:1271:1271))
        (PORT d[7] (1333:1333:1333) (1230:1230:1230))
        (PORT d[8] (1296:1296:1296) (1202:1202:1202))
        (PORT d[9] (1583:1583:1583) (1421:1421:1421))
        (PORT d[10] (1305:1305:1305) (1215:1215:1215))
        (PORT d[11] (1345:1345:1345) (1239:1239:1239))
        (PORT clk (1820:1820:1820) (1885:1885:1885))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1820:1820:1820) (1885:1885:1885))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1821:1821:1821) (1886:1886:1886))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1775:1775:1775) (1838:1838:1838))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (848:848:848) (873:873:873))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (848:848:848) (873:873:873))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (848:848:848) (873:873:873))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT asdata (1824:1824:1824) (1569:1569:1569))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1467:1467:1467) (1178:1178:1178))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1480:1480:1480))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1503:1503:1503) (1431:1431:1431))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1329:1329:1329) (1231:1231:1231))
        (PORT d[1] (1620:1620:1620) (1465:1465:1465))
        (PORT d[2] (1346:1346:1346) (1226:1226:1226))
        (PORT d[3] (1312:1312:1312) (1199:1199:1199))
        (PORT d[4] (1323:1323:1323) (1219:1219:1219))
        (PORT d[5] (1300:1300:1300) (1208:1208:1208))
        (PORT d[6] (1343:1343:1343) (1231:1231:1231))
        (PORT d[7] (2061:2061:2061) (1818:1818:1818))
        (PORT d[8] (1288:1288:1288) (1192:1192:1192))
        (PORT d[9] (1570:1570:1570) (1408:1408:1408))
        (PORT d[10] (1322:1322:1322) (1218:1218:1218))
        (PORT d[11] (1293:1293:1293) (1197:1197:1197))
        (PORT clk (1805:1805:1805) (1868:1868:1868))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1805:1805:1805) (1868:1868:1868))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1806:1806:1806) (1869:1869:1869))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1760:1760:1760) (1821:1821:1821))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (832:832:832) (855:855:855))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (833:833:833) (856:856:856))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (833:833:833) (856:856:856))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (833:833:833) (856:856:856))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1483:1483:1483))
        (PORT asdata (1551:1551:1551) (1356:1356:1356))
        (PORT clrn (1506:1506:1506) (1433:1433:1433))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1483:1483:1483))
        (PORT asdata (1551:1551:1551) (1365:1365:1365))
        (PORT clrn (1506:1506:1506) (1433:1433:1433))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1984:1984:1984) (1763:1763:1763))
        (PORT d[1] (1942:1942:1942) (1724:1724:1724))
        (PORT d[2] (2015:2015:2015) (1780:1780:1780))
        (PORT d[3] (1992:1992:1992) (1774:1774:1774))
        (PORT d[4] (1685:1685:1685) (1518:1518:1518))
        (PORT d[5] (1590:1590:1590) (1435:1435:1435))
        (PORT d[6] (1634:1634:1634) (1459:1459:1459))
        (PORT d[7] (1636:1636:1636) (1451:1451:1451))
        (PORT d[8] (1682:1682:1682) (1512:1512:1512))
        (PORT d[9] (1683:1683:1683) (1481:1481:1481))
        (PORT d[10] (1615:1615:1615) (1448:1448:1448))
        (PORT d[11] (1614:1614:1614) (1463:1463:1463))
        (PORT clk (1836:1836:1836) (1899:1899:1899))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1836:1836:1836) (1899:1899:1899))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1837:1837:1837) (1900:1900:1900))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1791:1791:1791) (1852:1852:1852))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (863:863:863) (886:886:886))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (864:864:864) (887:887:887))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (864:864:864) (887:887:887))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (864:864:864) (887:887:887))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1436:1436:1436) (1475:1475:1475))
        (PORT asdata (2084:2084:2084) (1862:1862:1862))
        (PORT clrn (1498:1498:1498) (1428:1428:1428))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1619:1619:1619) (1413:1413:1413))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1436:1436:1436) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1428:1428:1428))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2824:2824:2824) (2475:2475:2475))
        (PORT d[1] (2310:2310:2310) (2048:2048:2048))
        (PORT d[2] (2341:2341:2341) (2068:2068:2068))
        (PORT d[3] (1617:1617:1617) (1428:1428:1428))
        (PORT d[4] (2051:2051:2051) (1842:1842:1842))
        (PORT d[5] (2684:2684:2684) (2385:2385:2385))
        (PORT d[6] (2004:2004:2004) (1785:1785:1785))
        (PORT d[7] (1233:1233:1233) (1123:1123:1123))
        (PORT d[8] (1628:1628:1628) (1469:1469:1469))
        (PORT d[9] (2037:2037:2037) (1793:1793:1793))
        (PORT d[10] (1636:1636:1636) (1458:1458:1458))
        (PORT d[11] (1292:1292:1292) (1178:1178:1178))
        (PORT clk (1828:1828:1828) (1893:1893:1893))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1828:1828:1828) (1893:1893:1893))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1783:1783:1783) (1846:1846:1846))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (880:880:880))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1484:1484:1484) (1269:1269:1269))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1548:1548:1548) (1303:1303:1303))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (537:537:537) (513:513:513))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT asdata (711:711:711) (776:776:776))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (286:286:286) (344:344:344))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (284:284:284) (342:342:342))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT asdata (923:923:923) (917:917:917))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (503:503:503) (492:492:492))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT asdata (987:987:987) (956:956:956))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT asdata (940:940:940) (931:931:931))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT asdata (988:988:988) (958:958:958))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (551:551:551) (527:527:527))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (551:551:551) (527:527:527))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (551:551:551) (527:527:527))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (PORT ena (1207:1207:1207) (1112:1112:1112))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (766:766:766) (688:688:688))
        (PORT dataa[1] (796:796:796) (714:714:714))
        (PORT dataa[2] (864:864:864) (776:776:776))
        (PORT dataa[3] (827:827:827) (752:752:752))
        (PORT dataa[4] (472:472:472) (460:460:460))
        (PORT dataa[5] (472:472:472) (447:447:447))
        (PORT dataa[6] (884:884:884) (829:829:829))
        (PORT dataa[7] (858:858:858) (801:801:801))
        (PORT dataa[8] (1149:1149:1149) (1025:1025:1025))
        (PORT dataa[9] (1167:1167:1167) (1045:1045:1045))
        (PORT dataa[10] (835:835:835) (758:758:758))
        (PORT dataa[11] (873:873:873) (801:801:801))
        (PORT dataa[12] (1126:1126:1126) (989:989:989))
        (PORT dataa[13] (1026:1026:1026) (1139:1139:1139))
        (PORT dataa[14] (903:903:903) (967:967:967))
        (PORT dataa[15] (912:912:912) (972:972:972))
        (PORT dataa[16] (1026:1026:1026) (1139:1139:1139))
        (PORT dataa[17] (903:903:903) (967:967:967))
        (PORT datab[6] (532:532:532) (528:528:528))
        (PORT datab[7] (768:768:768) (679:679:679))
        (PORT datab[8] (525:525:525) (515:515:515))
        (PORT datab[9] (780:780:780) (689:689:689))
        (PORT datab[10] (754:754:754) (678:678:678))
        (PORT datab[11] (532:532:532) (517:517:517))
        (PORT datab[12] (497:497:497) (492:492:492))
        (PORT datab[13] (528:528:528) (517:517:517))
        (PORT datab[14] (489:489:489) (485:485:485))
        (PORT datab[15] (510:510:510) (499:499:499))
        (PORT datab[16] (519:519:519) (533:533:533))
        (PORT datab[17] (488:488:488) (503:503:503))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (898:898:898) (839:839:839))
        (PORT dataa[7] (1146:1146:1146) (1018:1018:1018))
        (PORT dataa[8] (852:852:852) (798:798:798))
        (PORT dataa[9] (1180:1180:1180) (1030:1030:1030))
        (PORT dataa[10] (1164:1164:1164) (1023:1023:1023))
        (PORT dataa[11] (1222:1222:1222) (1091:1091:1091))
        (PORT dataa[12] (897:897:897) (845:845:845))
        (PORT dataa[13] (1290:1290:1290) (1159:1159:1159))
        (PORT dataa[14] (858:858:858) (808:808:808))
        (PORT dataa[15] (1196:1196:1196) (1048:1048:1048))
        (PORT dataa[16] (1036:1036:1036) (1174:1174:1174))
        (PORT dataa[17] (1022:1022:1022) (1153:1153:1153))
        (PORT datab[10] (725:725:725) (791:791:791))
        (PORT datab[11] (550:550:550) (551:551:551))
        (PORT datab[12] (559:559:559) (556:556:556))
        (PORT datab[13] (725:725:725) (791:791:791))
        (PORT datab[14] (550:550:550) (551:551:551))
        (PORT datab[15] (559:559:559) (556:556:556))
        (PORT datab[16] (725:725:725) (791:791:791))
        (PORT datab[17] (550:550:550) (551:551:551))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (702:702:702) (570:570:570))
        (PORT datab (1125:1125:1125) (909:909:909))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (810:810:810) (690:690:690))
        (PORT datab (426:426:426) (341:341:341))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1167:1167:1167) (957:957:957))
        (PORT datab (475:475:475) (367:367:367))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (728:728:728) (573:573:573))
        (PORT datab (1119:1119:1119) (922:922:922))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1167:1167:1167) (955:955:955))
        (PORT datab (475:475:475) (367:367:367))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (486:486:486) (379:379:379))
        (PORT datab (1209:1209:1209) (990:990:990))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1419:1419:1419) (1142:1142:1142))
        (PORT datab (474:474:474) (366:366:366))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (1198:1198:1198) (974:974:974))
        (PORT datad (416:416:416) (327:327:327))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
)
