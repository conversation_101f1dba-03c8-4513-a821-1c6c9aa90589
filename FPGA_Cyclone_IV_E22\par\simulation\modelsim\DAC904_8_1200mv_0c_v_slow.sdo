// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 03:38:22")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1158:1158:1158) (1153:1153:1153))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1896:1896:1896) (1544:1544:1544))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1833:1833:1833) (1611:1611:1611))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1417:1417:1417) (1216:1216:1216))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1840:1840:1840) (1581:1581:1581))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2114:2114:2114) (1782:1782:1782))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1740:1740:1740) (1452:1452:1452))
        (IOPATH i o (3985:3985:3985) (4026:4026:4026))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (802:802:802) (688:688:688))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (819:819:819) (690:690:690))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (829:829:829) (703:703:703))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (810:810:810) (681:681:681))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (817:817:817) (712:712:712))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (823:823:823) (701:701:701))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (785:785:785) (667:667:667))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (692:692:692) (816:816:816))
        (IOPATH i o (2696:2696:2696) (2717:2717:2717))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_spi_valid\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2260:2260:2260) (2006:2006:2006))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1681:1681:1681) (1707:1707:1707))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1957:1957:1957) (1762:1762:1762))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1999:1999:1999) (1783:1783:1783))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (3122:3122:3122) (3010:3010:3010))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2043:2043:2043) (1720:1720:1720))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1863:1863:1863) (1657:1657:1657))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1885:1885:1885) (1669:1669:1669))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1826:1826:1826) (1608:1608:1608))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2171:2171:2171) (1862:1862:1862))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2100:2100:2100) (1787:1787:1787))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1888:1888:1888) (1651:1651:1651))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1875:1875:1875) (1665:1665:1665))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2199:2199:2199) (1933:1933:1933))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2479:2479:2479) (2308:2308:2308))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2016:2016:2016) (2368:2368:2368))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1832:1832:1832) (2130:2130:2130))
        (IOPATH i o (2767:2767:2767) (2803:2803:2803))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1167:1167:1167) (1167:1167:1167))
        (PORT inclk[0] (2039:2039:2039) (2039:2039:2039))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2044:2044:2044) (2010:2010:2010))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE spi_sclk\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Add0\~0)
    (DELAY
      (ABSOLUTE
        (PORT datab (338:338:338) (399:399:399))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector21\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (280:280:280) (296:296:296))
        (PORT datab (337:337:337) (397:397:397))
        (PORT datad (353:353:353) (434:434:434))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_state\.SPI_RECEIVING)
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (547:547:547) (452:452:452))
        (PORT datab (381:381:381) (451:451:451))
        (PORT datad (355:355:355) (435:435:435))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Add0\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (331:331:331) (395:395:395))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector18\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (398:398:398) (482:482:482))
        (PORT datab (490:490:490) (419:419:419))
        (PORT datad (339:339:339) (412:412:412))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Add0\~4)
    (DELAY
      (ABSOLUTE
        (PORT datab (325:325:325) (382:382:382))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (396:396:396) (480:480:480))
        (PORT datab (269:269:269) (277:277:277))
        (PORT datad (339:339:339) (412:412:412))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Add0\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (329:329:329) (391:391:391))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (272:272:272) (284:284:284))
        (PORT datab (381:381:381) (451:451:451))
        (PORT datad (354:354:354) (435:435:435))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Add0\~8)
    (DELAY
      (ABSOLUTE
        (PORT datad (286:286:286) (345:345:345))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (397:397:397) (481:481:481))
        (PORT datab (270:270:270) (277:277:277))
        (PORT datad (339:339:339) (412:412:412))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector22\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (328:328:328) (391:391:391))
        (PORT datab (325:325:325) (382:382:382))
        (PORT datac (284:284:284) (351:351:351))
        (PORT datad (285:285:285) (344:344:344))
        (IOPATH dataa combout (377:377:377) (371:371:371))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_state\.SPI_COMPLETE\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (283:283:283) (299:299:299))
        (PORT datab (339:339:339) (400:400:400))
        (PORT datad (340:340:340) (412:412:412))
        (IOPATH dataa combout (349:349:349) (371:371:371))
        (IOPATH datab combout (354:354:354) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_state\.SPI_COMPLETE)
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT asdata (1395:1395:1395) (1570:1570:1570))
        (PORT clrn (4070:4070:4070) (4102:4102:4102))
        (PORT ena (1222:1222:1222) (1102:1102:1102))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (898:898:898) (849:849:849))
        (PORT datab (998:998:998) (925:925:925))
        (PORT datad (987:987:987) (916:916:916))
        (IOPATH dataa combout (377:377:377) (371:371:371))
        (IOPATH datab combout (423:423:423) (391:391:391))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4485:4485:4485) (4488:4488:4488))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1050:1050:1050) (960:960:960))
        (PORT datab (994:994:994) (920:920:920))
        (PORT datad (288:288:288) (347:347:347))
        (IOPATH dataa combout (377:377:377) (371:371:371))
        (IOPATH datab combout (423:423:423) (391:391:391))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4485:4485:4485) (4488:4488:4488))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector12\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1054:1054:1054) (965:965:965))
        (PORT datab (562:562:562) (534:534:534))
        (PORT datad (1583:1583:1583) (1398:1398:1398))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4485:4485:4485) (4488:4488:4488))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector11\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1049:1049:1049) (959:959:959))
        (PORT datab (993:993:993) (919:919:919))
        (PORT datad (287:287:287) (346:346:346))
        (IOPATH dataa combout (377:377:377) (371:371:371))
        (IOPATH datab combout (423:423:423) (391:391:391))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1437:1437:1437) (1476:1476:1476))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4485:4485:4485) (4488:4488:4488))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector10\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1040:1040:1040) (1016:1016:1016))
        (PORT datab (1064:1064:1064) (1019:1019:1019))
        (PORT datad (553:553:553) (526:526:526))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (899:899:899) (834:834:834))
        (PORT datab (1061:1061:1061) (1016:1016:1016))
        (PORT datad (995:995:995) (963:963:963))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (628:628:628) (644:644:644))
        (PORT datab (1058:1058:1058) (1012:1012:1012))
        (PORT datad (992:992:992) (959:959:959))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector7\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1045:1045:1045) (1020:1020:1020))
        (PORT datab (1068:1068:1068) (1024:1024:1024))
        (PORT datad (302:302:302) (358:358:358))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1050:1050:1050) (1026:1026:1026))
        (PORT datab (1072:1072:1072) (1030:1030:1030))
        (PORT datad (300:300:300) (356:356:356))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (404:404:404))
        (PORT datab (1069:1069:1069) (1026:1026:1026))
        (PORT datad (1004:1004:1004) (971:971:971))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1038:1038:1038) (1014:1014:1014))
        (PORT datab (1062:1062:1062) (1017:1017:1017))
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (407:407:407))
        (PORT datab (1068:1068:1068) (1025:1025:1025))
        (PORT datad (1003:1003:1003) (970:970:970))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (334:334:334) (397:397:397))
        (PORT datab (1057:1057:1057) (1011:1011:1011))
        (PORT datad (991:991:991) (958:958:958))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1048:1048:1048) (1024:1024:1024))
        (PORT datab (1071:1071:1071) (1028:1028:1028))
        (PORT datad (303:303:303) (359:359:359))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (380:380:380) (380:380:380))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d1\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (3862:3862:3862) (3900:3900:3900))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d1)
    (DELAY
      (ABSOLUTE
        (PORT clk (1446:1446:1446) (1490:1490:1490))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1426:1426:1426))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d2\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (285:285:285) (343:343:343))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d2)
    (DELAY
      (ABSOLUTE
        (PORT clk (1446:1446:1446) (1490:1490:1490))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1426:1426:1426))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[11\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1041:1041:1041) (1017:1017:1017))
        (PORT datab (318:318:318) (372:372:372))
        (PORT datad (284:284:284) (342:342:342))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1463:1463:1463) (1504:1504:1504))
        (PORT asdata (1737:1737:1737) (1723:1723:1723))
        (PORT clrn (1527:1527:1527) (1455:1455:1455))
        (PORT ena (1880:1880:1880) (1792:1792:1792))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[12\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datac (287:287:287) (355:355:355))
        (IOPATH datac combout (301:301:301) (283:283:283))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1463:1463:1463) (1504:1504:1504))
        (PORT asdata (1632:1632:1632) (1568:1568:1568))
        (PORT clrn (1527:1527:1527) (1455:1455:1455))
        (PORT ena (1880:1880:1880) (1792:1792:1792))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Selector0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (406:406:406))
        (PORT datab (1063:1063:1063) (1018:1018:1018))
        (PORT datad (997:997:997) (965:965:965))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1434:1434:1434) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (4510:4510:4510) (4497:4497:4497))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1463:1463:1463) (1504:1504:1504))
        (PORT asdata (1671:1671:1671) (1675:1675:1675))
        (PORT clrn (1527:1527:1527) (1455:1455:1455))
        (PORT ena (1880:1880:1880) (1792:1792:1792))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1463:1463:1463) (1504:1504:1504))
        (PORT asdata (1368:1368:1368) (1294:1294:1294))
        (PORT clrn (1527:1527:1527) (1455:1455:1455))
        (PORT ena (1880:1880:1880) (1792:1792:1792))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1157:1157:1157) (1156:1156:1156))
        (PORT datab (467:467:467) (564:564:564))
        (PORT datac (414:414:414) (515:515:515))
        (PORT datad (404:404:404) (495:495:495))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH datab combout (393:393:393) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1155:1155:1155) (1154:1154:1154))
        (PORT datab (474:474:474) (572:572:572))
        (PORT datac (408:408:408) (509:509:509))
        (PORT datad (410:410:410) (501:501:501))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1424:1424:1424) (1406:1406:1406))
        (PORT datab (1326:1326:1326) (1324:1324:1324))
        (PORT datac (1437:1437:1437) (1355:1355:1355))
        (PORT datad (1732:1732:1732) (1602:1602:1602))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1155:1155:1155) (1154:1154:1154))
        (PORT datab (475:475:475) (573:573:573))
        (PORT datac (406:406:406) (507:507:507))
        (PORT datad (411:411:411) (502:502:502))
        (IOPATH dataa combout (420:420:420) (444:444:444))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1158:1158:1158) (1157:1157:1157))
        (PORT datab (464:464:464) (560:560:560))
        (PORT datac (417:417:417) (519:519:519))
        (PORT datad (402:402:402) (492:492:492))
        (IOPATH dataa combout (420:420:420) (450:450:450))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1156:1156:1156) (1155:1155:1155))
        (PORT datab (472:472:472) (570:570:570))
        (PORT datac (409:409:409) (510:510:510))
        (PORT datad (408:408:408) (500:500:500))
        (IOPATH dataa combout (420:420:420) (444:444:444))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1157:1157:1157) (1156:1156:1156))
        (PORT datab (469:469:469) (565:565:565))
        (PORT datac (413:413:413) (514:514:514))
        (PORT datad (405:405:405) (496:496:496))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH datab combout (393:393:393) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1159:1159:1159) (1158:1158:1158))
        (PORT datab (462:462:462) (559:559:559))
        (PORT datac (419:419:419) (521:521:521))
        (PORT datad (401:401:401) (491:491:491))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1156:1156:1156) (1155:1155:1155))
        (PORT datab (471:471:471) (569:569:569))
        (PORT datac (410:410:410) (511:511:511))
        (PORT datad (407:407:407) (498:498:498))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1159:1159:1159) (1159:1159:1159))
        (PORT datab (460:460:460) (556:556:556))
        (PORT datac (422:422:422) (523:523:523))
        (PORT datad (399:399:399) (489:489:489))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH datab combout (393:393:393) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1158:1158:1158) (1158:1158:1158))
        (PORT datab (461:461:461) (557:557:557))
        (PORT datac (420:420:420) (522:522:522))
        (PORT datad (399:399:399) (490:490:490))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1160:1160:1160) (1159:1159:1159))
        (PORT datab (457:457:457) (553:553:553))
        (PORT datac (424:424:424) (526:526:526))
        (PORT datad (396:396:396) (487:487:487))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1159:1159:1159) (1159:1159:1159))
        (PORT datab (458:458:458) (554:554:554))
        (PORT datac (423:423:423) (525:525:525))
        (PORT datad (397:397:397) (488:488:488))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (431:431:431))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (460:460:460) (560:560:560))
        (PORT datab (470:470:470) (567:567:567))
        (PORT datad (406:406:406) (497:497:497))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (474:474:474) (576:576:576))
        (PORT datab (455:455:455) (551:551:551))
        (PORT datad (308:308:308) (368:368:368))
        (IOPATH dataa combout (420:420:420) (400:400:400))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (475:475:475) (577:577:577))
        (PORT datab (348:348:348) (407:407:407))
        (PORT datad (394:394:394) (484:484:484))
        (IOPATH dataa combout (373:373:373) (380:380:380))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1154:1154:1154) (1153:1153:1153))
        (PORT datab (476:476:476) (574:574:574))
        (PORT datad (411:411:411) (503:503:503))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (554:554:554) (465:465:465))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (595:595:595) (545:545:545))
        (PORT datab (544:544:544) (459:459:459))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (485:485:485) (430:430:430))
        (PORT datab (315:315:315) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1000:1000:1000) (902:902:902))
        (PORT datab (534:534:534) (506:506:506))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (483:483:483) (425:425:425))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (490:490:490) (441:441:441))
        (PORT datab (534:534:534) (506:506:506))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (935:935:935) (853:853:853))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1000:1000:1000) (902:902:902))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (807:807:807) (678:678:678))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (875:875:875) (775:775:775))
        (PORT datab (486:486:486) (430:430:430))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (534:534:534) (513:513:513))
        (PORT datab (539:539:539) (449:449:449))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (536:536:536) (443:443:443))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (807:807:807) (678:678:678))
        (PORT datab (316:316:316) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (377:377:377))
        (PORT datab (539:539:539) (449:449:449))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (537:537:537) (518:518:518))
        (PORT datab (536:536:536) (443:443:443))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (318:318:318) (377:377:377))
        (PORT datab (535:535:535) (441:441:441))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (2105:2105:2105) (1939:1939:1939))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1160:1160:1160) (993:993:993))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (893:893:893) (758:758:758))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (854:854:854) (771:771:771))
        (PORT datab (546:546:546) (458:458:458))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (897:897:897) (766:766:766))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1276:1276:1276) (1060:1060:1060))
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (904:904:904) (773:773:773))
        (PORT datab (548:548:548) (528:528:528))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (546:546:546) (459:459:459))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (854:854:854) (777:777:777))
        (PORT datab (897:897:897) (767:767:767))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1425:1425:1425) (1407:1407:1407))
        (PORT datab (1327:1327:1327) (1325:1325:1325))
        (PORT datac (1439:1439:1439) (1357:1357:1357))
        (PORT datad (1733:1733:1733) (1603:1603:1603))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (487:487:487) (432:432:432))
        (PORT datab (363:363:363) (418:418:418))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (564:564:564) (558:558:558))
        (PORT datab (877:877:877) (762:762:762))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1423:1423:1423) (1405:1405:1405))
        (PORT datab (1326:1326:1326) (1325:1325:1325))
        (PORT datac (1436:1436:1436) (1354:1354:1354))
        (PORT datad (1731:1731:1731) (1601:1601:1601))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (557:557:557) (538:538:538))
        (PORT datab (539:539:539) (449:449:449))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (365:365:365) (425:425:425))
        (PORT datab (897:897:897) (767:767:767))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1280:1280:1280) (1064:1064:1064))
        (PORT datab (363:363:363) (418:418:418))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1425:1425:1425) (1407:1407:1407))
        (PORT datab (1327:1327:1327) (1325:1325:1325))
        (PORT datac (1438:1438:1438) (1356:1356:1356))
        (PORT datad (1732:1732:1732) (1603:1603:1603))
        (IOPATH dataa combout (349:349:349) (371:371:371))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (487:487:487) (433:433:433))
        (PORT datab (1655:1655:1655) (1456:1456:1456))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1417:1417:1417) (1398:1398:1398))
        (PORT datab (1329:1329:1329) (1328:1328:1328))
        (PORT datac (1433:1433:1433) (1351:1351:1351))
        (PORT datad (1728:1728:1728) (1597:1597:1597))
        (IOPATH dataa combout (373:373:373) (380:380:380))
        (IOPATH datab combout (423:423:423) (391:391:391))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (557:557:557) (548:548:548))
        (PORT datab (538:538:538) (445:445:445))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (403:403:403))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1464:1464:1464) (1505:1505:1505))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1888:1888:1888) (1773:1773:1773))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (949:949:949) (882:882:882))
        (PORT d[1] (920:920:920) (872:872:872))
        (PORT d[2] (950:950:950) (887:887:887))
        (PORT d[3] (950:950:950) (896:896:896))
        (PORT d[4] (1376:1376:1376) (1270:1270:1270))
        (PORT d[5] (1653:1653:1653) (1497:1497:1497))
        (PORT d[6] (2077:2077:2077) (1830:1830:1830))
        (PORT d[7] (954:954:954) (898:898:898))
        (PORT d[8] (1005:1005:1005) (938:938:938))
        (PORT d[9] (1689:1689:1689) (1519:1519:1519))
        (PORT d[10] (924:924:924) (881:881:881))
        (PORT d[11] (1789:1789:1789) (1608:1608:1608))
        (PORT clk (1830:1830:1830) (1895:1895:1895))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1830:1830:1830) (1895:1895:1895))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1831:1831:1831) (1896:1896:1896))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1785:1785:1785) (1848:1848:1848))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (858:858:858) (883:883:883))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (858:858:858) (883:883:883))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (858:858:858) (883:883:883))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1226:1226:1226) (1025:1025:1025))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1505:1505:1505) (1433:1433:1433))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1151:1151:1151) (972:972:972))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1505:1505:1505) (1433:1433:1433))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1349:1349:1349) (1240:1240:1240))
        (PORT d[1] (1303:1303:1303) (1199:1199:1199))
        (PORT d[2] (1353:1353:1353) (1243:1243:1243))
        (PORT d[3] (1330:1330:1330) (1222:1222:1222))
        (PORT d[4] (1024:1024:1024) (965:965:965))
        (PORT d[5] (998:998:998) (939:939:939))
        (PORT d[6] (2067:2067:2067) (1935:1935:1935))
        (PORT d[7] (968:968:968) (916:916:916))
        (PORT d[8] (971:971:971) (919:919:919))
        (PORT d[9] (1663:1663:1663) (1495:1495:1495))
        (PORT d[10] (1002:1002:1002) (951:951:951))
        (PORT d[11] (970:970:970) (923:923:923))
        (PORT clk (1830:1830:1830) (1894:1894:1894))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1830:1830:1830) (1894:1894:1894))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1831:1831:1831) (1895:1895:1895))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1785:1785:1785) (1847:1847:1847))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (858:858:858) (882:882:882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (858:858:858) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (858:858:858) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (944:944:944) (819:819:819))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1448:1448:1448) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1514:1514:1514) (1440:1440:1440))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (897:897:897) (781:781:781))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1448:1448:1448) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1514:1514:1514) (1440:1440:1440))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2372:2372:2372) (2088:2088:2088))
        (PORT d[1] (3058:3058:3058) (2638:2638:2638))
        (PORT d[2] (2499:2499:2499) (2154:2154:2154))
        (PORT d[3] (2642:2642:2642) (2289:2289:2289))
        (PORT d[4] (3057:3057:3057) (2619:2619:2619))
        (PORT d[5] (1640:1640:1640) (1448:1448:1448))
        (PORT d[6] (1591:1591:1591) (1409:1409:1409))
        (PORT d[7] (2746:2746:2746) (2378:2378:2378))
        (PORT d[8] (1591:1591:1591) (1407:1407:1407))
        (PORT d[9] (3059:3059:3059) (2666:2666:2666))
        (PORT d[10] (1649:1649:1649) (1512:1512:1512))
        (PORT d[11] (2661:2661:2661) (2324:2324:2324))
        (PORT clk (1821:1821:1821) (1885:1885:1885))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1821:1821:1821) (1885:1885:1885))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1822:1822:1822) (1886:1886:1886))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1776:1776:1776) (1838:1838:1838))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (848:848:848) (872:872:872))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (849:849:849) (873:873:873))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (849:849:849) (873:873:873))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (849:849:849) (873:873:873))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (793:793:793) (630:630:630))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1506:1506:1506) (1433:1433:1433))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (793:793:793) (630:630:630))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1506:1506:1506) (1433:1433:1433))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1961:1961:1961) (1746:1746:1746))
        (PORT d[1] (1934:1934:1934) (1687:1687:1687))
        (PORT d[2] (1858:1858:1858) (1639:1639:1639))
        (PORT d[3] (1966:1966:1966) (1732:1732:1732))
        (PORT d[4] (2003:2003:2003) (1769:1769:1769))
        (PORT d[5] (1914:1914:1914) (1713:1713:1713))
        (PORT d[6] (1953:1953:1953) (1727:1727:1727))
        (PORT d[7] (2013:2013:2013) (1770:1770:1770))
        (PORT d[8] (1964:1964:1964) (1740:1740:1740))
        (PORT d[9] (1962:1962:1962) (1732:1732:1732))
        (PORT d[10] (2064:2064:2064) (1868:1868:1868))
        (PORT d[11] (2255:2255:2255) (1971:1971:1971))
        (PORT clk (1818:1818:1818) (1883:1883:1883))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1883:1883:1883))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1819:1819:1819) (1884:1884:1884))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1773:1773:1773) (1836:1836:1836))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (870:870:870))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (871:871:871))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1182:1182:1182) (963:963:963))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1454:1454:1454) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1507:1507:1507) (1434:1434:1434))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1125:1125:1125) (934:934:934))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1454:1454:1454) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1507:1507:1507) (1434:1434:1434))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1283:1283:1283) (1187:1187:1187))
        (PORT d[1] (1321:1321:1321) (1221:1221:1221))
        (PORT d[2] (1369:1369:1369) (1260:1260:1260))
        (PORT d[3] (1375:1375:1375) (1257:1257:1257))
        (PORT d[4] (994:994:994) (946:946:946))
        (PORT d[5] (968:968:968) (920:920:920))
        (PORT d[6] (2112:2112:2112) (1989:1989:1989))
        (PORT d[7] (1023:1023:1023) (960:960:960))
        (PORT d[8] (1025:1025:1025) (964:964:964))
        (PORT d[9] (1267:1267:1267) (1168:1168:1168))
        (PORT d[10] (1003:1003:1003) (959:959:959))
        (PORT d[11] (1027:1027:1027) (974:974:974))
        (PORT clk (1817:1817:1817) (1879:1879:1879))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1817:1817:1817) (1879:1879:1879))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1880:1880:1880))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1772:1772:1772) (1832:1832:1832))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (866:866:866))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (867:867:867))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1575:1575:1575) (1347:1347:1347))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1504:1504:1504) (1432:1432:1432))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1494:1494:1494))
        (PORT asdata (1952:1952:1952) (1737:1737:1737))
        (PORT clrn (1504:1504:1504) (1432:1432:1432))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2715:2715:2715) (2375:2375:2375))
        (PORT d[1] (2630:2630:2630) (2283:2283:2283))
        (PORT d[2] (2557:2557:2557) (2220:2220:2220))
        (PORT d[3] (2686:2686:2686) (2334:2334:2334))
        (PORT d[4] (2704:2704:2704) (2362:2362:2362))
        (PORT d[5] (1898:1898:1898) (1694:1694:1694))
        (PORT d[6] (1899:1899:1899) (1680:1680:1680))
        (PORT d[7] (2694:2694:2694) (2341:2341:2341))
        (PORT d[8] (2301:2301:2301) (1998:1998:1998))
        (PORT d[9] (2717:2717:2717) (2369:2369:2369))
        (PORT d[10] (2004:2004:2004) (1818:1818:1818))
        (PORT d[11] (2655:2655:2655) (2318:2318:2318))
        (PORT clk (1820:1820:1820) (1885:1885:1885))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1820:1820:1820) (1885:1885:1885))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1821:1821:1821) (1886:1886:1886))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1775:1775:1775) (1838:1838:1838))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (847:847:847) (872:872:872))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (848:848:848) (873:873:873))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (848:848:848) (873:873:873))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (848:848:848) (873:873:873))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1142:1142:1142) (932:932:932))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1498:1498:1498))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1507:1507:1507) (1435:1435:1435))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1118:1118:1118) (925:925:925))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1455:1455:1455) (1498:1498:1498))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1507:1507:1507) (1435:1435:1435))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1342:1342:1342) (1240:1240:1240))
        (PORT d[1] (1317:1317:1317) (1216:1216:1216))
        (PORT d[2] (1371:1371:1371) (1262:1262:1262))
        (PORT d[3] (1288:1288:1288) (1179:1179:1179))
        (PORT d[4] (1042:1042:1042) (985:985:985))
        (PORT d[5] (1016:1016:1016) (960:960:960))
        (PORT d[6] (2021:2021:2021) (1780:1780:1780))
        (PORT d[7] (1364:1364:1364) (1260:1260:1260))
        (PORT d[8] (988:988:988) (937:937:937))
        (PORT d[9] (1661:1661:1661) (1483:1483:1483))
        (PORT d[10] (978:978:978) (939:939:939))
        (PORT d[11] (1065:1065:1065) (1008:1008:1008))
        (PORT clk (1828:1828:1828) (1893:1893:1893))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1828:1828:1828) (1893:1893:1893))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1783:1783:1783) (1846:1846:1846))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (880:880:880))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1133:1133:1133) (964:964:964))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1492:1492:1492))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1501:1501:1501) (1429:1429:1429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1143:1143:1143) (979:979:979))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1492:1492:1492))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1501:1501:1501) (1429:1429:1429))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (856:856:856) (804:804:804))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (289:289:289) (348:348:348))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (303:303:303) (359:359:359))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (286:286:286) (344:344:344))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT asdata (723:723:723) (785:785:785))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (497:497:497) (475:475:475))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (502:502:502) (483:483:483))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT asdata (1234:1234:1234) (1143:1143:1143))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (541:541:541) (509:509:509))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (506:506:506) (485:485:485))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~2)
    (DELAY
      (ABSOLUTE
        (PORT datad (544:544:544) (513:513:513))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[11\]\~3)
    (DELAY
      (ABSOLUTE
        (PORT datad (539:539:539) (509:509:509))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1449:1449:1449) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1499:1499:1499) (1429:1429:1429))
        (PORT ena (1638:1638:1638) (1516:1516:1516))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (805:805:805) (697:697:697))
        (PORT dataa[1] (756:756:756) (676:676:676))
        (PORT dataa[2] (751:751:751) (668:668:668))
        (PORT dataa[3] (859:859:859) (774:774:774))
        (PORT dataa[4] (758:758:758) (675:675:675))
        (PORT dataa[5] (754:754:754) (673:673:673))
        (PORT dataa[6] (778:778:778) (695:695:695))
        (PORT dataa[7] (791:791:791) (690:690:690))
        (PORT dataa[8] (477:477:477) (464:464:464))
        (PORT dataa[9] (489:489:489) (477:477:477))
        (PORT dataa[10] (782:782:782) (694:694:694))
        (PORT dataa[11] (804:804:804) (705:705:705))
        (PORT dataa[12] (831:831:831) (773:773:773))
        (PORT dataa[13] (833:833:833) (879:879:879))
        (PORT dataa[14] (858:858:858) (907:907:907))
        (PORT dataa[15] (842:842:842) (885:885:885))
        (PORT dataa[16] (833:833:833) (879:879:879))
        (PORT dataa[17] (858:858:858) (907:907:907))
        (PORT datab[6] (490:490:490) (483:483:483))
        (PORT datab[7] (507:507:507) (494:494:494))
        (PORT datab[8] (477:477:477) (464:464:464))
        (PORT datab[9] (490:490:490) (475:475:475))
        (PORT datab[10] (524:524:524) (507:507:507))
        (PORT datab[11] (480:480:480) (470:470:470))
        (PORT datab[12] (490:490:490) (489:489:489))
        (PORT datab[13] (518:518:518) (507:507:507))
        (PORT datab[14] (494:494:494) (478:478:478))
        (PORT datab[15] (488:488:488) (485:485:485))
        (PORT datab[16] (495:495:495) (507:507:507))
        (PORT datab[17] (726:726:726) (800:800:800))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (852:852:852) (802:802:802))
        (PORT dataa[7] (864:864:864) (807:807:807))
        (PORT dataa[8] (913:913:913) (843:843:843))
        (PORT dataa[9] (892:892:892) (816:816:816))
        (PORT dataa[10] (1190:1190:1190) (1072:1072:1072))
        (PORT dataa[11] (836:836:836) (779:779:779))
        (PORT dataa[12] (884:884:884) (823:823:823))
        (PORT dataa[13] (878:878:878) (824:824:824))
        (PORT dataa[14] (1213:1213:1213) (1077:1077:1077))
        (PORT dataa[15] (829:829:829) (777:777:777))
        (PORT dataa[16] (811:811:811) (868:868:868))
        (PORT dataa[17] (1044:1044:1044) (1185:1185:1185))
        (PORT datab[10] (564:564:564) (557:557:557))
        (PORT datab[11] (746:746:746) (843:843:843))
        (PORT datab[12] (572:572:572) (562:562:562))
        (PORT datab[13] (564:564:564) (557:557:557))
        (PORT datab[14] (746:746:746) (843:843:843))
        (PORT datab[15] (572:572:572) (562:562:562))
        (PORT datab[16] (564:564:564) (557:557:557))
        (PORT datab[17] (746:746:746) (843:843:843))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1365:1365:1365) (1074:1074:1074))
        (PORT datab (1981:1981:1981) (1614:1614:1614))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1307:1307:1307) (1051:1051:1051))
        (PORT datab (1393:1393:1393) (1137:1137:1137))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (2012:2012:2012) (1558:1558:1558))
        (PORT datab (1582:1582:1582) (1276:1276:1276))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1595:1595:1595) (1276:1276:1276))
        (PORT datab (1692:1692:1692) (1359:1359:1359))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1474:1474:1474) (1279:1279:1279))
        (PORT datab (1680:1680:1680) (1325:1325:1325))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1556:1556:1556) (1304:1304:1304))
        (PORT datab (1677:1677:1677) (1316:1316:1316))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1694:1694:1694) (1336:1336:1336))
        (PORT datab (1633:1633:1633) (1329:1329:1329))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (1249:1249:1249) (1009:1009:1009))
        (PORT datad (1621:1621:1621) (1318:1318:1318))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_valid)
    (DELAY
      (ABSOLUTE
        (PORT clk (1446:1446:1446) (1490:1490:1490))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1426:1426:1426))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
)
