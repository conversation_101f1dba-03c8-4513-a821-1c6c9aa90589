/*
 * MSPM0G3507 SPI配置头文件
 * 这个文件通常由SysConfig工具自动生成
 * 这里提供手动配置的关键定义
 */

#ifndef TI_MSP_DL_CONFIG_H
#define TI_MSP_DL_CONFIG_H

#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

// SPI实例定义
#define SPI_0_INST                          SPI0
#define GPIO_GRP_0_PORT                     GPIOA
#define GPIO_GRP_0_CS_PIN                   DL_GPIO_PIN_4

// SPI时钟配置结构体
extern const DL_SPI_ClockConfig gSPI_0ClockConfig;

// 函数声明
void SYSCFG_DL_init(void);

#endif /* TI_MSP_DL_CONFIG_H */
