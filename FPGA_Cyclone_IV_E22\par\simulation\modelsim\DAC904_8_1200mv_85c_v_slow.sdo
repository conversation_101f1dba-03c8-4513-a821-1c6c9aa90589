// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 85 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 03:12:55")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1288:1288:1288) (1302:1302:1302))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1769:1769:1769) (1634:1634:1634))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2152:2152:2152) (2009:2009:2009))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2044:2044:2044) (1883:1883:1883))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2011:2011:2011) (1858:1858:1858))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2047:2047:2047) (1872:1872:1872))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1535:1535:1535) (1477:1477:1477))
        (IOPATH i o (4518:4518:4518) (4585:4585:4585))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1748:1748:1748) (1606:1606:1606))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2123:2123:2123) (1900:1900:1900))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2212:2212:2212) (2041:2041:2041))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1759:1759:1759) (1610:1610:1610))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1878:1878:1878) (1751:1751:1751))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1324:1324:1324) (1163:1163:1163))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1317:1317:1317) (1158:1158:1158))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1141:1141:1141) (1305:1305:1305))
        (IOPATH i o (3029:3029:3029) (3068:3068:3068))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_spi_valid\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1117:1117:1117) (1054:1054:1054))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2102:2102:2102) (2092:2092:2092))
        (IOPATH i o (3105:3105:3105) (3157:3157:3157))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1243:1243:1243) (1202:1202:1202))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1964:1964:1964) (1974:1974:1974))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1212:1212:1212) (1167:1167:1167))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2637:2637:2637) (2485:2485:2485))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1846:1846:1846) (1725:1725:1725))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1480:1480:1480) (1386:1386:1386))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1530:1530:1530) (1443:1443:1443))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1508:1508:1508) (1447:1447:1447))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1455:1455:1455) (1341:1341:1341))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1924:1924:1924) (1847:1847:1847))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1162:1162:1162) (1106:1106:1106))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1580:1580:1580) (1508:1508:1508))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1902:1902:1902) (1802:1802:1802))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1589:1589:1589) (1730:1730:1730))
        (IOPATH i o (3105:3105:3105) (3157:3157:3157))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1492:1492:1492) (1584:1584:1584))
        (IOPATH i o (3105:3105:3105) (3157:3157:3157))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (222:222:222) (208:208:208))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1313:1313:1313) (1313:1313:1313))
        (PORT inclk[0] (2313:2313:2313) (2313:2313:2313))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2338:2338:2338) (2305:2305:2305))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE spi_sclk\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (222:222:222) (208:208:208))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT asdata (2014:2014:2014) (2124:2124:2124))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (306:306:306) (380:380:380))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (304:304:304) (378:378:378))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (304:304:304) (378:378:378))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (304:304:304) (378:378:378))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT asdata (770:770:770) (845:845:845))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (378:378:378))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT asdata (769:769:769) (842:842:842))
        (PORT ena (5335:5335:5335) (5083:5083:5083))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT asdata (1379:1379:1379) (1363:1363:1363))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[10\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (378:378:378))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (304:304:304) (378:378:378))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[13\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datac (321:321:321) (399:399:399))
        (IOPATH datac combout (324:324:324) (315:315:315))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d1\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (4246:4246:4246) (4462:4462:4462))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d1)
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1633:1633:1633))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1663:1663:1663) (1613:1613:1613))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d2)
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1633:1633:1633))
        (PORT asdata (767:767:767) (841:841:841))
        (PORT clrn (1663:1663:1663) (1613:1613:1613))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[0\]\~3)
    (DELAY
      (ABSOLUTE
        (IOPATH datac combout (462:462:462) (482:482:482))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1633:1633:1633))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (4953:4953:4953) (5132:5132:5132))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[1\]\~2)
    (DELAY
      (ABSOLUTE
        (PORT datad (319:319:319) (410:410:410))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1633:1633:1633))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (4953:4953:4953) (5132:5132:5132))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[2\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT dataa (356:356:356) (453:453:453))
        (PORT datab (362:362:362) (455:455:455))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (462:462:462) (482:482:482))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1633:1633:1633))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (4953:4953:4953) (5132:5132:5132))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_bit_cnt\[3\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (355:355:355) (451:451:451))
        (PORT datab (360:360:360) (453:453:453))
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH dataa combout (420:420:420) (428:428:428))
        (IOPATH datab combout (432:432:432) (433:433:433))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_bit_cnt\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1633:1633:1633))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (4953:4953:4953) (5132:5132:5132))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (352:352:352) (448:448:448))
        (PORT datab (358:358:358) (449:449:449))
        (PORT datac (292:292:292) (369:369:369))
        (PORT datad (303:303:303) (376:376:376))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always2\~1)
    (DELAY
      (ABSOLUTE
        (PORT datab (333:333:333) (409:409:409))
        (PORT datac (301:301:301) (383:383:383))
        (PORT datad (238:238:238) (256:256:256))
        (IOPATH datab combout (407:407:407) (408:408:408))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT asdata (2841:2841:2841) (2797:2797:2797))
        (PORT clrn (1677:1677:1677) (1629:1629:1629))
        (PORT ena (3421:3421:3421) (3341:3341:3341))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT asdata (2536:2536:2536) (2425:2425:2425))
        (PORT clrn (1677:1677:1677) (1629:1629:1629))
        (PORT ena (3421:3421:3421) (3341:3341:3341))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[14\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (881:881:881) (854:854:854))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT asdata (999:999:999) (1012:1012:1012))
        (PORT ena (5679:5679:5679) (5475:5475:5475))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT asdata (2413:2413:2413) (2325:2325:2325))
        (PORT clrn (1677:1677:1677) (1629:1629:1629))
        (PORT ena (3421:3421:3421) (3341:3341:3341))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT asdata (2513:2513:2513) (2399:2399:2399))
        (PORT clrn (1677:1677:1677) (1629:1629:1629))
        (PORT ena (3421:3421:3421) (3341:3341:3341))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (477:477:477) (648:648:648))
        (PORT datab (473:473:473) (632:632:632))
        (PORT datac (433:433:433) (576:576:576))
        (PORT datad (437:437:437) (547:547:547))
        (IOPATH dataa combout (438:438:438) (448:448:448))
        (IOPATH datab combout (440:440:440) (462:462:462))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1061:1061:1061) (1063:1063:1063))
        (PORT datab (1391:1391:1391) (1473:1473:1473))
        (PORT datac (959:959:959) (960:960:960))
        (PORT datad (1389:1389:1389) (1453:1453:1453))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (479:479:479) (652:652:652))
        (PORT datab (470:470:470) (628:628:628))
        (PORT datac (432:432:432) (575:575:575))
        (PORT datad (439:439:439) (548:548:548))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH datab combout (455:455:455) (424:424:424))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (487:487:487) (662:662:662))
        (PORT datab (461:461:461) (616:616:616))
        (PORT datac (431:431:431) (574:574:574))
        (PORT datad (443:443:443) (552:552:552))
        (IOPATH dataa combout (453:453:453) (413:413:413))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (486:486:486) (660:660:660))
        (PORT datab (462:462:462) (617:617:617))
        (PORT datac (431:431:431) (573:573:573))
        (PORT datad (442:442:442) (552:552:552))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (480:480:480) (653:653:653))
        (PORT datab (470:470:470) (626:626:626))
        (PORT datac (432:432:432) (575:575:575))
        (PORT datad (439:439:439) (549:549:549))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (488:488:488) (664:664:664))
        (PORT datab (461:461:461) (615:615:615))
        (PORT datac (431:431:431) (573:573:573))
        (PORT datad (443:443:443) (553:553:553))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (484:484:484) (657:657:657))
        (PORT datab (466:466:466) (621:621:621))
        (PORT datac (432:432:432) (574:574:574))
        (PORT datad (441:441:441) (551:551:551))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (474:474:474) (645:645:645))
        (PORT datab (477:477:477) (636:636:636))
        (PORT datac (433:433:433) (577:577:577))
        (PORT datad (436:436:436) (545:545:545))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH datab combout (455:455:455) (436:436:436))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (485:485:485) (660:660:660))
        (PORT datab (464:464:464) (619:619:619))
        (PORT datac (431:431:431) (574:574:574))
        (PORT datad (442:442:442) (551:551:551))
        (IOPATH dataa combout (432:432:432) (446:446:446))
        (IOPATH datab combout (440:440:440) (462:462:462))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (484:484:484) (658:658:658))
        (PORT datab (464:464:464) (620:620:620))
        (PORT datac (431:431:431) (574:574:574))
        (PORT datad (441:441:441) (551:551:551))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (475:475:475) (646:646:646))
        (PORT datab (476:476:476) (635:635:635))
        (PORT datac (433:433:433) (576:576:576))
        (PORT datad (436:436:436) (546:546:546))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (478:478:478) (649:649:649))
        (PORT datab (472:472:472) (630:630:630))
        (PORT datac (432:432:432) (575:575:575))
        (PORT datad (438:438:438) (547:547:547))
        (IOPATH dataa combout (438:438:438) (448:448:448))
        (IOPATH datab combout (440:440:440) (462:462:462))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (478:478:478) (650:650:650))
        (PORT datab (480:480:480) (625:625:625))
        (PORT datad (427:427:427) (575:575:575))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1103:1103:1103) (1139:1139:1139))
        (PORT datab (481:481:481) (626:626:626))
        (PORT datad (437:437:437) (546:546:546))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (455:455:455) (436:436:436))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (489:489:489) (664:664:664))
        (PORT datab (479:479:479) (622:622:622))
        (PORT datad (444:444:444) (553:553:553))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (435:435:435) (424:424:424))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (482:482:482) (655:655:655))
        (PORT datab (466:466:466) (622:622:622))
        (PORT datad (440:440:440) (550:550:550))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1055:1055:1055) (1028:1028:1028))
        (PORT datab (331:331:331) (406:406:406))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (604:604:604) (615:615:615))
        (PORT datab (550:550:550) (515:515:515))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (977:977:977) (965:965:965))
        (PORT datab (331:331:331) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (417:417:417))
        (PORT datab (497:497:497) (487:487:487))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (488:488:488) (476:476:476))
        (PORT datab (332:332:332) (408:408:408))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (552:552:552) (517:517:517))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (487:487:487) (468:468:468))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (549:549:549) (574:574:574))
        (PORT datab (498:498:498) (488:488:488))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (545:545:545) (574:574:574))
        (PORT datab (559:559:559) (528:528:528))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (561:561:561) (531:531:531))
        (PORT datab (333:333:333) (409:409:409))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (546:546:546) (575:575:575))
        (PORT datab (543:543:543) (506:506:506))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (554:554:554) (514:514:514))
        (PORT datab (535:535:535) (570:570:570))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (546:546:546) (576:576:576))
        (PORT datab (558:558:558) (527:527:527))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (544:544:544) (507:507:507))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (495:495:495) (488:488:488))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (334:334:334) (417:417:417))
        (PORT datab (484:484:484) (465:465:465))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1615:1615:1615) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1916:1916:1916) (1873:1873:1873))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1466:1466:1466) (1401:1401:1401))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (896:896:896) (838:838:838))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (541:541:541) (578:578:578))
        (PORT datab (1375:1375:1375) (1326:1326:1326))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (336:336:336) (419:419:419))
        (PORT datab (558:558:558) (530:530:530))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (899:899:899) (864:864:864))
        (PORT datab (359:359:359) (436:436:436))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1176:1176:1176) (1099:1099:1099))
        (PORT datab (556:556:556) (578:578:578))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (363:363:363) (446:446:446))
        (PORT datab (1376:1376:1376) (1327:1327:1327))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (848:848:848) (864:864:864))
        (PORT datab (559:559:559) (531:531:531))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1059:1059:1059) (1061:1061:1061))
        (PORT datab (1393:1393:1393) (1475:1475:1475))
        (PORT datac (961:961:961) (962:962:962))
        (PORT datad (1388:1388:1388) (1453:1453:1453))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1018:1018:1018) (993:993:993))
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1359:1359:1359) (1324:1324:1324))
        (PORT datab (887:887:887) (839:839:839))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1060:1060:1060) (1062:1062:1062))
        (PORT datab (1393:1393:1393) (1474:1474:1474))
        (PORT datac (960:960:960) (961:961:961))
        (PORT datad (1388:1388:1388) (1453:1453:1453))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (555:555:555) (519:519:519))
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (363:363:363) (447:447:447))
        (PORT datab (560:560:560) (533:533:533))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (901:901:901) (867:867:867))
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1065:1065:1065) (1068:1068:1068))
        (PORT datab (1387:1387:1387) (1467:1467:1467))
        (PORT datac (956:956:956) (956:956:956))
        (PORT datad (1391:1391:1391) (1456:1456:1456))
        (IOPATH dataa combout (438:438:438) (448:448:448))
        (IOPATH datab combout (437:437:437) (436:436:436))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (PORT datab (540:540:540) (498:498:498))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1060:1060:1060) (1062:1062:1062))
        (PORT datab (1394:1394:1394) (1477:1477:1477))
        (PORT datac (962:962:962) (963:963:963))
        (PORT datad (1388:1388:1388) (1453:1453:1453))
        (IOPATH dataa combout (453:453:453) (413:413:413))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (579:579:579) (608:608:608))
        (PORT datab (548:548:548) (512:512:512))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (364:364:364) (448:448:448))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1920:1920:1920) (1890:1890:1890))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1877:1877:1877) (1823:1823:1823))
        (PORT d[1] (1829:1829:1829) (1776:1776:1776))
        (PORT d[2] (1795:1795:1795) (1745:1745:1745))
        (PORT d[3] (2117:2117:2117) (2054:2054:2054))
        (PORT d[4] (1435:1435:1435) (1418:1418:1418))
        (PORT d[5] (1448:1448:1448) (1423:1423:1423))
        (PORT d[6] (2942:2942:2942) (2793:2793:2793))
        (PORT d[7] (1472:1472:1472) (1451:1451:1451))
        (PORT d[8] (1450:1450:1450) (1427:1427:1427))
        (PORT d[9] (1547:1547:1547) (1520:1520:1520))
        (PORT d[10] (2496:2496:2496) (2386:2386:2386))
        (PORT d[11] (1493:1493:1493) (1470:1470:1470))
        (PORT clk (2036:2036:2036) (2082:2082:2082))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2036:2036:2036) (2082:2082:2082))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2037:2037:2037) (2083:2083:2083))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1988:1988:1988) (2035:2035:2035))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (959:959:959) (976:976:976))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1604:1604:1604) (1478:1478:1478))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1602:1602:1602) (1637:1637:1637))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1667:1667:1667) (1617:1617:1617))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1630:1630:1630) (1488:1488:1488))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1602:1602:1602) (1637:1637:1637))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1667:1667:1667) (1617:1617:1617))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1395:1395:1395) (1372:1372:1372))
        (PORT d[1] (1803:1803:1803) (1740:1740:1740))
        (PORT d[2] (1437:1437:1437) (1406:1406:1406))
        (PORT d[3] (2561:2561:2561) (2469:2469:2469))
        (PORT d[4] (1063:1063:1063) (1068:1068:1068))
        (PORT d[5] (1034:1034:1034) (1036:1036:1036))
        (PORT d[6] (3445:3445:3445) (3260:3260:3260))
        (PORT d[7] (1060:1060:1060) (1062:1062:1062))
        (PORT d[8] (1020:1020:1020) (1024:1024:1024))
        (PORT d[9] (1107:1107:1107) (1112:1112:1112))
        (PORT d[10] (2551:2551:2551) (2437:2437:2437))
        (PORT d[11] (1038:1038:1038) (1044:1044:1044))
        (PORT clk (2032:2032:2032) (2081:2081:2081))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2032:2032:2032) (2081:2081:2081))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2033:2033:2033) (2082:2082:2082))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1984:1984:1984) (2034:2034:2034))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (975:975:975))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (956:956:956) (976:976:976))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (956:956:956) (976:976:976))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (956:956:956) (976:976:976))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (2041:2041:2041) (1879:1879:1879))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1635:1635:1635))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1665:1665:1665) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (2079:2079:2079) (1962:1962:1962))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1635:1635:1635))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1665:1665:1665) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1796:1796:1796) (1744:1744:1744))
        (PORT d[1] (2282:2282:2282) (2195:2195:2195))
        (PORT d[2] (1853:1853:1853) (1799:1799:1799))
        (PORT d[3] (2132:2132:2132) (2065:2065:2065))
        (PORT d[4] (1482:1482:1482) (1463:1463:1463))
        (PORT d[5] (1496:1496:1496) (1469:1469:1469))
        (PORT d[6] (2965:2965:2965) (2812:2812:2812))
        (PORT d[7] (1781:1781:1781) (1740:1740:1740))
        (PORT d[8] (1430:1430:1430) (1408:1408:1408))
        (PORT d[9] (1520:1520:1520) (1500:1500:1500))
        (PORT d[10] (2140:2140:2140) (2050:2050:2050))
        (PORT d[11] (1571:1571:1571) (1547:1547:1547))
        (PORT clk (2036:2036:2036) (2082:2082:2082))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2036:2036:2036) (2082:2082:2082))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2037:2037:2037) (2083:2083:2083))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1988:1988:1988) (2035:2035:2035))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (959:959:959) (976:976:976))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1593:1593:1593) (1467:1467:1467))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1636:1636:1636))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1666:1666:1666) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1564:1564:1564) (1443:1443:1443))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1636:1636:1636))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1666:1666:1666) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1446:1446:1446) (1409:1409:1409))
        (PORT d[1] (2204:2204:2204) (2078:2078:2078))
        (PORT d[2] (1381:1381:1381) (1353:1353:1353))
        (PORT d[3] (2568:2568:2568) (2477:2477:2477))
        (PORT d[4] (1050:1050:1050) (1053:1053:1053))
        (PORT d[5] (1021:1021:1021) (1022:1022:1022))
        (PORT d[6] (3451:3451:3451) (3267:3267:3267))
        (PORT d[7] (1006:1006:1006) (1010:1010:1010))
        (PORT d[8] (1039:1039:1039) (1040:1040:1040))
        (PORT d[9] (1397:1397:1397) (1378:1378:1378))
        (PORT d[10] (1784:1784:1784) (1715:1715:1715))
        (PORT d[11] (1024:1024:1024) (1028:1028:1028))
        (PORT clk (2031:2031:2031) (2080:2080:2080))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2031:2031:2031) (2080:2080:2080))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2032:2032:2032) (2081:2081:2081))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1983:1983:1983) (2033:2033:2033))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (974:974:974))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (975:975:975))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (975:975:975))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (975:975:975))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1729:1729:1729) (1622:1622:1622))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1632:1632:1632))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1662:1662:1662) (1613:1613:1613))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1632:1632:1632))
        (PORT asdata (2457:2457:2457) (2293:2293:2293))
        (PORT clrn (1662:1662:1662) (1613:1613:1613))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (985:985:985) (971:971:971))
        (PORT d[1] (950:950:950) (951:951:951))
        (PORT d[2] (1028:1028:1028) (1017:1017:1017))
        (PORT d[3] (2585:2585:2585) (2475:2475:2475))
        (PORT d[4] (1403:1403:1403) (1366:1366:1366))
        (PORT d[5] (1389:1389:1389) (1354:1354:1354))
        (PORT d[6] (1480:1480:1480) (1438:1438:1438))
        (PORT d[7] (1414:1414:1414) (1374:1374:1374))
        (PORT d[8] (958:958:958) (957:957:957))
        (PORT d[9] (967:967:967) (968:968:968))
        (PORT d[10] (2557:2557:2557) (2443:2443:2443))
        (PORT d[11] (1380:1380:1380) (1351:1351:1351))
        (PORT clk (2030:2030:2030) (2079:2079:2079))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2030:2030:2030) (2079:2079:2079))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2031:2031:2031) (2080:2080:2080))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1982:1982:1982) (2032:2032:2032))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (953:953:953) (973:973:973))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (974:974:974))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (974:974:974))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (974:974:974))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1742:1742:1742) (1624:1624:1624))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1619:1619:1619) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1680:1680:1680) (1634:1634:1634))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1685:1685:1685) (1572:1572:1572))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1619:1619:1619) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1680:1680:1680) (1634:1634:1634))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1918:1918:1918) (1858:1858:1858))
        (PORT d[1] (1841:1841:1841) (1787:1787:1787))
        (PORT d[2] (1838:1838:1838) (1784:1784:1784))
        (PORT d[3] (1744:1744:1744) (1696:1696:1696))
        (PORT d[4] (1442:1442:1442) (1426:1426:1426))
        (PORT d[5] (1456:1456:1456) (1431:1431:1431))
        (PORT d[6] (2600:2600:2600) (2473:2473:2473))
        (PORT d[7] (1439:1439:1439) (1421:1421:1421))
        (PORT d[8] (1436:1436:1436) (1414:1414:1414))
        (PORT d[9] (1486:1486:1486) (1469:1469:1469))
        (PORT d[10] (1741:1741:1741) (1690:1690:1690))
        (PORT d[11] (1532:1532:1532) (1509:1509:1509))
        (PORT clk (2035:2035:2035) (2081:2081:2081))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2035:2035:2035) (2081:2081:2081))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2036:2036:2036) (2082:2082:2082))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1987:1987:1987) (2034:2034:2034))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (975:975:975))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (959:959:959) (976:976:976))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (959:959:959) (976:976:976))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (959:959:959) (976:976:976))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1376:1376:1376) (1276:1276:1276))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1377:1377:1377) (1275:1275:1275))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1392:1392:1392) (1366:1366:1366))
        (PORT d[1] (1388:1388:1388) (1362:1362:1362))
        (PORT d[2] (1424:1424:1424) (1387:1387:1387))
        (PORT d[3] (2569:2569:2569) (2472:2472:2472))
        (PORT d[4] (1029:1029:1029) (1038:1038:1038))
        (PORT d[5] (1468:1468:1468) (1438:1438:1438))
        (PORT d[6] (3024:3024:3024) (2873:2873:2873))
        (PORT d[7] (1027:1027:1027) (1032:1032:1032))
        (PORT d[8] (1422:1422:1422) (1391:1391:1391))
        (PORT d[9] (1085:1085:1085) (1091:1091:1091))
        (PORT d[10] (2167:2167:2167) (2080:2080:2080))
        (PORT d[11] (1076:1076:1076) (1082:1082:1082))
        (PORT clk (2034:2034:2034) (2084:2084:2084))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2034:2034:2034) (2084:2084:2084))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2035:2035:2035) (2085:2085:2085))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1986:1986:1986) (2037:2037:2037))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (957:957:957) (978:978:978))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (979:979:979))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (979:979:979))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (979:979:979))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1966:1966:1966) (1821:1821:1821))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1602:1602:1602) (1637:1637:1637))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1667:1667:1667) (1617:1617:1617))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1964:1964:1964) (1808:1808:1808))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1602:1602:1602) (1637:1637:1637))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1667:1667:1667) (1617:1617:1617))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (303:303:303) (377:377:377))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (378:378:378))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (306:306:306) (380:380:380))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT asdata (770:770:770) (844:844:844))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (306:306:306) (380:380:380))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT asdata (769:769:769) (843:843:843))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (325:325:325) (396:396:396))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1599:1599:1599) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1614:1614:1614))
        (PORT ena (1750:1750:1750) (1674:1674:1674))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (303:303:303) (377:377:377))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1661:1661:1661) (1612:1612:1612))
        (PORT ena (2356:2356:2356) (2279:2279:2279))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1661:1661:1661) (1612:1612:1612))
        (PORT ena (2356:2356:2356) (2279:2279:2279))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1661:1661:1661) (1612:1612:1612))
        (PORT ena (2356:2356:2356) (2279:2279:2279))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1661:1661:1661) (1612:1612:1612))
        (PORT ena (2356:2356:2356) (2279:2279:2279))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (905:905:905) (891:891:891))
        (PORT dataa[1] (860:860:860) (850:850:850))
        (PORT dataa[2] (782:782:782) (756:756:756))
        (PORT dataa[3] (781:781:781) (763:763:763))
        (PORT dataa[4] (885:885:885) (869:869:869))
        (PORT dataa[5] (772:772:772) (750:750:750))
        (PORT dataa[6] (880:880:880) (865:865:865))
        (PORT dataa[7] (783:783:783) (740:740:740))
        (PORT dataa[8] (1260:1260:1260) (1192:1192:1192))
        (PORT dataa[9] (1227:1227:1227) (1188:1188:1188))
        (PORT dataa[10] (877:877:877) (869:869:869))
        (PORT dataa[11] (913:913:913) (895:895:895))
        (PORT dataa[12] (778:778:778) (755:755:755))
        (PORT dataa[13] (900:900:900) (911:911:911))
        (PORT dataa[14] (955:955:955) (966:966:966))
        (PORT dataa[15] (965:965:965) (972:972:972))
        (PORT dataa[16] (900:900:900) (911:911:911))
        (PORT dataa[17] (955:955:955) (966:966:966))
        (PORT datab[6] (896:896:896) (888:888:888))
        (PORT datab[7] (908:908:908) (894:894:894))
        (PORT datab[8] (898:898:898) (887:887:887))
        (PORT datab[9] (1202:1202:1202) (1144:1144:1144))
        (PORT datab[10] (902:902:902) (875:875:875))
        (PORT datab[11] (856:856:856) (829:829:829))
        (PORT datab[12] (908:908:908) (877:877:877))
        (PORT datab[13] (885:885:885) (852:852:852))
        (PORT datab[14] (795:795:795) (772:772:772))
        (PORT datab[15] (521:521:521) (550:550:550))
        (PORT datab[16] (552:552:552) (530:530:530))
        (PORT datab[17] (518:518:518) (502:502:502))
        (IOPATH dataa dataout (3928:3928:3928) (3928:3928:3928))
        (IOPATH datab dataout (3863:3863:3863) (3863:3863:3863))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (139:139:139) (148:148:148))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (506:506:506) (531:531:531))
        (PORT dataa[7] (548:548:548) (570:570:570))
        (PORT dataa[8] (507:507:507) (531:531:531))
        (PORT dataa[9] (548:548:548) (563:563:563))
        (PORT dataa[10] (538:538:538) (562:562:562))
        (PORT dataa[11] (540:540:540) (560:560:560))
        (PORT dataa[12] (511:511:511) (531:531:531))
        (PORT dataa[13] (530:530:530) (545:545:545))
        (PORT dataa[14] (823:823:823) (800:800:800))
        (PORT dataa[15] (900:900:900) (903:903:903))
        (PORT dataa[16] (866:866:866) (895:895:895))
        (PORT dataa[17] (880:880:880) (895:895:895))
        (PORT datab[10] (800:800:800) (834:834:834))
        (PORT datab[11] (611:611:611) (588:588:588))
        (PORT datab[12] (622:622:622) (593:593:593))
        (PORT datab[13] (800:800:800) (834:834:834))
        (PORT datab[14] (611:611:611) (588:588:588))
        (PORT datab[15] (622:622:622) (593:593:593))
        (PORT datab[16] (800:800:800) (834:834:834))
        (PORT datab[17] (611:611:611) (588:588:588))
        (IOPATH dataa dataout (3928:3928:3928) (3928:3928:3928))
        (IOPATH datab dataout (3863:3863:3863) (3863:3863:3863))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (139:139:139) (148:148:148))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (481:481:481) (435:435:435))
        (PORT datab (1148:1148:1148) (1030:1030:1030))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (817:817:817) (748:748:748))
        (PORT datab (681:681:681) (611:611:611))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (713:713:713) (647:647:647))
        (PORT datab (769:769:769) (699:699:699))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (818:818:818) (725:725:725))
        (PORT datab (799:799:799) (736:736:736))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (795:795:795) (700:700:700))
        (PORT datab (842:842:842) (756:756:756))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (775:775:775) (713:713:713))
        (PORT datab (809:809:809) (714:714:714))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (828:828:828) (753:753:753))
        (PORT datab (772:772:772) (689:689:689))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (760:760:760) (676:676:676))
        (PORT datad (761:761:761) (694:694:694))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data_valid)
    (DELAY
      (ABSOLUTE
        (PORT clk (1598:1598:1598) (1633:1633:1633))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1663:1663:1663) (1613:1613:1613))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
)
