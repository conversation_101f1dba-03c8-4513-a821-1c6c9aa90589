module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA
);

// DAC904 digital to analog converter interface

wire	CLK_20M;

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;

// Square wave generation
assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;

// PLL clock generation module
PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_20M	)
);

// Sine wave ROM lookup table
ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_z		)
);

// Triangle wave ROM lookup table
ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_s		)
);

// 32-bit frequency accumulator
add_32bit u_add_32bit(
	.clk	(CLK_20M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);

// Key control module for frequency and waveform selection
key_con u_key_con(
	.clk			(CLK_20M	),
	.rst_n		(SYS_RST	),
	.key1_in	(KEY_IN[0]),
	.key2_in	(KEY_IN[1]),
	.key3_in  (KEY_IN[2]),
	.sel_wave	(sel			),
	.fre_k		(fre_k		)
);

// Waveform selection module
sel_wave u_sel_wave(
	.clk		(CLK_20M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_20M;
// High precision amplitude control using fixed-point arithmetic
localparam [7:0] SPI_VV = 8'd3;  // Amplitude attenuation factor
localparam [15:0] SCALE_FACTOR = 16'd16384;  // 65536/3 for 1/3 scaling with fixed-point multiplication

// Method 1: Direct fixed-point multiplication scaling (recommended)
wire [29:0] scaled_data;                           // 14-bit 脳 16-bit = 30-bit
assign scaled_data = DATA_BUF * SCALE_FACTOR;      
assign DAC_DATA = scaled_data[29:16];              // Take upper 14 bits for precise 1/3 scaling

// Method 2: Alternative with DC offset compensation (if needed)
// wire [29:0] scaled_signal;
// wire [29:0] scaled_offset; 
// assign scaled_signal = DATA_BUF * SCALE_FACTOR;
// assign scaled_offset = 14'd8191 * SCALE_FACTOR;
// assign DAC_DATA = scaled_signal[29:16] + (14'd8191 - scaled_offset[29:16]); 
//assign DAC_DATA = 14'd10000; // Fixed DC output for testing

endmodule