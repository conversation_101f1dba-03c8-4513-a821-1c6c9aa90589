{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753980574667 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "TimeQuest Timing Analyzer Quartus II 64-Bit " "Running Quartus II 64-Bit TimeQuest Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574668 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 00:49:34 2025 " "Processing started: Fri Aug 01 00:49:34 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753980574668 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753980574668 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta DAC904 -c DAC904 " "Command: quartus_sta DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753980574668 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Quartus II" 0 0 1753980574697 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1753980574750 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753980574774 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753980574774 ""}
{ "Info" "ISTA_SDC_FOUND" "../doc/SDC1.sdc " "Reading SDC File: '../doc/SDC1.sdc'" {  } {  } 0 332104 "Reading SDC File: '%1!s!'" 0 0 "Quartus II" 0 -1 1753980574898 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574899 ""}  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574899 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_CALL_IS_DELAYED" "" "Clock uncertainty is not calculated until you update the timing netlist." {  } {  } 0 332151 "Clock uncertainty is not calculated until you update the timing netlist." 0 0 "Quartus II" 0 -1 1753980574899 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574949 ""}
{ "Info" "0" "" "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Quartus II" 0 0 1753980574949 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Quartus II" 0 0 1753980574953 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 43.260 " "Worst-case setup slack is 43.260" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574959 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574959 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   43.260               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   43.260               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574959 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980574959 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.455 " "Worst-case hold slack is 0.455" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574960 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574960 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.455               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.455               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574960 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980574960 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753980574961 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753980574962 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574963 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574963 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574963 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.934               0.000 CLK_50M  " "    9.934               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574963 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.717               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.717               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980574963 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980574963 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753980574990 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Quartus II" 0 -1 1753980575001 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Quartus II" 0 -1 1753980575133 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575161 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 43.904 " "Worst-case setup slack is 43.904" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575166 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575166 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   43.904               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   43.904               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575166 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980575166 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.432 " "Worst-case hold slack is 0.432" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575168 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575168 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.432               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.432               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575168 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980575168 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753980575170 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753980575172 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575173 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575173 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575173 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943               0.000 CLK_50M  " "    9.943               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575173 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.718               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.718               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575173 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980575173 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753980575201 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575294 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 47.079 " "Worst-case setup slack is 47.079" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575297 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575297 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   47.079               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   47.079               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575297 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980575297 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.159 " "Worst-case hold slack is 0.159" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575300 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575300 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.159               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.159               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575300 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980575300 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753980575302 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753980575303 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 2.060 " "Worst-case minimum pulse width slack is 2.060" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575306 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575306 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.060               0.000 CLK_165M  " "    2.060               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575306 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.594               0.000 CLK_50M  " "    9.594               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575306 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.734               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.734               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753980575306 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753980575306 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753980575510 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753980575511 ""}
{ "Info" "IQEXE_ERROR_COUNT" "TimeQuest Timing Analyzer 0 s 1  Quartus II 64-Bit " "Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 1 warning" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4648 " "Peak virtual memory: 4648 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753980575551 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 00:49:35 2025 " "Processing ended: Fri Aug 01 00:49:35 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753980575551 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753980575551 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753980575551 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753980575551 ""}
