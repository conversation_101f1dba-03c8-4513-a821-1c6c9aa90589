{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753990700324 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "TimeQuest Timing Analyzer Quartus II 64-Bit " "Running Quartus II 64-Bit TimeQuest Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700324 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 03:38:20 2025 " "Processing started: Fri Aug 01 03:38:20 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753990700324 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753990700324 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta DAC904 -c DAC904 " "Command: quartus_sta DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753990700325 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Quartus II" 0 0 1753990700373 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1753990700423 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753990700447 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753990700447 ""}
{ "Info" "ISTA_SDC_FOUND" "../doc/SDC1.sdc " "Reading SDC File: '../doc/SDC1.sdc'" {  } {  } 0 332104 "Reading SDC File: '%1!s!'" 0 0 "Quartus II" 0 -1 1753990700567 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700568 ""}  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700568 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_CALL_IS_DELAYED" "" "Clock uncertainty is not calculated until you update the timing netlist." {  } {  } 0 332151 "Clock uncertainty is not calculated until you update the timing netlist." 0 0 "Quartus II" 0 -1 1753990700569 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "spi_sclk " "Node: spi_sclk was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753990700571 "|DAC904_TOP|spi_sclk"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700618 ""}
{ "Info" "0" "" "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Quartus II" 0 0 1753990700619 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Quartus II" 0 0 1753990700630 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 43.783 " "Worst-case setup slack is 43.783" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700636 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700636 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   43.783               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   43.783               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700636 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990700636 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.441 " "Worst-case hold slack is 0.441" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700638 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700638 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.441               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.441               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700638 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990700638 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990700639 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990700640 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700641 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700641 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700641 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.934               0.000 CLK_50M  " "    9.934               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700641 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.721               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.721               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700641 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990700641 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753990700675 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Quartus II" 0 -1 1753990700689 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Quartus II" 0 -1 1753990700865 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "spi_sclk " "Node: spi_sclk was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753990700893 "|DAC904_TOP|spi_sclk"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700894 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 44.381 " "Worst-case setup slack is 44.381" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700898 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700898 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   44.381               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   44.381               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700898 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990700898 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.422 " "Worst-case hold slack is 0.422" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700899 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700899 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.422               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.422               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700899 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990700899 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990700901 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990700903 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700905 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700905 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700905 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943               0.000 CLK_50M  " "    9.943               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700905 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.719               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.719               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990700905 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990700905 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753990700935 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "spi_sclk " "Node: spi_sclk was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753990701034 "|DAC904_TOP|spi_sclk"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701034 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 47.135 " "Worst-case setup slack is 47.135" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701036 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701036 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   47.135               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   47.135               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701036 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990701036 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.147 " "Worst-case hold slack is 0.147" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701039 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701039 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.147               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.147               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701039 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990701039 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990701040 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990701043 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 2.060 " "Worst-case minimum pulse width slack is 2.060" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701045 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701045 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.060               0.000 CLK_165M  " "    2.060               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701045 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.594               0.000 CLK_50M  " "    9.594               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701045 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.734               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.734               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990701045 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990701045 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753990701266 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753990701267 ""}
{ "Info" "IQEXE_ERROR_COUNT" "TimeQuest Timing Analyzer 0 s 4 s Quartus II 64-Bit " "Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 4 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4644 " "Peak virtual memory: 4644 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753990701310 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 03:38:21 2025 " "Processing ended: Fri Aug 01 03:38:21 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753990701310 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753990701310 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753990701310 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753990701310 ""}
