{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753990404564 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus II 64-Bit " "Running Quartus II 64-Bit Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753990404564 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 03:33:24 2025 " "Processing started: Fri Aug 01 03:33:24 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753990404564 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753990404564 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753990404564 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1753990404686 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Tri " "Found entity 1: ROM_Tri" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404706 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404706 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/sel_wave.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/sel_wave.v" { { "Info" "ISGN_ENTITY_NAME" "1 sel_wave " "Found entity 1: sel_wave" {  } { { "../rtl/sel_wave.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/sel_wave.v" 2 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404707 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404707 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/key_delay.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/key_delay.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_delay " "Found entity 1: key_delay" {  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404708 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404708 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/key_con.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/key_con.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_con " "Found entity 1: key_con" {  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/key_con.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404709 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404709 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/add_32bit.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/add_32bit.v" { { "Info" "ISGN_ENTITY_NAME" "1 add_32bit " "Found entity 1: add_32bit" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404710 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404710 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Sin " "Found entity 1: ROM_Sin" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404711 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404711 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/dac904_top.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/rtl/dac904_top.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_TOP " "Found entity 1: DAC904_TOP" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404712 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404712 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/workspace_ccstheia/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK " "Found entity 1: PLL_CLK" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404714 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404714 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "DAC904_TOP " "Elaborating entity \"DAC904_TOP\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Quartus II" 0 -1 1753990404754 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "spi_cnt DAC904_TOP.v(23) " "Verilog HDL or VHDL warning at DAC904_TOP.v(23): object \"spi_cnt\" assigned a value but never read" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 23 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Quartus II" 0 -1 1753990404755 "|DAC904_TOP"}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "sclk_d DAC904_TOP.v(25) " "Verilog HDL or VHDL warning at DAC904_TOP.v(25): object \"sclk_d\" assigned a value but never read" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 25 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Quartus II" 0 -1 1753990404755 "|DAC904_TOP"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PLL_CLK PLL_CLK:u_PLL_CLK " "Elaborating entity \"PLL_CLK\" for hierarchy \"PLL_CLK:u_PLL_CLK\"" {  } { { "../rtl/DAC904_TOP.v" "u_PLL_CLK" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 122 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404766 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Elaborating entity \"altpll\" for hierarchy \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\"" {  } { { "../ip_core/PLL/PLL_CLK.v" "altpll_component" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404782 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Elaborated megafunction instantiation \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\"" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404784 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Instantiated megafunction \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 5 " "Parameter \"clk0_divide_by\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 2 " "Parameter \"clk0_multiply_by\" = \"2\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=PLL_CLK " "Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=PLL_CLK\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_USED " "Parameter \"port_areset\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_UNUSED " "Parameter \"port_locked\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_UNUSED " "Parameter \"port_clk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Parameter \"width_clock\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404785 ""}  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1753990404785 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/pll_clk_altpll.v 1 1 " "Found 1 design units, including 1 entities, in source file db/pll_clk_altpll.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK_altpll " "Found entity 1: PLL_CLK_altpll" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 29 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404816 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404816 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PLL_CLK_altpll PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated " "Elaborating entity \"PLL_CLK_altpll\" for hierarchy \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404816 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "ROM_Sin ROM_Sin:u_ROM_Sin " "Elaborating entity \"ROM_Sin\" for hierarchy \"ROM_Sin:u_ROM_Sin\"" {  } { { "../rtl/DAC904_TOP.v" "u_ROM_Sin" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 129 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404818 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Sin.v" "altsyncram_component" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404830 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Instantiated megafunction \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file Sin_Wave.mif " "Parameter \"init_file\" = \"Sin_Wave.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 4096 " "Parameter \"numwords_a\" = \"4096\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a CLOCK0 " "Parameter \"outdata_reg_a\" = \"CLOCK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 12 " "Parameter \"widthad_a\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404831 ""}  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1753990404831 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_aj91.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_aj91.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_aj91 " "Found entity 1: altsyncram_aj91" {  } { { "db/altsyncram_aj91.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_aj91.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404859 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404859 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_aj91 ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\|altsyncram_aj91:auto_generated " "Elaborating entity \"altsyncram_aj91\" for hierarchy \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\|altsyncram_aj91:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404860 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "ROM_Tri ROM_Tri:ROM_Tri " "Elaborating entity \"ROM_Tri\" for hierarchy \"ROM_Tri:ROM_Tri\"" {  } { { "../rtl/DAC904_TOP.v" "ROM_Tri" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404862 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Tri.v" "altsyncram_component" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404866 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Instantiated megafunction \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file Tri_Wave.mif " "Parameter \"init_file\" = \"Tri_Wave.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 4096 " "Parameter \"numwords_a\" = \"4096\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 12 " "Parameter \"widthad_a\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404867 ""}  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1753990404867 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_4aa1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_4aa1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_4aa1 " "Found entity 1: altsyncram_4aa1" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990404897 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990404897 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_4aa1 ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated " "Elaborating entity \"altsyncram_4aa1\" for hierarchy \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404897 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "add_32bit add_32bit:u_add_32bit " "Elaborating entity \"add_32bit\" for hierarchy \"add_32bit:u_add_32bit\"" {  } { { "../rtl/DAC904_TOP.v" "u_add_32bit" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 144 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404899 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sel_wave sel_wave:u_sel_wave " "Elaborating entity \"sel_wave\" for hierarchy \"sel_wave:u_sel_wave\"" {  } { { "../rtl/DAC904_TOP.v" "u_sel_wave" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 158 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990404900 ""}
{ "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_HDR" "" "Synthesized away the following node(s):" { { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_SUB_HDR" "RAM " "Synthesized away the following RAM node(s):" { { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[0\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[0\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 34 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[1\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[1\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 55 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a1"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[2\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[2\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 76 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[3\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[3\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 97 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a3"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[4\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[4\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 118 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[5\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[5\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 139 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a5"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[6\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[6\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 160 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[7\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[7\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 181 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a7"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[8\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[8\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 202 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[9\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[9\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 223 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a9"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[10\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[10\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 244 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[11\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[11\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 265 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a11"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[12\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[12\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 286 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[13\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[13\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 307 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 136 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990404935 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a13"}  } {  } 0 14285 "Synthesized away the following %1!s! node(s):" 0 0 "Quartus II" 0 -1 1753990404935 ""}  } {  } 0 14284 "Synthesized away the following node(s):" 0 0 "Quartus II" 0 -1 1753990404935 ""}
{ "Info" "ILPMS_INFERENCING_SUMMARY" "1 " "Inferred 1 megafunctions from design logic" { { "Info" "ILPMS_LPM_MULT_INFERRED" "Mult0 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"Mult0\"" {  } { { "../rtl/DAC904_TOP.v" "Mult0" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 171 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990405038 ""}  } {  } 0 278001 "Inferred %1!llu! megafunctions from design logic" 0 0 "Quartus II" 0 -1 1753990405038 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "lpm_mult:Mult0 " "Elaborated megafunction instantiation \"lpm_mult:Mult0\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 171 -1 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "lpm_mult:Mult0 " "Instantiated megafunction \"lpm_mult:Mult0\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHA 26 " "Parameter \"LPM_WIDTHA\" = \"26\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHB 12 " "Parameter \"LPM_WIDTHB\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHP 38 " "Parameter \"LPM_WIDTHP\" = \"38\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHR 38 " "Parameter \"LPM_WIDTHR\" = \"38\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHS 1 " "Parameter \"LPM_WIDTHS\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_REPRESENTATION UNSIGNED " "Parameter \"LPM_REPRESENTATION\" = \"UNSIGNED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_A_IS_CONSTANT NO " "Parameter \"INPUT_A_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_B_IS_CONSTANT NO " "Parameter \"INPUT_B_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "MAXIMIZE_SPEED 5 " "Parameter \"MAXIMIZE_SPEED\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753990405056 ""}  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 171 -1 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1753990405056 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mult_9dt.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/mult_9dt.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mult_9dt " "Found entity 1: mult_9dt" {  } { { "db/mult_9dt.tdf" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/mult_9dt.tdf" 30 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753990405087 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753990405087 ""}
{ "Info" "IMLS_MLS_IGNORED_SUMMARY" "24 " "Ignored 24 buffer(s)" { { "Info" "IMLS_MLS_IGNORED_SOFT" "24 " "Ignored 24 SOFT buffer(s)" {  } {  } 0 13019 "Ignored %1!d! SOFT buffer(s)" 0 0 "Quartus II" 0 -1 1753990405215 ""}  } {  } 0 13014 "Ignored %1!d! buffer(s)" 0 0 "Quartus II" 0 -1 1753990405215 ""}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 87 -1 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 62 -1 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 63 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Quartus II" 0 -1 1753990405217 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Quartus II" 0 -1 1753990405217 ""}
{ "Warning" "WMLS_MLS_STUCK_PIN_HDR" "" "Output pins are stuck at VCC or GND" { { "Warning" "WMLS_MLS_STUCK_PIN" "PD GND " "Pin \"PD\" is stuck at GND" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 9 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Quartus II" 0 -1 1753990405227 "|DAC904_TOP|PD"}  } {  } 0 13024 "Output pins are stuck at VCC or GND" 0 0 "Quartus II" 0 -1 1753990405227 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Quartus II" 0 -1 1753990405283 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "1 0 1 0 0 " "Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Quartus II" 0 -1 1753990405605 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990405605 ""}
{ "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN_HDR" "3 " "Design contains 3 input pin(s) that do not drive logic" { { "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN" "KEY_IN\[0\] " "No output dependent on input pin \"KEY_IN\[0\]\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 4 0 0 } }  } 0 15610 "No output dependent on input pin \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990405634 "|DAC904_TOP|KEY_IN[0]"} { "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN" "KEY_IN\[1\] " "No output dependent on input pin \"KEY_IN\[1\]\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 4 0 0 } }  } 0 15610 "No output dependent on input pin \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990405634 "|DAC904_TOP|KEY_IN[1]"} { "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN" "KEY_IN\[2\] " "No output dependent on input pin \"KEY_IN\[2\]\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 4 0 0 } }  } 0 15610 "No output dependent on input pin \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753990405634 "|DAC904_TOP|KEY_IN[2]"}  } {  } 0 21074 "Design contains %1!d! input pin(s) that do not drive logic" 0 0 "Quartus II" 0 -1 1753990405634 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "171 " "Implemented 171 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "8 " "Implemented 8 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Quartus II" 0 -1 1753990405634 ""} { "Info" "ICUT_CUT_TM_OPINS" "33 " "Implemented 33 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Quartus II" 0 -1 1753990405634 ""} { "Info" "ICUT_CUT_TM_LCELLS" "111 " "Implemented 111 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Quartus II" 0 -1 1753990405634 ""} { "Info" "ICUT_CUT_TM_RAMS" "14 " "Implemented 14 RAM segments" {  } {  } 0 21064 "Implemented %1!d! RAM segments" 0 0 "Quartus II" 0 -1 1753990405634 ""} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Implemented 1 PLLs" {  } {  } 0 21065 "Implemented %1!d! PLLs" 0 0 "Quartus II" 0 -1 1753990405634 ""} { "Info" "ICUT_CUT_TM_DSP_ELEM" "4 " "Implemented 4 DSP elements" {  } {  } 0 21062 "Implemented %1!d! DSP elements" 0 0 "Quartus II" 0 -1 1753990405634 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Quartus II" 0 -1 1753990405634 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 25 s Quartus II 64-Bit " "Quartus II 64-Bit Analysis & Synthesis was successful. 0 errors, 25 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4645 " "Peak virtual memory: 4645 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753990405655 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 03:33:25 2025 " "Processing ended: Fri Aug 01 03:33:25 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753990405655 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753990405655 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753990405655 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753990405655 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753990406624 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Fitter Quartus II 64-Bit " "Running Quartus II 64-Bit Fitter" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753990406625 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 03:33:26 2025 " "Processing started: Fri Aug 01 03:33:26 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753990406625 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Fitter" 0 -1 1753990406625 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_fit --read_settings_files=off --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_fit --read_settings_files=off --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Fitter" 0 -1 1753990406625 ""}
{ "Info" "0" "" "qfit2_default_script.tcl version: #1" {  } {  } 0 0 "qfit2_default_script.tcl version: #1" 0 0 "Fitter" 0 0 1753990406650 ""}
{ "Info" "0" "" "Project  = DAC904" {  } {  } 0 0 "Project  = DAC904" 0 0 "Fitter" 0 0 1753990406650 ""}
{ "Info" "0" "" "Revision = DAC904" {  } {  } 0 0 "Revision = DAC904" 0 0 "Fitter" 0 0 1753990406650 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Fitter" 0 -1 1753990406683 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "DAC904 EP4CE6E22C8 " "Selected device EP4CE6E22C8 for design \"DAC904\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1753990406687 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753990406712 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753990406712 ""}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1 Cyclone IV E PLL " "Implemented PLL \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] 2 5 0 0 " "Implementing clock multiplication of 2, clock division of 5, and phase shift of 0 degrees (0 ps) for PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] port" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 140 9662 10382 0}  }  } }  } 0 15099 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "Quartus II" 0 -1 1753990406744 ""}  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 140 9662 10382 0}  }  } }  } 0 15535 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "Fitter" 0 -1 1753990406744 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1753990406755 ""}
{ "Warning" "WCPT_FEATURE_DISABLED_POST" "LogicLock " "Feature LogicLock is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." {  } {  } 0 292013 "Feature %1!s! is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." 0 0 "Fitter" 0 -1 1753990406760 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10E22C8 " "Device EP4CE10E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753990406840 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15E22C8 " "Device EP4CE15E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753990406840 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22E22C8 " "Device EP4CE22E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753990406840 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1753990406840 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "4 " "Fitter converted 4 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ 6 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location 6" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_ASDO_DATA1~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 690 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753990406841 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ 8 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location 8" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_FLASH_nCE_nCSO~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 692 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753990406841 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ 12 " "Pin ~ALTERA_DCLK~ is reserved at location 12" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DCLK~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 694 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753990406841 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ 13 " "Pin ~ALTERA_DATA0~ is reserved at location 13" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DATA0~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 696 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753990406841 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1753990406841 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1753990406842 ""}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 176045 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "Fitter" 0 -1 1753990406843 ""}
{ "Critical Warning" "WFIOMGR_PINS_MISSING_LOCATION_INFO" "17 41 " "No exact pin location assignment(s) for 17 pins of 41 total pins" { { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_spi_valid " "Pin debug_spi_valid not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_spi_valid } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 13 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_spi_valid } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 59 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_freq_sel\[0\] " "Pin debug_freq_sel\[0\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_freq_sel[0] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 14 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_freq_sel[0] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 35 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_freq_sel\[1\] " "Pin debug_freq_sel\[1\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_freq_sel[1] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 14 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_freq_sel[1] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 36 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_freq_sel\[2\] " "Pin debug_freq_sel\[2\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_freq_sel[2] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 14 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_freq_sel[2] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 37 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_freq_sel\[3\] " "Pin debug_freq_sel\[3\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_freq_sel[3] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 14 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_freq_sel[3] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 38 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[0\] " "Pin debug_amplitude\[0\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[0] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[0] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 39 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[1\] " "Pin debug_amplitude\[1\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[1] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[1] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 40 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[2\] " "Pin debug_amplitude\[2\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[2] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[2] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 41 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[3\] " "Pin debug_amplitude\[3\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[3] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[3] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 42 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[4\] " "Pin debug_amplitude\[4\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[4] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[4] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 43 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[5\] " "Pin debug_amplitude\[5\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[5] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[5] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 44 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[6\] " "Pin debug_amplitude\[6\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[6] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[6] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 45 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[7\] " "Pin debug_amplitude\[7\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[7] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[7] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 46 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[8\] " "Pin debug_amplitude\[8\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[8] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[8] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 47 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[9\] " "Pin debug_amplitude\[9\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[9] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[9] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 48 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[10\] " "Pin debug_amplitude\[10\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[10] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[10] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 49 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""} { "Info" "IFIOMGR_PIN_MISSING_LOCATION_INFO" "debug_amplitude\[11\] " "Pin debug_amplitude\[11\] not assigned to an exact location on the device" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { debug_amplitude[11] } } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 16 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { debug_amplitude[11] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 50 9662 10382 0}  }  } }  } 0 169086 "Pin %1!s! not assigned to an exact location on the device" 0 0 "Quartus II" 0 -1 1753990406991 ""}  } {  } 1 169085 "No exact pin location assignment(s) for %1!d! pins of %2!d! total pins" 0 0 "Fitter" 0 -1 1753990406991 ""}
{ "Info" "ISTA_SDC_FOUND" "../doc/SDC1.sdc " "Reading SDC File: '../doc/SDC1.sdc'" {  } {  } 0 332104 "Reading SDC File: '%1!s!'" 0 0 "Fitter" 0 -1 1753990407116 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753990407117 ""}  } {  } 0 332110 "%1!s!" 0 0 "Fitter" 0 -1 1753990407117 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_CALL_IS_DELAYED" "" "Clock uncertainty is not calculated until you update the timing netlist." {  } {  } 0 332151 "Clock uncertainty is not calculated until you update the timing netlist." 0 0 "Fitter" 0 -1 1753990407117 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "spi_sclk " "Node: spi_sclk was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1753990407118 "|DAC904_TOP|spi_sclk"}
{ "Info" "ISTA_NO_UNCERTAINTY_FOUND" "" "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." {  } {  } 0 332154 "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." 0 0 "Fitter" 0 -1 1753990407118 ""}
{ "Info" "ISTA_USER_TDC_OPTIMIZATION_GOALS" "" "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" {  } {  } 0 332129 "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" 0 0 "Fitter" 0 -1 1753990407119 ""}
{ "Info" "ISTA_REPORT_CLOCKS_INFO" "Found 3 clocks " "Found 3 clocks" { { "Info" "ISTA_REPORT_CLOCKS_INFO" "  Period   Clock Name " "  Period   Clock Name" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753990407119 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "======== ============ " "======== ============" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753990407119 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "  20.000      CLK_50M " "  20.000      CLK_50M" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753990407119 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "   6.060     CLK_165M " "   6.060     CLK_165M" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753990407119 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "  50.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " "  50.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753990407119 ""}  } {  } 0 332111 "%1!s!" 0 0 "Fitter" 0 -1 1753990407119 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_2) " "Automatically promoted node PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_2)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G8 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G8" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753990407124 ""}  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 80 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { PLL_CLK:u_PLL_CLK|altpll:altpll_component|PLL_CLK_altpll:auto_generated|wire_pll1_clk[0] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 140 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753990407124 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "spi_sclk~input (placed in PIN 23 (CLK1, DIFFCLK_0n)) " "Automatically promoted node spi_sclk~input (placed in PIN 23 (CLK1, DIFFCLK_0n))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G2 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G2" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753990407124 ""}  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 6 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { spi_sclk~input } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 681 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753990407124 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "SYS_RST~input (placed in PIN 24 (CLK2, DIFFCLK_1p)) " "Automatically promoted node SYS_RST~input (placed in PIN 24 (CLK2, DIFFCLK_1p))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G4 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G4" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[20\] " "Destination node add_32bit:u_add_32bit\|add\[20\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[20] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 97 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[21\] " "Destination node add_32bit:u_add_32bit\|add\[21\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[21] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 98 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[22\] " "Destination node add_32bit:u_add_32bit\|add\[22\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[22] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 99 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[23\] " "Destination node add_32bit:u_add_32bit\|add\[23\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[23] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 100 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[24\] " "Destination node add_32bit:u_add_32bit\|add\[24\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[24] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 101 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[25\] " "Destination node add_32bit:u_add_32bit\|add\[25\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[25] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 102 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[26\] " "Destination node add_32bit:u_add_32bit\|add\[26\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[26] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 103 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[27\] " "Destination node add_32bit:u_add_32bit\|add\[27\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[27] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 104 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[28\] " "Destination node add_32bit:u_add_32bit\|add\[28\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[28] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 105 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[29\] " "Destination node add_32bit:u_add_32bit\|add\[29\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[29] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 106 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753990407125 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Quartus II" 0 -1 1753990407125 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Quartus II" 0 -1 1753990407125 ""}  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 3 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { SYS_RST~input } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 678 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753990407125 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1753990407283 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753990407283 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753990407283 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753990407284 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753990407284 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 1753990407284 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "No registers were packed into other blocks" {  } {  } 1 176219 "No registers were packed into other blocks" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_IO_BANK_PIN_GROUP_STATISTICS" "I/O pins that need to be placed that use the same VCCIO and VREF, before I/O pin placement " "Statistics of I/O pins that need to be placed that use the same VCCIO and VREF, before I/O pin placement" { { "Info" "IFSAC_FSAC_SINGLE_IOC_GROUP_STATISTICS" "17 unused 2.5V 0 17 0 " "Number of I/O pins in group: 17 (unused VREF, 2.5V VCCIO, 0 input, 17 output, 0 bidirectional)" { { "Info" "IFSAC_FSAC_IO_STDS_IN_IOC_GROUP" "2.5 V. " "I/O standards used: 2.5 V." {  } {  } 0 176212 "I/O standards used: %1!s!" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 176211 "Number of I/O pins in group: %1!d! (%2!s! VREF, %3!s! VCCIO, %4!d! input, %5!d! output, %6!d! bidirectional)" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 176214 "Statistics of %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_IO_STATS_BEFORE_AFTER_PLACEMENT" "before " "I/O bank details before I/O pin placement" { { "Info" "IFSAC_FSAC_IO_BANK_PIN_GROUP_STATISTICS" "I/O banks " "Statistics of I/O banks" { { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "1 does not use undetermined 5 6 " "I/O bank number 1 does not use VREF pins and has undetermined VCCIO pins. 5 total pin(s) used --  6 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "2 does not use undetermined 3 5 " "I/O bank number 2 does not use VREF pins and has undetermined VCCIO pins. 3 total pin(s) used --  5 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "3 does not use undetermined 0 11 " "I/O bank number 3 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  11 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "4 does not use undetermined 0 14 " "I/O bank number 4 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  14 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "5 does not use 2.5V 13 0 " "I/O bank number 5 does not use VREF pins and has 2.5V VCCIO pins. 13 total pin(s) used --  0 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "6 does not use 2.5V 7 3 " "I/O bank number 6 does not use VREF pins and has 2.5V VCCIO pins. 7 total pin(s) used --  3 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "7 does not use undetermined 0 13 " "I/O bank number 7 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  13 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "8 does not use undetermined 0 12 " "I/O bank number 8 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  12 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 176214 "Statistics of %1!s!" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 176215 "I/O bank details %1!s! I/O pin placement" 0 0 "Fitter" 0 -1 ************* ""}
{ "Warning" "WCUT_PLL_CLK_FEEDS_NON_DEDICATED_IO" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1 clk\[0\] DAC_CLK~output " "PLL \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1\" output port clk\[0\] feeds output pin \"DAC_CLK~output\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "altpll.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 122 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 10 0 0 } }  } 0 15064 "PLL \"%1!s!\" output port %2!s! feeds output pin \"%3!s!\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:01 " "Fitter preparation operations ending: elapsed time is 00:00:01" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:00 " "Fitter placement operations ending: elapsed time is 00:00:00" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "0 " "Router estimated average interconnect usage is 0% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "2 X11_Y0 X22_Y11 " "Router estimated peak interconnect usage is 2% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11" {  } { { "loc" "" { Generic "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/" { { 1 { 0 "Router estimated peak interconnect usage is 2% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11"} { { 11 { 0 "Router estimated peak interconnect usage is 2% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11"} 11 0 12 12 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_TIMING" "" "Optimizations that may affect the design's timing were skipped" {  } {  } 0 170200 "Optimizations that may affect the design's timing were skipped" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "0.09 " "Total time spent on timing analysis during the Fitter is 0.09 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during the Fitter is %1!s! seconds." 0 0 "Fitter" 0 -1 1753990408543 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 1753990408577 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1753990408675 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 1753990408704 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1753990408819 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:01 " "Fitter post-fit operations ending: elapsed time is 00:00:01" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1753990409011 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/output_files/DAC904.fit.smsg " "Generated suppressed messages file C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/output_files/DAC904.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1753990409177 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 6 s Quartus II 64-Bit " "Quartus II 64-Bit Fitter was successful. 0 errors, 6 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4963 " "Peak virtual memory: 4963 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753990409360 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 03:33:29 2025 " "Processing ended: Fri Aug 01 03:33:29 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753990409360 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:03 " "Elapsed time: 00:00:03" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753990409360 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:03 " "Total CPU time (on all processors): 00:00:03" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753990409360 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1753990409360 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Fitter" 0 -1 1753990410307 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus II 64-Bit " "Running Quartus II 64-Bit Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753990410307 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 03:33:30 2025 " "Processing started: Fri Aug 01 03:33:30 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753990410307 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Assembler" 0 -1 1753990410307 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_asm --read_settings_files=off --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Assembler" 0 -1 1753990410307 ""}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Writing out detailed assembly data for power analysis" {  } {  } 0 115031 "Writing out detailed assembly data for power analysis" 0 0 "Assembler" 0 -1 1753990410600 ""}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Assembler is generating device programming files" {  } {  } 0 115030 "Assembler is generating device programming files" 0 0 "Assembler" 0 -1 1753990410608 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 0 s Quartus II 64-Bit " "Quartus II 64-Bit Assembler was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4597 " "Peak virtual memory: 4597 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753990410727 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 03:33:30 2025 " "Processing ended: Fri Aug 01 03:33:30 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753990410727 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753990410727 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753990410727 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Assembler" 0 -1 1753990410727 ""}
{ "Info" "IFLOW_DISABLED_MODULE" "PowerPlay Power Analyzer FLOW_ENABLE_POWER_ANALYZER " "Skipped module PowerPlay Power Analyzer due to the assignment FLOW_ENABLE_POWER_ANALYZER" {  } {  } 0 293026 "Skipped module %1!s! due to the assignment %2!s!" 0 0 "Assembler" 0 -1 1753990411287 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Assembler" 0 -1 1753990411709 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "TimeQuest Timing Analyzer Quartus II 64-Bit " "Running Quartus II 64-Bit TimeQuest Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753990411709 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 03:33:31 2025 " "Processing started: Fri Aug 01 03:33:31 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753990411709 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753990411709 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta DAC904 -c DAC904 " "Command: quartus_sta DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753990411710 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Quartus II" 0 0 1753990411740 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1753990411794 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753990411820 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753990411820 ""}
{ "Info" "ISTA_SDC_FOUND" "../doc/SDC1.sdc " "Reading SDC File: '../doc/SDC1.sdc'" {  } {  } 0 332104 "Reading SDC File: '%1!s!'" 0 0 "Quartus II" 0 -1 1753990411949 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753990411950 ""}  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753990411950 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_CALL_IS_DELAYED" "" "Clock uncertainty is not calculated until you update the timing netlist." {  } {  } 0 332151 "Clock uncertainty is not calculated until you update the timing netlist." 0 0 "Quartus II" 0 -1 1753990411950 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "spi_sclk " "Node: spi_sclk was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753990411953 "|DAC904_TOP|spi_sclk"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412002 ""}
{ "Info" "0" "" "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Quartus II" 0 0 1753990412003 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Quartus II" 0 0 1753990412008 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 44.381 " "Worst-case setup slack is 44.381" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412014 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412014 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   44.381               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   44.381               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412014 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412014 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.456 " "Worst-case hold slack is 0.456" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412015 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412015 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.456               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.456               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412015 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412015 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990412017 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990412018 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412019 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412019 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412019 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.934               0.000 CLK_50M  " "    9.934               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412019 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.718               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.718               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412019 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412019 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753990412049 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Quartus II" 0 -1 1753990412060 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Quartus II" 0 -1 1753990412198 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "spi_sclk " "Node: spi_sclk was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753990412228 "|DAC904_TOP|spi_sclk"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412228 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 44.896 " "Worst-case setup slack is 44.896" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412233 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412233 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   44.896               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   44.896               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412233 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412233 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.432 " "Worst-case hold slack is 0.432" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412235 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412235 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.432               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.432               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412235 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412235 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990412237 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990412238 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412240 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412240 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412240 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943               0.000 CLK_50M  " "    9.943               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412240 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.717               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.717               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412240 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412240 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753990412271 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "spi_sclk " "Node: spi_sclk was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753990412367 "|DAC904_TOP|spi_sclk"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412368 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 47.499 " "Worst-case setup slack is 47.499" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412370 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412370 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   47.499               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   47.499               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412370 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412370 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.163 " "Worst-case hold slack is 0.163" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412373 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412373 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.163               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.163               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412373 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412373 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990412375 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753990412378 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 2.060 " "Worst-case minimum pulse width slack is 2.060" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412380 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412380 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.060               0.000 CLK_165M  " "    2.060               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412380 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.594               0.000 CLK_50M  " "    9.594               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412380 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.735               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.735               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753990412380 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753990412380 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753990412604 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753990412604 ""}
{ "Info" "IQEXE_ERROR_COUNT" "TimeQuest Timing Analyzer 0 s 4 s Quartus II 64-Bit " "Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 4 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4644 " "Peak virtual memory: 4644 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753990412647 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 03:33:32 2025 " "Processing ended: Fri Aug 01 03:33:32 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753990412647 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753990412647 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753990412647 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753990412647 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753990413585 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "EDA Netlist Writer Quartus II 64-Bit " "Running Quartus II 64-Bit EDA Netlist Writer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753990413585 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 03:33:33 2025 " "Processing started: Fri Aug 01 03:33:33 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753990413585 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753990413585 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_eda --read_settings_files=off --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_eda --read_settings_files=off --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753990413585 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_85c_slow.vo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_85c_slow.vo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413758 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_0c_slow.vo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_0c_slow.vo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413780 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_min_1200mv_0c_fast.vo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_min_1200mv_0c_fast.vo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413800 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904.vo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904.vo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413821 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_85c_v_slow.sdo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_85c_v_slow.sdo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413837 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_0c_v_slow.sdo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_0c_v_slow.sdo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413853 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_min_1200mv_0c_v_fast.sdo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_min_1200mv_0c_v_fast.sdo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413868 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_v.sdo C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_v.sdo in folder \"C:/Users/<USER>/workspace_ccstheia/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753990413884 ""}
{ "Info" "IQEXE_ERROR_COUNT" "EDA Netlist Writer 0 s 0 s Quartus II 64-Bit " "Quartus II 64-Bit EDA Netlist Writer was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4548 " "Peak virtual memory: 4548 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753990413908 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 03:33:33 2025 " "Processing ended: Fri Aug 01 03:33:33 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753990413908 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753990413908 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753990413908 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753990413908 ""}
{ "Info" "IFLOW_ERROR_COUNT" "Full Compilation 0 s 35 s " "Quartus II Full Compilation was successful. 0 errors, 35 warnings" {  } {  } 0 293000 "Quartus II %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753990414485 ""}
