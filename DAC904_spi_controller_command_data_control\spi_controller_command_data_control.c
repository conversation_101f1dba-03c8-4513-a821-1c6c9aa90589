/*
 * DAC904 FPGA SPI控制器 - 修复编译错误版本
 * 功能：通过SPI控制FPGA输出波形的频率和幅度
 * 
 * 硬件连接：
 * MSPM0G3507     →    FPGA Cyclone IV
 * ─────────────────────────────────────
 * PB9 (SCLK)     →    PIN_23 (spi_sclk)
 * PB8 (MOSI)     →    PIN_25 (spi_mosi)  
 * PB6 (CS0)      →    PIN_31 (spi_cs_n)
 * GND            →    GND
 * 3.3V           →    3.3V
 */

#include "ti_msp_dl_config.h"
#include <stdint.h>

// 启用16位SPI传输
#define USE_16BIT_SPI

// 频率档位定义
typedef enum {
    FREQ_1KHZ = 0,      // 1KHz
    FREQ_10KHZ = 1,     // 10KHz  
    FREQ_100KHZ = 2,    // 100KHz
    FREQ_1MHZ = 3,      // 1MHz
    FREQ_1_5MHZ = 4     // 1.5MHz
} freq_level_t;

// 函数声明
void delay_ms_safe(uint32_t ms);
void send_dac_params(freq_level_t freq_level, uint8_t amplitude_percent);

/**
 * 安全的非阻塞延时函数
 */
void delay_ms_safe(uint32_t ms)
{
    // 分段延时，避免长时间阻塞
    uint32_t segments = ms / 10;  // 每段10ms
    uint32_t remainder = ms % 10;
    
    for(uint32_t i = 0; i < segments; i++) {
        // 短延时，不阻塞中断
        for(volatile uint32_t j = 0; j < 80000; j++) {
            __NOP();
        }
        // 给系统处理其他事务的机会
        __NOP();
        __NOP();
    }
    
    // 处理余数
    if(remainder > 0) {
        for(volatile uint32_t j = 0; j < remainder * 8000; j++) {
            __NOP();
        }
    }
}

/**
 * 发送DAC参数到FPGA - 时序优化版本
 * @param freq_level: 频率档位 (0-4)
 * @param amplitude_percent: 幅度百分比 (0-100)
 */
void send_dac_params(freq_level_t freq_level, uint8_t amplitude_percent)
{
    // 参数检查
    if(freq_level > FREQ_1_5MHZ) freq_level = FREQ_1KHZ;
    if(amplitude_percent > 100) amplitude_percent = 100;

    // 计算幅度值 (0-100% 映射到 0-4095)
    uint16_t amplitude = (amplitude_percent * 4095) / 100;

    // 组合16位数据：[15:12]频率档位 + [11:0]幅度
    uint16_t spi_data = ((uint16_t)freq_level << 12) | (amplitude & 0x0FFF);

    // 等待SPI完全空闲
    while (DL_SPI_isBusy(SPI_0_INST));

    // 手动控制CS信号
    DL_GPIO_clearPins(GPIO_SPI_0_CS0_PORT, GPIO_SPI_0_CS0_PIN);  // CS拉低

    // 方法1：尝试16位直接发送（如果支持）
    #ifdef USE_16BIT_SPI
    DL_SPI_transmitData16(SPI_0_INST, spi_data);
    #else
    // 方法2：优化的8位连续发送
    // 预先准备数据，减少计算延时
    uint8_t tx_buffer[2];
    tx_buffer[0] = (spi_data >> 8) & 0xFF;  // 高字节先发
    tx_buffer[1] = spi_data & 0xFF;         // 低字节后发

    // 连续发送，最小化间隔
    for(int i = 0; i < 2; i++) {
        // 等待FIFO有空间
        while(DL_SPI_isTXFIFOFull(SPI_0_INST));
        DL_SPI_transmitData8(SPI_0_INST, tx_buffer[i]);
    }
    #endif

    // 等待传输完成
    while (DL_SPI_isBusy(SPI_0_INST));

    // CS拉高，结束传输
    DL_GPIO_setPins(GPIO_SPI_0_CS0_PORT, GPIO_SPI_0_CS0_PIN);  // CS拉高

    // 延时确保FPGA处理完成（增加延时）
    for(volatile int i = 0; i < 5000; i++) {
        __NOP();
    }
}

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 更长的稳定时间，确保所有外设就绪
    delay_ms_safe(500);
    
    // 点亮LED表示系统就绪
    DL_GPIO_clearPins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
    
    // 发送初始测试数据，验证通信
    send_dac_params(FREQ_10KHZ, 50);
    delay_ms_safe(100);

        send_dac_params(FREQ_10 KHZ, 75);
    
    // 主循环：DAC参数控制演示
    while(1) {
        // 测试序列1：频率扫描，固定75%幅度
        // send_dac_params(FREQ_1KHZ, 75);
        // delay_ms_safe(200);
        
            delay_ms_safe(200);
        
        // send_dac_params(FREQ_100KHZ, 75);
        // delay_ms_safe(200);
        
        // send_dac_params(FREQ_1MHZ, 75);
        // delay_ms_safe(200);
        
        // 测试序列2：幅度扫描，固定10KHz
        // for(int amp = 25; amp <= 100; amp += 25) {
        //     send_dac_params(FREQ_10KHZ, amp);
        //     delay_ms_safe(1000);
        // }
        

    }
}