<TABLE>
<TR  bgcolor="#C0C0C0">
<TH>Hierarchy</TH>
<TH>Input</TH>
<TH>Constant Input</TH>
<TH>Unused Input</TH>
<TH>Floating Input</TH>
<TH>Output</TH>
<TH>Constant Output</TH>
<TH>Unused Output</TH>
<TH>Floating Output</TH>
<TH>Bidir</TH>
<TH>Constant Bidir</TH>
<TH>Unused Bidir</TH>
<TH>Input only Bidir</TH>
<TH>Output only Bidir</TH>
</TR>
<TR >
<TD >u_sel_wave</TD>
<TD >46</TD>
<TD >2</TD>
<TD >0</TD>
<TD >2</TD>
<TD >14</TD>
<TD >2</TD>
<TD >2</TD>
<TD >2</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
<TR >
<TD >u_add_32bit</TD>
<TD >34</TD>
<TD >1</TD>
<TD >0</TD>
<TD >1</TD>
<TD >12</TD>
<TD >1</TD>
<TD >1</TD>
<TD >1</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
<TR >
<TD >ROM_Tri|altsyncram_component|auto_generated</TD>
<TD >13</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >14</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
<TR >
<TD >ROM_Tri</TD>
<TD >13</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >14</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
<TR >
<TD >u_ROM_Sin|altsyncram_component|auto_generated</TD>
<TD >13</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >14</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
<TR >
<TD >u_ROM_Sin</TD>
<TD >13</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >14</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
<TR >
<TD >u_PLL_CLK|altpll_component|auto_generated</TD>
<TD >3</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >5</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
<TR >
<TD >u_PLL_CLK</TD>
<TD >2</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >1</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
<TD >0</TD>
</TR>
</TABLE>
