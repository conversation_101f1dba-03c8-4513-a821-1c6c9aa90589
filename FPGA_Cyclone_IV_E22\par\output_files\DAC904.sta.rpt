TimeQuest Timing Analyzer report for DAC904
Fri Aug 01 03:38:21 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. TimeQuest Timing Analyzer Summary
  3. Parallel Compilation
  4. SDC File List
  5. Clocks
  6. Slow 1200mV 85C Model Fmax Summary
  7. Timing Closure Recommendations
  8. Slow 1200mV 85C Model Setup Summary
  9. Slow 1200mV 85C Model Hold Summary
 10. Slow 1200mV 85C Model Recovery Summary
 11. Slow 1200mV 85C Model Removal Summary
 12. Slow 1200mV 85C Model Minimum Pulse Width Summary
 13. Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 14. Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 15. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'
 16. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'
 17. Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 18. Setup Times
 19. Hold Times
 20. Clock to Output Times
 21. Minimum Clock to Output Times
 22. Slow 1200mV 85C Model Metastability Report
 23. Slow 1200mV 0C Model Fmax Summary
 24. Slow 1200mV 0C Model Setup Summary
 25. Slow 1200mV 0C Model Hold Summary
 26. Slow 1200mV 0C Model Recovery Summary
 27. Slow 1200mV 0C Model Removal Summary
 28. Slow 1200mV 0C Model Minimum Pulse Width Summary
 29. Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 30. Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 31. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 32. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 33. Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 34. Setup Times
 35. Hold Times
 36. Clock to Output Times
 37. Minimum Clock to Output Times
 38. Slow 1200mV 0C Model Metastability Report
 39. Fast 1200mV 0C Model Setup Summary
 40. Fast 1200mV 0C Model Hold Summary
 41. Fast 1200mV 0C Model Recovery Summary
 42. Fast 1200mV 0C Model Removal Summary
 43. Fast 1200mV 0C Model Minimum Pulse Width Summary
 44. Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 45. Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 46. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 47. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 48. Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 49. Setup Times
 50. Hold Times
 51. Clock to Output Times
 52. Minimum Clock to Output Times
 53. Fast 1200mV 0C Model Metastability Report
 54. Multicorner Timing Analysis Summary
 55. Setup Times
 56. Hold Times
 57. Clock to Output Times
 58. Minimum Clock to Output Times
 59. Board Trace Model Assignments
 60. Input Transition Times
 61. Signal Integrity Metrics (Slow 1200mv 0c Model)
 62. Signal Integrity Metrics (Slow 1200mv 85c Model)
 63. Signal Integrity Metrics (Fast 1200mv 0c Model)
 64. Setup Transfers
 65. Hold Transfers
 66. Report TCCS
 67. Report RSKM
 68. Unconstrained Paths
 69. TimeQuest Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+-------------------------------------------------------------------------+
; TimeQuest Timing Analyzer Summary                                       ;
+--------------------+----------------------------------------------------+
; Quartus II Version ; Version 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name      ; DAC904                                             ;
; Device Family      ; Cyclone IV E                                       ;
; Device Name        ; EP4CE6E22C8                                        ;
; Timing Models      ; Final                                              ;
; Delay Model        ; Combined                                           ;
; Rise/Fall Delays   ; Enabled                                            ;
+--------------------+----------------------------------------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-----------------------------------------------------+
; SDC File List                                       ;
+-----------------+--------+--------------------------+
; SDC File Path   ; Status ; Read at                  ;
+-----------------+--------+--------------------------+
; ../doc/SDC1.sdc ; OK     ; Fri Aug 01 03:38:20 2025 ;
+-----------------+--------+--------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clock Name                                            ; Type      ; Period ; Frequency  ; Rise  ; Fall   ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master  ; Source                                                  ; Targets                                                                                                                                                                                 ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; CLK_50M                                               ; Base      ; 20.000 ; 50.0 MHz   ; 0.000 ; 10.000 ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { SYS_CLK }                                                                                                                                                                             ;
; CLK_165M                                              ; Base      ; 6.060  ; 165.02 MHz ; 0.000 ; 3.030  ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { DAC_CLK DAC_DATA[0] DAC_DATA[1] DAC_DATA[2] DAC_DATA[3] DAC_DATA[4] DAC_DATA[5] DAC_DATA[6] DAC_DATA[7] DAC_DATA[8] DAC_DATA[9] DAC_DATA[10] DAC_DATA[11] DAC_DATA[12] DAC_DATA[13] } ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Generated ; 50.000 ; 20.0 MHz   ; 0.000 ; 25.000 ; 50.00      ; 5         ; 2           ;       ;        ;           ;            ; false    ; CLK_50M ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0] ; { u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] }                                                                                                                               ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                                          ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 160.85 MHz ; 160.85 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                                            ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 43.783 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                                            ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.441 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                              ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.934  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.721 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                           ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                    ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 43.783 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 6.141      ;
; 43.813 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 6.111      ;
; 43.929 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.995      ;
; 43.959 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.965      ;
; 44.075 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.849      ;
; 44.105 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.819      ;
; 44.221 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.703      ;
; 44.251 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.673      ;
; 44.367 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.557      ;
; 44.397 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.527      ;
; 44.433 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.491      ;
; 44.463 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.461      ;
; 44.513 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.411      ;
; 44.543 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.381      ;
; 44.579 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.345      ;
; 44.609 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.315      ;
; 44.636 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.288      ;
; 44.659 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.265      ;
; 44.666 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.258      ;
; 44.689 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.235      ;
; 44.725 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.199      ;
; 44.755 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.169      ;
; 44.782 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.142      ;
; 44.805 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.119      ;
; 44.812 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.112      ;
; 44.835 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.089      ;
; 44.871 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.053      ;
; 44.896 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.028      ;
; 44.900 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.024      ;
; 44.901 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 5.023      ;
; 44.928 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.996      ;
; 44.947 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.973      ;
; 44.958 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.966      ;
; 44.977 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.943      ;
; 45.017 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.907      ;
; 45.042 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.882      ;
; 45.046 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.878      ;
; 45.047 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.877      ;
; 45.074 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.850      ;
; 45.093 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.827      ;
; 45.104 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.820      ;
; 45.123 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.797      ;
; 45.163 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.761      ;
; 45.188 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.736      ;
; 45.192 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.732      ;
; 45.193 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.731      ;
; 45.220 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.704      ;
; 45.239 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.681      ;
; 45.250 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.674      ;
; 45.269 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.651      ;
; 45.309 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.615      ;
; 45.334 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.590      ;
; 45.338 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.586      ;
; 45.339 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.585      ;
; 45.366 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.558      ;
; 45.385 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.535      ;
; 45.396 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.528      ;
; 45.415 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.505      ;
; 45.455 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.469      ;
; 45.480 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.444      ;
; 45.484 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.440      ;
; 45.485 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.439      ;
; 45.512 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.412      ;
; 45.531 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[6]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.389      ;
; 45.542 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.382      ;
; 45.561 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[7]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.359      ;
; 45.597 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.323      ;
; 45.626 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.298      ;
; 45.627 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.293      ;
; 45.630 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.294      ;
; 45.658 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.266      ;
; 45.677 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[4]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.243      ;
; 45.688 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.236      ;
; 45.707 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[5]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.213      ;
; 45.743 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.177      ;
; 45.745 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 4.178      ;
; 45.772 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.152      ;
; 45.773 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.147      ;
; 45.775 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 4.148      ;
; 45.776 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.148      ;
; 45.800 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.120      ;
; 45.830 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.090      ;
; 45.889 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.031      ;
; 45.891 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 4.032      ;
; 45.908 ; add_32bit:u_add_32bit|add[0] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 4.015      ;
; 45.918 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.006      ;
; 45.919 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.001      ;
; 45.921 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 4.002      ;
; 45.922 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.077     ; 4.002      ;
; 45.928 ; add_32bit:u_add_32bit|add[3] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.995      ;
; 45.946 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.974      ;
; 45.958 ; add_32bit:u_add_32bit|add[3] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.965      ;
; 45.976 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.944      ;
; 45.984 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[3]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.936      ;
; 46.035 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.885      ;
; 46.037 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.886      ;
; 46.039 ; add_32bit:u_add_32bit|add[0] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.884      ;
; 46.054 ; add_32bit:u_add_32bit|add[2] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.869      ;
; 46.054 ; add_32bit:u_add_32bit|add[0] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.869      ;
; 46.060 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.860      ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.441 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.174      ;
; 0.444 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.177      ;
; 0.456 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.189      ;
; 0.459 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.192      ;
; 0.472 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.205      ;
; 0.472 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.205      ;
; 0.492 ; spi_cs_n_d1                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.784      ;
; 0.496 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.226      ;
; 0.500 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.230      ;
; 0.506 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.236      ;
; 0.510 ; spi_cs_n_d1                   ; spi_cs_n_d2                                                                                                       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.802      ;
; 0.512 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.240      ;
; 0.513 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.230      ;
; 0.515 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.248      ;
; 0.518 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.246      ;
; 0.522 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.252      ;
; 0.531 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.261      ;
; 0.540 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.257      ;
; 0.543 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.271      ;
; 0.547 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.264      ;
; 0.549 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.279      ;
; 0.555 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.272      ;
; 0.559 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.276      ;
; 0.570 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.298      ;
; 0.571 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.288      ;
; 0.596 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.324      ;
; 0.736 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.030      ;
; 0.736 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.030      ;
; 0.736 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.755 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.049      ;
; 0.761 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.762 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.056      ;
; 0.762 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.056      ;
; 0.779 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.496      ;
; 0.782 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.510      ;
; 0.786 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.080      ;
; 0.786 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.080      ;
; 0.787 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[27]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.081      ;
; 0.789 ; spi_cs_n_d2                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.081      ;
; 0.802 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.532      ;
; 0.802 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.519      ;
; 0.816 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.544      ;
; 0.831 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.561      ;
; 0.836 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.553      ;
; 0.846 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.574      ;
; 0.848 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.578      ;
; 0.850 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.580      ;
; 0.862 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.590      ;
; 0.865 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.598      ;
; 0.869 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.597      ;
; 0.878 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.595      ;
; 0.879 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.463      ; 1.596      ;
; 0.939 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.233      ;
; 0.941 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.235      ;
; 0.943 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.237      ;
; 0.944 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.238      ;
; 0.961 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.255      ;
; 0.964 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.258      ;
; 0.964 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.258      ;
; 0.980 ; add_32bit:u_add_32bit|add[25] ; add_32bit:u_add_32bit|add[25]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.274      ;
; 0.995 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.289      ;
; 1.033 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.467      ; 1.754      ;
; 1.036 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.467      ; 1.757      ;
; 1.078 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.467      ; 1.799      ;
; 1.085 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.086      ; 1.383      ;
; 1.090 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.384      ;
; 1.090 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.384      ;
; 1.090 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.384      ;
; 1.090 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.384      ;
; 1.091 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.385      ;
; 1.098 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.392      ;
; 1.098 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.392      ;
; 1.098 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.392      ;
; 1.098 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.392      ;
; 1.099 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.393      ;
; 1.099 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.393      ;
; 1.100 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.394      ;
; 1.107 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.401      ;
; 1.107 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.401      ;
; 1.107 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.401      ;
; 1.107 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.401      ;
; 1.108 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.402      ;
; 1.108 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.402      ;
; 1.109 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.403      ;
; 1.114 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.474      ; 1.842      ;
; 1.121 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.479      ; 1.854      ;
; 1.124 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[23]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.418      ;
; 1.125 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.419      ;
; 1.126 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.476      ; 1.856      ;
; 1.133 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.427      ;
; 1.134 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.428      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'                                    ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'                                                                                               ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.954  ; 9.954        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.977  ; 9.977        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.022 ; 10.022       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.046 ; 10.046       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                 ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.723 ; 24.958       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.723 ; 24.943       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.724 ; 24.959       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.724 ; 24.959       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.724 ; 24.959       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[12]                                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[13]                                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[14]                                                                                                      ;
; 24.724 ; 24.944       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[15]                                                                                                      ;
; 24.725 ; 24.960       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.725 ; 24.960       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.725 ; 24.960       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d1                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d2                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[0]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[10]                                                                                                      ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[11]                                                                                                      ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[1]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[2]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[3]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[4]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[5]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[6]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[7]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[8]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[9]                                                                                                       ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_valid                                                                                                    ;
; 24.726 ; 24.961       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.726 ; 24.961       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.726 ; 24.961       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.726 ; 24.961       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.727 ; 24.962       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.727 ; 24.962       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.727 ; 24.962       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.727 ; 24.962       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.728 ; 24.963       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.729 ; 24.964       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.729 ; 24.964       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.732 ; 24.967       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.779 ; 25.014       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.779 ; 25.014       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.781 ; 25.016       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.782 ; 25.017       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.782 ; 25.017       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.784 ; 25.019       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.784 ; 25.019       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.784 ; 25.019       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.784 ; 25.019       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.784 ; 25.019       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.785 ; 25.020       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.785 ; 25.020       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.786 ; 25.021       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.786 ; 25.021       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 2.814 ; 3.097 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 5.380 ; 5.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -1.922 ; -2.164 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -4.549 ; -4.788 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 3.199  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 13.429 ; 13.477 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 10.663 ; 10.417 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 10.591 ; 10.480 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 10.168 ; 10.055 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 10.611 ; 10.452 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 10.890 ; 10.691 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 11.954 ; 11.872 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 12.348 ; 12.227 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 13.137 ; 13.006 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 13.069 ; 13.004 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 13.272 ; 13.145 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 13.204 ; 13.156 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 13.429 ; 13.313 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 13.312 ; 13.257 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 13.343 ; 13.477 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 6.393  ; 6.284  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.889  ; 5.657  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.717  ; 5.579  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.745  ; 5.593  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.685  ; 5.530  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 6.022  ; 5.817  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.860  ; 5.662  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.739  ; 5.577  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.736  ; 5.585  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 6.077  ; 5.877  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 6.393  ; 6.284  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.983  ; 6.238  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.780  ; 6.013  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 7.128  ; 7.039  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.604  ; 5.607  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.839  ; 5.706  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.873  ; 5.741  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 7.128  ; 7.039  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 6.137  ; 5.957  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 3.157  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.703 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 6.130 ; 6.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 6.605 ; 6.367 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 6.536 ; 6.428 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 6.130 ; 6.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 6.555 ; 6.401 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 6.823 ; 6.631 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 7.903 ; 7.827 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 7.107 ; 6.955 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 7.163 ; 7.044 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 7.410 ; 7.288 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 7.399 ; 7.284 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 7.462 ; 7.356 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 7.453 ; 7.349 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 7.437 ; 7.325 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 6.892 ; 7.022 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 5.079 ; 4.928 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.275 ; 5.050 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.110 ; 4.975 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.137 ; 4.988 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.079 ; 4.928 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.402 ; 5.203 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.241 ; 5.049 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.131 ; 4.974 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.128 ; 4.981 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.455 ; 5.261 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.758 ; 5.652 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.363 ; 5.610 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.168 ; 5.394 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 4.998 ; 5.003 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 4.998 ; 5.003 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.226 ; 5.096 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.259 ; 5.130 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 6.464 ; 6.376 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 5.514 ; 5.339 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 2.662 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


----------------------------------------------
; Slow 1200mV 85C Model Metastability Report ;
----------------------------------------------
No synchronizer chains to report.


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                           ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 177.97 MHz ; 177.97 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 44.381 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.422 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.943  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.719 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                            ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                    ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 44.381 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.549      ;
; 44.420 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.510      ;
; 44.507 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.423      ;
; 44.546 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.384      ;
; 44.633 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.297      ;
; 44.672 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.258      ;
; 44.759 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.171      ;
; 44.798 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.132      ;
; 44.885 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.045      ;
; 44.924 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.006      ;
; 44.997 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.933      ;
; 45.011 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.919      ;
; 45.036 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.894      ;
; 45.050 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.880      ;
; 45.123 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.807      ;
; 45.137 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.793      ;
; 45.157 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.773      ;
; 45.162 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.768      ;
; 45.176 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.754      ;
; 45.196 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.734      ;
; 45.249 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.681      ;
; 45.263 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.667      ;
; 45.283 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.647      ;
; 45.288 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.642      ;
; 45.302 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.628      ;
; 45.322 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.608      ;
; 45.375 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.555      ;
; 45.377 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.553      ;
; 45.381 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.549      ;
; 45.389 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.541      ;
; 45.409 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.521      ;
; 45.414 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.516      ;
; 45.428 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.502      ;
; 45.448 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.482      ;
; 45.501 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.429      ;
; 45.503 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.427      ;
; 45.507 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.423      ;
; 45.515 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.415      ;
; 45.535 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.395      ;
; 45.540 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.390      ;
; 45.554 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.376      ;
; 45.574 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.356      ;
; 45.627 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.303      ;
; 45.629 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.301      ;
; 45.633 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.297      ;
; 45.641 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.289      ;
; 45.661 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.269      ;
; 45.666 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.264      ;
; 45.680 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.250      ;
; 45.700 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.230      ;
; 45.753 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.177      ;
; 45.755 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.175      ;
; 45.759 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.171      ;
; 45.767 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.163      ;
; 45.787 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.143      ;
; 45.792 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.138      ;
; 45.806 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.124      ;
; 45.826 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.104      ;
; 45.879 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.051      ;
; 45.881 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.049      ;
; 45.885 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.045      ;
; 45.893 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[6]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.037      ;
; 45.913 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.017      ;
; 45.918 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.012      ;
; 45.932 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[7]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.998      ;
; 45.952 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.978      ;
; 46.005 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.925      ;
; 46.007 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.923      ;
; 46.011 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.919      ;
; 46.019 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[4]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.911      ;
; 46.039 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.891      ;
; 46.044 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.886      ;
; 46.058 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[5]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.872      ;
; 46.078 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.852      ;
; 46.131 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.799      ;
; 46.133 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.797      ;
; 46.137 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.793      ;
; 46.165 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.765      ;
; 46.170 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.760      ;
; 46.204 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.726      ;
; 46.253 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.676      ;
; 46.255 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.674      ;
; 46.257 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.673      ;
; 46.263 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.667      ;
; 46.291 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.639      ;
; 46.296 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.634      ;
; 46.302 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.628      ;
; 46.330 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.600      ;
; 46.338 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[3]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.592      ;
; 46.372 ; add_32bit:u_add_32bit|add[0] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.557      ;
; 46.379 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.550      ;
; 46.381 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.548      ;
; 46.383 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.547      ;
; 46.389 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.541      ;
; 46.409 ; add_32bit:u_add_32bit|add[3] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.520      ;
; 46.417 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.513      ;
; 46.422 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.508      ;
; 46.428 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.502      ;
; 46.434 ; add_32bit:u_add_32bit|add[3] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.495      ;
; 46.456 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.474      ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                 ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.422 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.076      ;
; 0.431 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.085      ;
; 0.432 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.086      ;
; 0.437 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.091      ;
; 0.445 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.099      ;
; 0.448 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.102      ;
; 0.454 ; spi_cs_n_d1                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.721      ;
; 0.465 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.119      ;
; 0.468 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.122      ;
; 0.472 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.126      ;
; 0.477 ; spi_cs_n_d1                   ; spi_cs_n_d2                                                                                                       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.744      ;
; 0.481 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.122      ;
; 0.486 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.140      ;
; 0.486 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.140      ;
; 0.487 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.139      ;
; 0.489 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.141      ;
; 0.499 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.153      ;
; 0.506 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.147      ;
; 0.509 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.161      ;
; 0.512 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.166      ;
; 0.519 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.160      ;
; 0.520 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.161      ;
; 0.524 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.165      ;
; 0.533 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.185      ;
; 0.534 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.175      ;
; 0.555 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.207      ;
; 0.684 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.685 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.688 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.705 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.706 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.974      ;
; 0.707 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.975      ;
; 0.708 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.976      ;
; 0.720 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.361      ;
; 0.720 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.372      ;
; 0.730 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.998      ;
; 0.730 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[27]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.998      ;
; 0.730 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.998      ;
; 0.733 ; spi_cs_n_d2                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.000      ;
; 0.737 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.391      ;
; 0.738 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.379      ;
; 0.755 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.407      ;
; 0.759 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.413      ;
; 0.770 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.411      ;
; 0.776 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.430      ;
; 0.778 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.432      ;
; 0.778 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.430      ;
; 0.797 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.449      ;
; 0.799 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.451      ;
; 0.804 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.458      ;
; 0.805 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.446      ;
; 0.808 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.411      ; 1.449      ;
; 0.839 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.107      ;
; 0.847 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.115      ;
; 0.853 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.121      ;
; 0.858 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.126      ;
; 0.868 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.136      ;
; 0.869 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.137      ;
; 0.879 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.147      ;
; 0.883 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.151      ;
; 0.896 ; add_32bit:u_add_32bit|add[25] ; add_32bit:u_add_32bit|add[25]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.164      ;
; 0.945 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.415      ; 1.590      ;
; 0.947 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.415      ; 1.592      ;
; 0.984 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.415      ; 1.629      ;
; 1.004 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.272      ;
; 1.004 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.272      ;
; 1.004 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.272      ;
; 1.005 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.273      ;
; 1.005 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.273      ;
; 1.005 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.273      ;
; 1.005 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.273      ;
; 1.006 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.274      ;
; 1.006 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.274      ;
; 1.006 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.274      ;
; 1.006 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.274      ;
; 1.009 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.277      ;
; 1.010 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.278      ;
; 1.011 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.422      ; 1.663      ;
; 1.019 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.287      ;
; 1.021 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.675      ;
; 1.021 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.289      ;
; 1.022 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.290      ;
; 1.022 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.290      ;
; 1.023 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.677      ;
; 1.023 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.291      ;
; 1.023 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.291      ;
; 1.023 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.291      ;
; 1.026 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[23]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.294      ;
; 1.028 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.296      ;
; 1.041 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.309      ;
; 1.043 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.697      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.975  ; 9.975        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.991  ; 9.991        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.008 ; 10.008       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.025 ; 10.025       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[12]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[13]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[14]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[15]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[0]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[10]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[11]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[1]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[2]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[3]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[4]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[5]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[6]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[7]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[8]                                                                                                       ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[9]                                                                                                       ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d1                                                                                                       ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d2                                                                                                       ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_valid                                                                                                    ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.725 ; 24.955       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.725 ; 24.955       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.727 ; 24.957       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.732 ; 24.962       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.732 ; 24.962       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.732 ; 24.962       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.732 ; 24.962       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.801 ; 25.031       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.801 ; 25.031       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.802 ; 25.032       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.803 ; 25.033       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.803 ; 25.033       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 2.477 ; 2.815 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 4.758 ; 4.818 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -1.697 ; -1.973 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -4.017 ; -4.081 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.980  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 12.480 ; 12.496 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 9.889  ; 9.524  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 9.826  ; 9.591  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 9.410  ; 9.196  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 9.833  ; 9.561  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 10.107 ; 9.762  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 11.001 ; 10.762 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 11.497 ; 11.345 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 12.224 ; 12.027 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 12.161 ; 12.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 12.341 ; 12.144 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 12.275 ; 12.155 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 12.480 ; 12.290 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 12.369 ; 12.236 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 12.321 ; 12.496 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 5.973  ; 5.862  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.537  ; 5.178  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.357  ; 5.115  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.379  ; 5.127  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.320  ; 5.066  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.665  ; 5.320  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.508  ; 5.174  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.382  ; 5.109  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.369  ; 5.123  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.693  ; 5.391  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.973  ; 5.766  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.474  ; 5.862  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.290  ; 5.624  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 6.630  ; 6.482  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.153  ; 5.215  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.465  ; 5.234  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.507  ; 5.255  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 6.630  ; 6.482  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 5.751  ; 5.461  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 2.903  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.523 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 5.717 ; 5.512 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 6.178 ; 5.827 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 6.117 ; 5.891 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 5.717 ; 5.512 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 6.123 ; 5.862 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 6.386 ; 6.055 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 7.296 ; 7.068 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 6.524 ; 6.345 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 6.600 ; 6.418 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 6.824 ; 6.640 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 6.807 ; 6.625 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 6.860 ; 6.695 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 6.858 ; 6.683 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 6.837 ; 6.662 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 6.288 ; 6.453 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 4.728 ; 4.513 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 4.966 ; 4.621 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 4.794 ; 4.560 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 4.815 ; 4.571 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 4.758 ; 4.513 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.089 ; 4.757 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 4.935 ; 4.613 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 4.818 ; 4.554 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 4.805 ; 4.567 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.116 ; 4.825 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.385 ; 5.185 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 4.904 ; 5.278 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 4.728 ; 5.050 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 4.597 ; 4.658 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 4.597 ; 4.658 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 4.898 ; 4.675 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 4.938 ; 4.695 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 6.016 ; 5.872 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 5.173 ; 4.893 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 2.447 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Slow 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 47.135 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.147 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 2.060  ; 0.000         ;
; CLK_50M                                               ; 9.594  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.734 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                            ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                    ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 47.135 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.817      ;
; 47.199 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.753      ;
; 47.203 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.749      ;
; 47.267 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.685      ;
; 47.271 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.681      ;
; 47.335 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.617      ;
; 47.339 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.613      ;
; 47.403 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.549      ;
; 47.407 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.545      ;
; 47.471 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.481      ;
; 47.475 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.477      ;
; 47.482 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.470      ;
; 47.539 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.413      ;
; 47.543 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.409      ;
; 47.546 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.406      ;
; 47.550 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.402      ;
; 47.568 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.384      ;
; 47.607 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.345      ;
; 47.611 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.341      ;
; 47.614 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.338      ;
; 47.618 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.334      ;
; 47.632 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.320      ;
; 47.636 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.316      ;
; 47.669 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.283      ;
; 47.675 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.277      ;
; 47.678 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.273      ;
; 47.682 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.270      ;
; 47.686 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.266      ;
; 47.700 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.252      ;
; 47.704 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.248      ;
; 47.733 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.219      ;
; 47.737 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.215      ;
; 47.742 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.209      ;
; 47.746 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.205      ;
; 47.750 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.202      ;
; 47.754 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.198      ;
; 47.768 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.184      ;
; 47.772 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.180      ;
; 47.801 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.151      ;
; 47.805 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.147      ;
; 47.810 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.141      ;
; 47.814 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.137      ;
; 47.818 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.134      ;
; 47.822 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.130      ;
; 47.836 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.116      ;
; 47.840 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.112      ;
; 47.869 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.083      ;
; 47.873 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.079      ;
; 47.878 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.073      ;
; 47.882 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.069      ;
; 47.886 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.066      ;
; 47.890 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.062      ;
; 47.904 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.048      ;
; 47.908 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.044      ;
; 47.937 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.015      ;
; 47.941 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.011      ;
; 47.946 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.005      ;
; 47.950 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[7]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.001      ;
; 47.954 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.998      ;
; 47.958 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.994      ;
; 47.972 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.980      ;
; 47.976 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.976      ;
; 48.005 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.947      ;
; 48.009 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.943      ;
; 48.014 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[6]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.937      ;
; 48.018 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[5]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.933      ;
; 48.022 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.930      ;
; 48.025 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.926      ;
; 48.040 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.912      ;
; 48.044 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.908      ;
; 48.073 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.879      ;
; 48.077 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.875      ;
; 48.081 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.870      ;
; 48.082 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[4]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.869      ;
; 48.085 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.866      ;
; 48.089 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.862      ;
; 48.093 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.858      ;
; 48.108 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.844      ;
; 48.111 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.840      ;
; 48.141 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.811      ;
; 48.145 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.807      ;
; 48.149 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.802      ;
; 48.153 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.798      ;
; 48.157 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.794      ;
; 48.161 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.790      ;
; 48.163 ; add_32bit:u_add_32bit|add[3] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.788      ;
; 48.167 ; add_32bit:u_add_32bit|add[3] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.784      ;
; 48.175 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.776      ;
; 48.179 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.772      ;
; 48.187 ; add_32bit:u_add_32bit|add[0] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.764      ;
; 48.209 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.743      ;
; 48.212 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.739      ;
; 48.217 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.734      ;
; 48.221 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.730      ;
; 48.225 ; add_32bit:u_add_32bit|add[0] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.726      ;
; 48.225 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.726      ;
; 48.229 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.722      ;
; 48.231 ; add_32bit:u_add_32bit|add[5] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.720      ;
; 48.231 ; add_32bit:u_add_32bit|add[3] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.720      ;
; 48.232 ; add_32bit:u_add_32bit|add[9] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.719      ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                 ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.147 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.476      ;
; 0.151 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.480      ;
; 0.160 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.489      ;
; 0.162 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.491      ;
; 0.163 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.492      ;
; 0.163 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.492      ;
; 0.168 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.496      ;
; 0.171 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.499      ;
; 0.171 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.499      ;
; 0.175 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.503      ;
; 0.179 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.507      ;
; 0.180 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.501      ;
; 0.181 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.510      ;
; 0.184 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.511      ;
; 0.186 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.513      ;
; 0.188 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.516      ;
; 0.192 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.519      ;
; 0.193 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.514      ;
; 0.195 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.516      ;
; 0.197 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.518      ;
; 0.198 ; spi_cs_n_d1                   ; spi_cs_n_d2                                                                                                       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.318      ;
; 0.199 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.520      ;
; 0.203 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.524      ;
; 0.203 ; spi_cs_n_d1                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.323      ;
; 0.205 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.532      ;
; 0.213 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.540      ;
; 0.292 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.298 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.419      ;
; 0.304 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.632      ;
; 0.304 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.305 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.425      ;
; 0.306 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.426      ;
; 0.308 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.629      ;
; 0.313 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.640      ;
; 0.315 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.643      ;
; 0.315 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.636      ;
; 0.316 ; spi_cs_n_d2                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.436      ;
; 0.318 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.438      ;
; 0.318 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[27]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.438      ;
; 0.318 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.438      ;
; 0.321 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.648      ;
; 0.324 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.652      ;
; 0.326 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.654      ;
; 0.326 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.647      ;
; 0.336 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.663      ;
; 0.341 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.662      ;
; 0.342 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.669      ;
; 0.344 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.671      ;
; 0.345 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.674      ;
; 0.345 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.217      ; 0.666      ;
; 0.361 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.482      ;
; 0.361 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.482      ;
; 0.362 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.483      ;
; 0.363 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.484      ;
; 0.373 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.493      ;
; 0.373 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.493      ;
; 0.375 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.495      ;
; 0.378 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.499      ;
; 0.381 ; add_32bit:u_add_32bit|add[25] ; add_32bit:u_add_32bit|add[25]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.501      ;
; 0.435 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.220      ; 0.759      ;
; 0.436 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.220      ; 0.760      ;
; 0.437 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.223      ; 0.764      ;
; 0.440 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.768      ;
; 0.440 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.561      ;
; 0.441 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.562      ;
; 0.441 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.562      ;
; 0.442 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.563      ;
; 0.442 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.562      ;
; 0.443 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.563      ;
; 0.446 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.775      ;
; 0.451 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.220      ; 0.775      ;
; 0.451 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.572      ;
; 0.451 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.572      ;
; 0.452 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.573      ;
; 0.452 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.573      ;
; 0.452 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.572      ;
; 0.452 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.573      ;
; 0.452 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.573      ;
; 0.454 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.783      ;
; 0.454 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.575      ;
; 0.454 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.575      ;
; 0.455 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.576      ;
; 0.455 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.576      ;
; 0.455 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.576      ;
; 0.455 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.576      ;
; 0.455 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.575      ;
; 0.464 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[23]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.584      ;
; 0.465 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.585      ;
; 0.466 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.586      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.620  ; 9.620        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.622  ; 9.622        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.378 ; 10.378       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.380 ; 10.380       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.737 ; 24.967       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.737 ; 24.967       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d1                                                                                                       ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d2                                                                                                       ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_valid                                                                                                    ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[0]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[10]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[11]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[12]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[13]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[14]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[15]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[1]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[2]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[3]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[4]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[5]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[6]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[7]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[8]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[9]                                                                                                       ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 1.397 ; 1.753 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 2.616 ; 3.244 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -0.989 ; -1.345 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -2.232 ; -2.859 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                  ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.519 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 5.955 ; 6.007 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 4.727 ; 4.819 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 4.759 ; 4.890 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 4.546 ; 4.643 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 4.742 ; 4.860 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 4.839 ; 4.963 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 5.610 ; 5.766 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 5.516 ; 5.582 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 5.747 ; 5.826 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 5.841 ; 5.898 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 5.808 ; 5.887 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 5.905 ; 5.970 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 5.877 ; 5.966 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 5.951 ; 6.007 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 5.955 ; 5.887 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 2.925 ; 3.113 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.605 ; 2.714 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.575 ; 2.695 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.569 ; 2.688 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.538 ; 2.653 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.671 ; 2.803 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.584 ; 2.708 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 2.551 ; 2.673 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 2.579 ; 2.699 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.721 ; 2.847 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.925 ; 3.113 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.886 ; 2.761 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.774 ; 2.656 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 3.350 ; 3.611 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.760 ; 2.608 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.626 ; 2.757 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.644 ; 2.782 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 3.350 ; 3.611 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 2.765 ; 2.904 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.584 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 2.726 ; 2.820 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 2.899 ; 2.988 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 2.930 ; 3.057 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 2.726 ; 2.820 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 2.913 ; 3.028 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 3.006 ; 3.127 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 3.784 ; 3.937 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 3.143 ; 3.201 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 3.179 ; 3.244 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 3.298 ; 3.368 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 3.284 ; 3.348 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 3.314 ; 3.393 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 3.307 ; 3.383 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 3.302 ; 3.372 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 3.148 ; 3.093 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 2.258 ; 2.368 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.322 ; 2.427 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.294 ; 2.409 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.288 ; 2.402 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.258 ; 2.368 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.386 ; 2.512 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.303 ; 2.422 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 2.270 ; 2.388 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 2.297 ; 2.413 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.434 ; 2.555 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.630 ; 2.810 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.593 ; 2.472 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.485 ; 2.372 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 2.342 ; 2.324 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.470 ; 2.324 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.342 ; 2.468 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.359 ; 2.492 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 3.037 ; 3.287 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 2.476 ; 2.609 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Fast 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                                ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Clock                                                  ; Setup  ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Worst-case Slack                                       ; 43.783 ; 0.147 ; N/A      ; N/A     ; 1.616               ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 1.616               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 9.594               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 43.783 ; 0.147 ; N/A      ; N/A     ; 24.719              ;
; Design-wide TNS                                        ; 0.0    ; 0.0   ; 0.0      ; 0.0     ; 0.0                 ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000  ; 0.000 ; N/A      ; N/A     ; 0.000               ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 2.814 ; 3.097 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 5.380 ; 5.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -0.989 ; -1.345 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -2.232 ; -2.859 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 3.199  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 13.429 ; 13.477 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 10.663 ; 10.417 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 10.591 ; 10.480 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 10.168 ; 10.055 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 10.611 ; 10.452 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 10.890 ; 10.691 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 11.954 ; 11.872 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 12.348 ; 12.227 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 13.137 ; 13.006 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 13.069 ; 13.004 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 13.272 ; 13.145 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 13.204 ; 13.156 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 13.429 ; 13.313 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 13.312 ; 13.257 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 13.343 ; 13.477 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 6.393  ; 6.284  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.889  ; 5.657  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.717  ; 5.579  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.745  ; 5.593  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.685  ; 5.530  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 6.022  ; 5.817  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.860  ; 5.662  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.739  ; 5.577  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.736  ; 5.585  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 6.077  ; 5.877  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 6.393  ; 6.284  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.983  ; 6.238  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.780  ; 6.013  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 7.128  ; 7.039  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.604  ; 5.607  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.839  ; 5.706  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.873  ; 5.741  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 7.128  ; 7.039  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 6.137  ; 5.957  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 3.157  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 2.726 ; 2.820 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 2.899 ; 2.988 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 2.930 ; 3.057 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 2.726 ; 2.820 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 2.913 ; 3.028 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 3.006 ; 3.127 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 3.784 ; 3.937 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 3.143 ; 3.201 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 3.179 ; 3.244 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 3.298 ; 3.368 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 3.284 ; 3.348 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 3.314 ; 3.393 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 3.307 ; 3.383 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 3.302 ; 3.372 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 3.148 ; 3.093 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 2.258 ; 2.368 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.322 ; 2.427 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.294 ; 2.409 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.288 ; 2.402 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.258 ; 2.368 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.386 ; 2.512 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.303 ; 2.422 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 2.270 ; 2.388 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 2.297 ; 2.413 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.434 ; 2.555 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.630 ; 2.810 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.593 ; 2.472 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.485 ; 2.372 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 2.342 ; 2.324 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.470 ; 2.324 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.342 ; 2.468 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.359 ; 2.492 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 3.037 ; 3.287 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 2.476 ; 2.609 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                          ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin                 ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; PD                  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_CLK             ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[0]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[1]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[2]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[3]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[4]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[5]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[6]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[7]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[8]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[9]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[10]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[11]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[12]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[13]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_spi_valid     ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[4]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[5]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[6]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[7]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[8]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[9]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[10] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[11] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; KEY_IN[0]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[1]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[2]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_RST                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_CLK                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_sclk                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_cs_n                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_mosi                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                                                           ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 2783     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                                            ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 2783     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths                            ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 1     ; 1    ;
; Unconstrained Input Ports       ; 3     ; 3    ;
; Unconstrained Input Port Paths  ; 90    ; 90   ;
; Unconstrained Output Ports      ; 32    ; 32   ;
; Unconstrained Output Port Paths ; 382   ; 382  ;
+---------------------------------+-------+------+


+------------------------------------+
; TimeQuest Timing Analyzer Messages ;
+------------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Fri Aug 01 03:38:20 2025
Info: Command: quartus_sta DAC904 -c DAC904
Info: qsta_default_script.tcl version: #1
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Info (332104): Reading SDC File: '../doc/SDC1.sdc'
Info (332110): Deriving PLL clocks
    Info (332110): create_generated_clock -source {u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]} {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]}
Info (332151): Clock uncertainty is not calculated until you update the timing netlist.
Warning (332060): Node: spi_sclk was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info: Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Info (332146): Worst-case setup slack is 43.783
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    43.783               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.441
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.441               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.934               0.000 CLK_50M 
    Info (332119):    24.721               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Warning (332060): Node: spi_sclk was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 44.381
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    44.381               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.422
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.422               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.943               0.000 CLK_50M 
    Info (332119):    24.719               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Fast 1200mV 0C Model
Warning (332060): Node: spi_sclk was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 47.135
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    47.135               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.147
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.147               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 2.060
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     2.060               0.000 CLK_165M 
    Info (332119):     9.594               0.000 CLK_50M 
    Info (332119):    24.734               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 4 warnings
    Info: Peak virtual memory: 4644 megabytes
    Info: Processing ended: Fri Aug 01 03:38:21 2025
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


