TimeQuest Timing Analyzer report for DAC904
Fri Aug 01 03:12:54 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. TimeQuest Timing Analyzer Summary
  3. Parallel Compilation
  4. SDC File List
  5. Clocks
  6. Slow 1200mV 85C Model Fmax Summary
  7. Timing Closure Recommendations
  8. Slow 1200mV 85C Model Setup Summary
  9. Slow 1200mV 85C Model Hold Summary
 10. Slow 1200mV 85C Model Recovery Summary
 11. Slow 1200mV 85C Model Removal Summary
 12. Slow 1200mV 85C Model Minimum Pulse Width Summary
 13. Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 14. Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 15. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'
 16. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'
 17. Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 18. Setup Times
 19. Hold Times
 20. Clock to Output Times
 21. Minimum Clock to Output Times
 22. Slow 1200mV 85C Model Metastability Report
 23. Slow 1200mV 0C Model Fmax Summary
 24. Slow 1200mV 0C Model Setup Summary
 25. Slow 1200mV 0C Model Hold Summary
 26. Slow 1200mV 0C Model Recovery Summary
 27. Slow 1200mV 0C Model Removal Summary
 28. Slow 1200mV 0C Model Minimum Pulse Width Summary
 29. Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 30. Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 31. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 32. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 33. Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 34. Setup Times
 35. Hold Times
 36. Clock to Output Times
 37. Minimum Clock to Output Times
 38. Slow 1200mV 0C Model Metastability Report
 39. Fast 1200mV 0C Model Setup Summary
 40. Fast 1200mV 0C Model Hold Summary
 41. Fast 1200mV 0C Model Recovery Summary
 42. Fast 1200mV 0C Model Removal Summary
 43. Fast 1200mV 0C Model Minimum Pulse Width Summary
 44. Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 45. Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 46. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 47. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 48. Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 49. Setup Times
 50. Hold Times
 51. Clock to Output Times
 52. Minimum Clock to Output Times
 53. Fast 1200mV 0C Model Metastability Report
 54. Multicorner Timing Analysis Summary
 55. Setup Times
 56. Hold Times
 57. Clock to Output Times
 58. Minimum Clock to Output Times
 59. Board Trace Model Assignments
 60. Input Transition Times
 61. Signal Integrity Metrics (Slow 1200mv 0c Model)
 62. Signal Integrity Metrics (Slow 1200mv 85c Model)
 63. Signal Integrity Metrics (Fast 1200mv 0c Model)
 64. Setup Transfers
 65. Hold Transfers
 66. Report TCCS
 67. Report RSKM
 68. Unconstrained Paths
 69. TimeQuest Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+-------------------------------------------------------------------------+
; TimeQuest Timing Analyzer Summary                                       ;
+--------------------+----------------------------------------------------+
; Quartus II Version ; Version 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name      ; DAC904                                             ;
; Device Family      ; Cyclone IV E                                       ;
; Device Name        ; EP4CE6E22C8                                        ;
; Timing Models      ; Final                                              ;
; Delay Model        ; Combined                                           ;
; Rise/Fall Delays   ; Enabled                                            ;
+--------------------+----------------------------------------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-----------------------------------------------------+
; SDC File List                                       ;
+-----------------+--------+--------------------------+
; SDC File Path   ; Status ; Read at                  ;
+-----------------+--------+--------------------------+
; ../doc/SDC1.sdc ; OK     ; Fri Aug 01 03:12:53 2025 ;
+-----------------+--------+--------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clock Name                                            ; Type      ; Period ; Frequency  ; Rise  ; Fall   ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master  ; Source                                                  ; Targets                                                                                                                                                                                 ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; CLK_50M                                               ; Base      ; 20.000 ; 50.0 MHz   ; 0.000 ; 10.000 ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { SYS_CLK }                                                                                                                                                                             ;
; CLK_165M                                              ; Base      ; 6.060  ; 165.02 MHz ; 0.000 ; 3.030  ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { DAC_CLK DAC_DATA[0] DAC_DATA[1] DAC_DATA[2] DAC_DATA[3] DAC_DATA[4] DAC_DATA[5] DAC_DATA[6] DAC_DATA[7] DAC_DATA[8] DAC_DATA[9] DAC_DATA[10] DAC_DATA[11] DAC_DATA[12] DAC_DATA[13] } ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Generated ; 50.000 ; 20.0 MHz   ; 0.000 ; 25.000 ; 50.00      ; 5         ; 2           ;       ;        ;           ;            ; false    ; CLK_50M ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0] ; { u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] }                                                                                                                               ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                                          ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 161.24 MHz ; 161.24 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                                            ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 43.798 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                                            ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.439 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                              ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.934  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.716 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                           ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                    ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 43.798 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 6.122      ;
; 43.944 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.976      ;
; 43.969 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.951      ;
; 44.076 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.844      ;
; 44.090 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.830      ;
; 44.115 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.805      ;
; 44.222 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.698      ;
; 44.236 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.684      ;
; 44.252 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.668      ;
; 44.261 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.659      ;
; 44.330 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.590      ;
; 44.368 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.552      ;
; 44.382 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.538      ;
; 44.398 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.522      ;
; 44.407 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.513      ;
; 44.469 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.451      ;
; 44.476 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.444      ;
; 44.506 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.414      ;
; 44.514 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.406      ;
; 44.528 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.392      ;
; 44.544 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.376      ;
; 44.553 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.367      ;
; 44.615 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.305      ;
; 44.622 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.298      ;
; 44.645 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.275      ;
; 44.652 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.268      ;
; 44.660 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.260      ;
; 44.674 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.246      ;
; 44.690 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.230      ;
; 44.699 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.221      ;
; 44.761 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.159      ;
; 44.768 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.152      ;
; 44.791 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.129      ;
; 44.798 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.122      ;
; 44.806 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.114      ;
; 44.820 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.100      ;
; 44.836 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.084      ;
; 44.845 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.075      ;
; 44.907 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.013      ;
; 44.914 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 5.006      ;
; 44.937 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.983      ;
; 44.944 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.976      ;
; 44.952 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.968      ;
; 44.967 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.954      ;
; 44.982 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.938      ;
; 44.991 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.929      ;
; 45.053 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.867      ;
; 45.060 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.860      ;
; 45.083 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.837      ;
; 45.090 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.830      ;
; 45.098 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.822      ;
; 45.113 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.808      ;
; 45.128 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.792      ;
; 45.138 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.783      ;
; 45.199 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.721      ;
; 45.206 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.714      ;
; 45.229 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.691      ;
; 45.236 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.684      ;
; 45.245 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.676      ;
; 45.259 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.662      ;
; 45.274 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.646      ;
; 45.284 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.637      ;
; 45.345 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.575      ;
; 45.352 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.568      ;
; 45.375 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.545      ;
; 45.382 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.538      ;
; 45.391 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.530      ;
; 45.405 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.516      ;
; 45.421 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.500      ;
; 45.430 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.491      ;
; 45.491 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.429      ;
; 45.499 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.422      ;
; 45.508 ; spi_cs_n_d2                  ; spi_data[14]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.422      ;
; 45.508 ; spi_cs_n_d2                  ; spi_data[15]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.422      ;
; 45.508 ; spi_cs_n_d2                  ; spi_data[12]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.422      ;
; 45.508 ; spi_cs_n_d2                  ; spi_data[13]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.422      ;
; 45.521 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.399      ;
; 45.528 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.392      ;
; 45.537 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.384      ;
; 45.551 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[7]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.370      ;
; 45.567 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.354      ;
; 45.571 ; spi_cs_n_d1                  ; spi_data[14]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.359      ;
; 45.571 ; spi_cs_n_d1                  ; spi_data[15]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.359      ;
; 45.571 ; spi_cs_n_d1                  ; spi_data[12]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.359      ;
; 45.571 ; spi_cs_n_d1                  ; spi_data[13]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.359      ;
; 45.576 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.345      ;
; 45.638 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.283      ;
; 45.645 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.276      ;
; 45.667 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.253      ;
; 45.675 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.246      ;
; 45.683 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.238      ;
; 45.697 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[5]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.224      ;
; 45.713 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.208      ;
; 45.722 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[6]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.199      ;
; 45.741 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.178      ;
; 45.771 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.148      ;
; 45.784 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.137      ;
; 45.791 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.130      ;
; 45.814 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.107      ;
; 45.821 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.080     ; 4.100      ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.439 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.173      ;
; 0.445 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.179      ;
; 0.455 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.189      ;
; 0.458 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.192      ;
; 0.492 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.227      ;
; 0.502 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.236      ;
; 0.504 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.240      ;
; 0.506 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.241      ;
; 0.509 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.244      ;
; 0.509 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.247      ;
; 0.511 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.249      ;
; 0.518 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.254      ;
; 0.521 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.257      ;
; 0.523 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.258      ;
; 0.534 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.269      ;
; 0.543 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.279      ;
; 0.546 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.282      ;
; 0.557 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.295      ;
; 0.565 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.303      ;
; 0.588 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.324      ;
; 0.632 ; spi_cs_n_d1                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.925      ;
; 0.705 ; spi_cs_n_d1                   ; spi_cs_n_d2                                                                                                       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.998      ;
; 0.735 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.737 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.738 ; spi_cs_n_d2                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.756 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.049      ;
; 0.762 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.055      ;
; 0.763 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.056      ;
; 0.763 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.056      ;
; 0.764 ; add_32bit:u_add_32bit|add[29] ; add_32bit:u_add_32bit|add[29]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[27]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.057      ;
; 0.823 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.557      ;
; 0.825 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.560      ;
; 0.826 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.560      ;
; 0.830 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.568      ;
; 0.834 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.572      ;
; 0.837 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.571      ;
; 0.841 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.577      ;
; 0.845 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.579      ;
; 0.848 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.583      ;
; 0.854 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.592      ;
; 0.857 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.595      ;
; 0.867 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.613      ;
; 0.873 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.491      ; 1.618      ;
; 0.874 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.610      ;
; 0.875 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.621      ;
; 0.878 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.613      ;
; 0.879 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.491      ; 1.624      ;
; 0.881 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.627      ;
; 0.884 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.630      ;
; 0.884 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.491      ; 1.629      ;
; 0.890 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.491      ; 1.635      ;
; 0.903 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.484      ; 1.641      ;
; 0.907 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.653      ;
; 0.907 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.480      ; 1.641      ;
; 0.919 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.665      ;
; 0.925 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.671      ;
; 0.925 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.671      ;
; 0.926 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.491      ; 1.671      ;
; 0.933 ; add_32bit:u_add_32bit|add[18] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.226      ;
; 0.934 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.227      ;
; 0.937 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.230      ;
; 0.938 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.231      ;
; 0.940 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.233      ;
; 0.941 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.234      ;
; 0.954 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.700      ;
; 0.954 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.247      ;
; 0.964 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.491      ; 1.709      ;
; 0.970 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.263      ;
; 0.974 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.720      ;
; 0.996 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.289      ;
; 0.999 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.492      ; 1.745      ;
; 1.090 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.383      ;
; 1.091 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.383      ;
; 1.091 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.384      ;
; 1.091 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.384      ;
; 1.091 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.384      ;
; 1.091 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.384      ;
; 1.091 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.384      ;
; 1.099 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.392      ;
; 1.099 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.392      ;
; 1.099 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.392      ;
; 1.099 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.392      ;
; 1.100 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.393      ;
; 1.100 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.393      ;
; 1.108 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.401      ;
; 1.108 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.401      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'                                    ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'                                                                                               ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.954  ; 9.954        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.977  ; 9.977        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.022 ; 10.022       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.046 ; 10.046       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                 ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.716 ; 24.936       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.716 ; 24.936       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[12]                                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[13]                                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[14]                                                                                                      ;
; 24.718 ; 24.938       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[15]                                                                                                      ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[10]                                                                                                      ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[11]                                                                                                      ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[8]                                                                                                       ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[9]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d1                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d2                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[0]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[1]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[2]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[3]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[4]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[5]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[6]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[7]                                                                                                       ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_valid                                                                                                    ;
; 24.731 ; 24.966       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.732 ; 24.967       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.732 ; 24.967       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.732 ; 24.967       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.732 ; 24.967       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.732 ; 24.967       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.733 ; 24.968       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.735 ; 24.970       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.735 ; 24.970       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.735 ; 24.970       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.736 ; 24.971       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.736 ; 24.971       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.736 ; 24.971       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.736 ; 24.971       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.736 ; 24.971       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.736 ; 24.971       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.776 ; 25.011       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.776 ; 25.011       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.776 ; 25.011       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.776 ; 25.011       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.776 ; 25.011       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.776 ; 25.011       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.778 ; 25.013       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.778 ; 25.013       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.778 ; 25.013       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.779 ; 25.014       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.779 ; 25.014       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.779 ; 25.014       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.779 ; 25.014       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.779 ; 25.014       ; 0.235          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 2.574 ; 2.823 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 5.336 ; 5.595 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -1.868 ; -2.122 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -4.506 ; -4.763 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 3.199  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 13.155 ; 12.993 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 10.797 ; 10.632 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 11.180 ; 11.007 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 11.072 ; 10.881 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 11.039 ; 10.856 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 11.075 ; 10.870 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 12.013 ; 12.031 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 11.999 ; 11.797 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 13.144 ; 12.868 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 13.155 ; 12.993 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 12.926 ; 12.724 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 12.967 ; 12.849 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 12.637 ; 12.423 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 12.552 ; 12.402 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 12.441 ; 12.674 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 6.407  ; 6.203  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 6.407  ; 6.203  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.616  ; 5.443  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.250  ; 5.104  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.300  ; 5.161  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.278  ; 5.165  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.225  ; 5.059  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.694  ; 5.565  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 4.932  ; 4.824  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.348  ; 5.224  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.670  ; 5.518  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.305  ; 5.498  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.208  ; 5.352  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 5.835  ; 5.877  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.835  ; 5.877  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.028  ; 4.935  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.749  ; 5.707  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.997  ; 4.900  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 4.886  ; 4.771  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 3.157  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.703 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 6.421 ; 6.261 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 6.421 ; 6.261 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 6.787 ; 6.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 6.684 ; 6.499 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 6.652 ; 6.476 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 6.687 ; 6.489 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 7.646 ; 7.666 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 7.229 ; 6.999 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 7.822 ; 7.564 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 7.942 ; 7.726 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 7.582 ; 7.395 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 7.672 ; 7.499 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 7.155 ; 6.956 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 7.124 ; 6.920 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 6.674 ; 6.899 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 4.356 ; 4.250 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.773 ; 5.574 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.013 ; 4.845 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 4.662 ; 4.519 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 4.710 ; 4.574 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 4.689 ; 4.578 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 4.638 ; 4.476 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.088 ; 4.962 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 4.356 ; 4.250 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 4.756 ; 4.635 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.065 ; 4.917 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 4.712 ; 4.900 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 4.619 ; 4.760 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 4.419 ; 4.324 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.221 ; 5.263 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 4.448 ; 4.357 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.141 ; 5.098 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.419 ; 4.324 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 4.311 ; 4.199 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 2.662 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


----------------------------------------------
; Slow 1200mV 85C Model Metastability Report ;
----------------------------------------------
No synchronizer chains to report.


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                           ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 179.21 MHz ; 179.21 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 44.420 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.422 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.943  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.716 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                            ;
+--------+--------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node    ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+--------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 44.420 ; spi_data[12] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.510      ;
; 44.546 ; spi_data[12] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.384      ;
; 44.585 ; spi_data[12] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.345      ;
; 44.671 ; spi_data[13] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.259      ;
; 44.672 ; spi_data[12] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.258      ;
; 44.711 ; spi_data[12] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.219      ;
; 44.797 ; spi_data[13] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.133      ;
; 44.798 ; spi_data[12] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.132      ;
; 44.836 ; spi_data[13] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.094      ;
; 44.837 ; spi_data[12] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.093      ;
; 44.923 ; spi_data[13] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.007      ;
; 44.924 ; spi_data[12] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 5.006      ;
; 44.962 ; spi_data[13] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.968      ;
; 44.963 ; spi_data[12] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.967      ;
; 44.985 ; spi_data[15] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.945      ;
; 45.027 ; spi_data[14] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.903      ;
; 45.049 ; spi_data[13] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.881      ;
; 45.050 ; spi_data[12] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.880      ;
; 45.088 ; spi_data[13] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.842      ;
; 45.089 ; spi_data[12] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.841      ;
; 45.090 ; spi_data[15] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.840      ;
; 45.111 ; spi_data[15] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.819      ;
; 45.153 ; spi_data[14] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.777      ;
; 45.175 ; spi_data[13] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.755      ;
; 45.176 ; spi_data[12] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.754      ;
; 45.192 ; spi_data[14] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.738      ;
; 45.214 ; spi_data[13] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.716      ;
; 45.215 ; spi_data[12] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.715      ;
; 45.216 ; spi_data[15] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.714      ;
; 45.237 ; spi_data[15] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.693      ;
; 45.279 ; spi_data[14] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.651      ;
; 45.301 ; spi_data[13] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.629      ;
; 45.302 ; spi_data[12] ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.628      ;
; 45.318 ; spi_data[14] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.612      ;
; 45.340 ; spi_data[13] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.590      ;
; 45.341 ; spi_data[12] ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.589      ;
; 45.342 ; spi_data[15] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.588      ;
; 45.363 ; spi_data[15] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.567      ;
; 45.405 ; spi_data[14] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.525      ;
; 45.427 ; spi_data[13] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.503      ;
; 45.429 ; spi_data[12] ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.502      ;
; 45.444 ; spi_data[14] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.486      ;
; 45.466 ; spi_data[13] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.464      ;
; 45.467 ; spi_data[12] ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.463      ;
; 45.468 ; spi_data[15] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.462      ;
; 45.489 ; spi_data[15] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.441      ;
; 45.531 ; spi_data[14] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.399      ;
; 45.553 ; spi_data[13] ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.377      ;
; 45.555 ; spi_data[12] ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.376      ;
; 45.570 ; spi_data[14] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.360      ;
; 45.592 ; spi_data[13] ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.338      ;
; 45.594 ; spi_data[12] ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.337      ;
; 45.594 ; spi_data[15] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.336      ;
; 45.615 ; spi_data[15] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.315      ;
; 45.657 ; spi_data[14] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.273      ;
; 45.680 ; spi_data[13] ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.251      ;
; 45.681 ; spi_data[12] ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.250      ;
; 45.696 ; spi_data[14] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.234      ;
; 45.718 ; spi_data[13] ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.212      ;
; 45.720 ; spi_data[12] ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.211      ;
; 45.720 ; spi_data[15] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.210      ;
; 45.741 ; spi_data[15] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.189      ;
; 45.783 ; spi_data[14] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.147      ;
; 45.798 ; spi_cs_n_d2  ; spi_data[14]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.142      ;
; 45.798 ; spi_cs_n_d2  ; spi_data[15]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.142      ;
; 45.798 ; spi_cs_n_d2  ; spi_data[12]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.142      ;
; 45.798 ; spi_cs_n_d2  ; spi_data[13]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.142      ;
; 45.806 ; spi_data[13] ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.125      ;
; 45.807 ; spi_data[12] ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.124      ;
; 45.822 ; spi_data[14] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.108      ;
; 45.845 ; spi_data[13] ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.086      ;
; 45.846 ; spi_data[12] ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 4.085      ;
; 45.846 ; spi_data[15] ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.084      ;
; 45.867 ; spi_data[15] ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.063      ;
; 45.876 ; spi_cs_n_d1  ; spi_data[14]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.064      ;
; 45.876 ; spi_cs_n_d1  ; spi_data[15]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.064      ;
; 45.876 ; spi_cs_n_d1  ; spi_data[12]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.064      ;
; 45.876 ; spi_cs_n_d1  ; spi_data[13]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.062     ; 4.064      ;
; 45.909 ; spi_data[14] ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 4.021      ;
; 45.932 ; spi_data[13] ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.999      ;
; 45.933 ; spi_data[12] ; add_32bit:u_add_32bit|add[7]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.998      ;
; 45.948 ; spi_data[14] ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.982      ;
; 45.971 ; spi_data[13] ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.960      ;
; 45.972 ; spi_data[12] ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.959      ;
; 45.972 ; spi_data[15] ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.958      ;
; 45.994 ; spi_data[15] ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.937      ;
; 46.036 ; spi_data[14] ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.895      ;
; 46.058 ; spi_data[13] ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.873      ;
; 46.059 ; spi_data[12] ; add_32bit:u_add_32bit|add[5]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.872      ;
; 46.074 ; spi_data[14] ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.856      ;
; 46.097 ; spi_data[13] ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.834      ;
; 46.098 ; spi_data[12] ; add_32bit:u_add_32bit|add[6]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.833      ;
; 46.099 ; spi_data[15] ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.832      ;
; 46.120 ; spi_data[15] ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.811      ;
; 46.162 ; spi_data[14] ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.769      ;
; 46.184 ; spi_data[13] ; add_32bit:u_add_32bit|add[7]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.747      ;
; 46.185 ; spi_data[12] ; add_32bit:u_add_32bit|add[3]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.746      ;
; 46.201 ; spi_data[14] ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.730      ;
; 46.223 ; spi_data[13] ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.708      ;
; 46.224 ; spi_data[12] ; add_32bit:u_add_32bit|add[4]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.071     ; 3.707      ;
+--------+--------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                 ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.422 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.076      ;
; 0.426 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.080      ;
; 0.435 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.089      ;
; 0.436 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.090      ;
; 0.466 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.121      ;
; 0.475 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.130      ;
; 0.475 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.129      ;
; 0.478 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.134      ;
; 0.480 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.135      ;
; 0.487 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.143      ;
; 0.489 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.145      ;
; 0.489 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.145      ;
; 0.491 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.146      ;
; 0.494 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.150      ;
; 0.500 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.155      ;
; 0.512 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.168      ;
; 0.514 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.170      ;
; 0.528 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.184      ;
; 0.539 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.195      ;
; 0.555 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.211      ;
; 0.588 ; spi_cs_n_d1                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.854      ;
; 0.654 ; spi_cs_n_d1                   ; spi_cs_n_d2                                                                                                       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.920      ;
; 0.684 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.951      ;
; 0.685 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.952      ;
; 0.686 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.687 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.688 ; spi_cs_n_d2                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.954      ;
; 0.689 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.690 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.957      ;
; 0.706 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.973      ;
; 0.707 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.974      ;
; 0.708 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.975      ;
; 0.708 ; add_32bit:u_add_32bit|add[29] ; add_32bit:u_add_32bit|add[29]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.975      ;
; 0.709 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.976      ;
; 0.709 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[27]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.976      ;
; 0.709 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.976      ;
; 0.709 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.976      ;
; 0.710 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.977      ;
; 0.760 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.415      ;
; 0.767 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.421      ;
; 0.767 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.421      ;
; 0.770 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.426      ;
; 0.774 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.430      ;
; 0.775 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.431      ;
; 0.775 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.429      ;
; 0.784 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.439      ;
; 0.786 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.440      ;
; 0.792 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.448      ;
; 0.796 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.452      ;
; 0.805 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.460      ;
; 0.806 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.462      ;
; 0.806 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.468      ;
; 0.812 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.474      ;
; 0.812 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.474      ;
; 0.818 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.480      ;
; 0.819 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.481      ;
; 0.820 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.482      ;
; 0.820 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.482      ;
; 0.828 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.490      ;
; 0.837 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.493      ;
; 0.841 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.495      ;
; 0.843 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.505      ;
; 0.846 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.113      ;
; 0.846 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.113      ;
; 0.847 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.114      ;
; 0.851 ; add_32bit:u_add_32bit|add[18] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.118      ;
; 0.851 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.118      ;
; 0.852 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.514      ;
; 0.854 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.121      ;
; 0.858 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.125      ;
; 0.860 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.522      ;
; 0.861 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.523      ;
; 0.861 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.523      ;
; 0.874 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.141      ;
; 0.884 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.151      ;
; 0.887 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.549      ;
; 0.894 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.556      ;
; 0.907 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.569      ;
; 0.927 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.432      ; 1.589      ;
; 1.005 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.272      ;
; 1.005 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.272      ;
; 1.006 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.273      ;
; 1.006 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.273      ;
; 1.006 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.273      ;
; 1.006 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.273      ;
; 1.006 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 1.272      ;
; 1.006 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.273      ;
; 1.006 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.273      ;
; 1.007 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.274      ;
; 1.007 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.274      ;
; 1.010 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.277      ;
; 1.011 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.278      ;
; 1.020 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.287      ;
; 1.022 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.289      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.975  ; 9.975        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.991  ; 9.991        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.008 ; 10.008       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.025 ; 10.025       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.716 ; 24.932       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[0]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[10]                                                                                                      ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[11]                                                                                                      ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[1]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[2]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[3]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[4]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[5]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[6]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[7]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[8]                                                                                                       ;
; 24.717 ; 24.933       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[9]                                                                                                       ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[12]                                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[13]                                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[14]                                                                                                      ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[15]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d1                                                                                                       ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d2                                                                                                       ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_valid                                                                                                    ;
; 24.726 ; 24.956       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.726 ; 24.956       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.726 ; 24.956       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.726 ; 24.956       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.726 ; 24.956       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.726 ; 24.956       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.727 ; 24.957       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.733 ; 24.963       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.800 ; 25.030       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.801 ; 25.031       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.801 ; 25.031       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.801 ; 25.031       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.801 ; 25.031       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 2.273 ; 2.555 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 4.712 ; 4.797 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -1.647 ; -1.927 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -3.974 ; -4.062 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.980  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 12.254 ; 11.924 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 10.026 ; 9.730  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 10.387 ; 10.061 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 10.297 ; 9.957  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 10.255 ; 9.939  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 10.304 ; 9.947  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 11.059 ; 10.926 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 11.187 ; 10.863 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 12.231 ; 11.825 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 12.254 ; 11.924 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 12.023 ; 11.681 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 12.060 ; 11.787 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 11.717 ; 11.409 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 11.638 ; 11.384 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 11.433 ; 11.723 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 6.023  ; 5.673  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 6.023  ; 5.673  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.270  ; 4.994  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 4.913  ; 4.688  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 4.961  ; 4.737  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 4.948  ; 4.738  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 4.880  ; 4.649  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.332  ; 5.097  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 4.598  ; 4.439  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.001  ; 4.802  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.314  ; 5.060  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 4.860  ; 5.151  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 4.783  ; 5.006  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 5.376  ; 5.455  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.376  ; 5.455  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 4.686  ; 4.538  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.334  ; 5.260  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.657  ; 4.507  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 4.554  ; 4.386  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 2.903  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.523 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 6.005 ; 5.720 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 6.005 ; 5.720 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 6.351 ; 6.038 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 6.265 ; 5.938 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 6.225 ; 5.921 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 6.272 ; 5.928 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 7.047 ; 6.921 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 6.716 ; 6.371 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 7.273 ; 6.891 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 7.390 ; 7.026 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 7.044 ; 6.724 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 7.128 ; 6.817 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 6.622 ; 6.334 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 6.595 ; 6.303 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 6.103 ; 6.383 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 4.066 ; 3.912 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.434 ; 5.096 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 4.710 ; 4.444 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 4.368 ; 4.150 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 4.413 ; 4.197 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 4.402 ; 4.198 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 4.336 ; 4.113 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 4.770 ; 4.543 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 4.066 ; 3.912 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 4.452 ; 4.259 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 4.752 ; 4.508 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 4.315 ; 4.596 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 4.241 ; 4.457 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 4.122 ; 3.977 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 4.811 ; 4.888 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 4.150 ; 4.006 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 4.772 ; 4.700 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.122 ; 3.977 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 4.024 ; 3.861 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 2.447 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Slow 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 47.164 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.148 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 2.060  ; 0.000         ;
; CLK_50M                                               ; 9.594  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.735 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                            ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                    ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 47.164 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.787      ;
; 47.228 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.723      ;
; 47.232 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.719      ;
; 47.296 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.655      ;
; 47.300 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.651      ;
; 47.364 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.587      ;
; 47.368 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.583      ;
; 47.392 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.559      ;
; 47.397 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.554      ;
; 47.418 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.533      ;
; 47.432 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.519      ;
; 47.436 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.515      ;
; 47.460 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.491      ;
; 47.461 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.490      ;
; 47.465 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.486      ;
; 47.486 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.465      ;
; 47.500 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.451      ;
; 47.504 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.447      ;
; 47.528 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.423      ;
; 47.529 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.422      ;
; 47.533 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.418      ;
; 47.554 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.397      ;
; 47.568 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.383      ;
; 47.572 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.379      ;
; 47.577 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.374      ;
; 47.587 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.364      ;
; 47.596 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.355      ;
; 47.597 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.354      ;
; 47.601 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.350      ;
; 47.622 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.329      ;
; 47.636 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.315      ;
; 47.640 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.311      ;
; 47.645 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.306      ;
; 47.655 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.296      ;
; 47.664 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.287      ;
; 47.665 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.286      ;
; 47.669 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.282      ;
; 47.690 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.261      ;
; 47.704 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.247      ;
; 47.709 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.243      ;
; 47.713 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.238      ;
; 47.723 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.228      ;
; 47.731 ; spi_cs_n_d2                  ; spi_data[14]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.224      ;
; 47.731 ; spi_cs_n_d2                  ; spi_data[15]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.224      ;
; 47.731 ; spi_cs_n_d2                  ; spi_data[12]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.224      ;
; 47.731 ; spi_cs_n_d2                  ; spi_data[13]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.224      ;
; 47.732 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.219      ;
; 47.733 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.218      ;
; 47.737 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.214      ;
; 47.758 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.193      ;
; 47.773 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.179      ;
; 47.777 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.175      ;
; 47.781 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.170      ;
; 47.791 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.160      ;
; 47.800 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.151      ;
; 47.801 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.150      ;
; 47.805 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.146      ;
; 47.826 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.125      ;
; 47.837 ; spi_cs_n_d1                  ; spi_data[14]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.118      ;
; 47.837 ; spi_cs_n_d1                  ; spi_data[15]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.118      ;
; 47.837 ; spi_cs_n_d1                  ; spi_data[12]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.118      ;
; 47.837 ; spi_cs_n_d1                  ; spi_data[13]                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.032     ; 2.118      ;
; 47.841 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.111      ;
; 47.845 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.107      ;
; 47.849 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.102      ;
; 47.859 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.092      ;
; 47.868 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.083      ;
; 47.869 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.082      ;
; 47.873 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.078      ;
; 47.894 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.057      ;
; 47.909 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.043      ;
; 47.913 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[9]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.039      ;
; 47.917 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.034      ;
; 47.927 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.024      ;
; 47.937 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 2.014      ;
; 47.937 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.015      ;
; 47.942 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 2.010      ;
; 47.963 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.989      ;
; 47.977 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[8]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.975      ;
; 47.981 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[7]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.971      ;
; 47.985 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.966      ;
; 47.995 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.956      ;
; 48.005 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.947      ;
; 48.006 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.946      ;
; 48.010 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[13] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.942      ;
; 48.031 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.921      ;
; 48.045 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[6]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.907      ;
; 48.049 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[5]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.903      ;
; 48.053 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.898      ;
; 48.063 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.888      ;
; 48.073 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.879      ;
; 48.074 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[12] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.878      ;
; 48.078 ; spi_data[15]                 ; add_32bit:u_add_32bit|add[11] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.874      ;
; 48.080 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.870      ;
; 48.084 ; add_32bit:u_add_32bit|add[1] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.866      ;
; 48.099 ; spi_data[13]                 ; add_32bit:u_add_32bit|add[10] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.853      ;
; 48.113 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[4]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.839      ;
; 48.117 ; spi_data[12]                 ; add_32bit:u_add_32bit|add[3]  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.835      ;
; 48.122 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[15] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.830      ;
; 48.132 ; spi_data[14]                 ; add_32bit:u_add_32bit|add[14] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.035     ; 1.820      ;
+--------+------------------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                 ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.148 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.478      ;
; 0.149 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.479      ;
; 0.154 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.484      ;
; 0.163 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.493      ;
; 0.164 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.495      ;
; 0.166 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.497      ;
; 0.171 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.501      ;
; 0.172 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.503      ;
; 0.175 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.506      ;
; 0.175 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.506      ;
; 0.177 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.508      ;
; 0.178 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.509      ;
; 0.181 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.513      ;
; 0.183 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.515      ;
; 0.184 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.515      ;
; 0.188 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.519      ;
; 0.189 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.520      ;
; 0.201 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.533      ;
; 0.203 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.535      ;
; 0.206 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.537      ;
; 0.258 ; spi_cs_n_d1                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.378      ;
; 0.272 ; spi_cs_n_d1                   ; spi_cs_n_d2                                                                                                       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.392      ;
; 0.293 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.294 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.295 ; spi_cs_n_d2                   ; spi_data_valid                                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.299 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.419      ;
; 0.304 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.305 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.425      ;
; 0.306 ; add_32bit:u_add_32bit|add[29] ; add_32bit:u_add_32bit|add[29]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[27]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.426      ;
; 0.307 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.427      ;
; 0.307 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.427      ;
; 0.313 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.644      ;
; 0.322 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.653      ;
; 0.324 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.654      ;
; 0.327 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.657      ;
; 0.327 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.659      ;
; 0.329 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.659      ;
; 0.330 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.661      ;
; 0.330 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.662      ;
; 0.334 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.665      ;
; 0.334 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.664      ;
; 0.334 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.666      ;
; 0.340 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.671      ;
; 0.342 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.674      ;
; 0.348 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.684      ;
; 0.348 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.684      ;
; 0.350 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.228      ; 0.682      ;
; 0.351 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.687      ;
; 0.352 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.688      ;
; 0.354 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.231      ; 0.689      ;
; 0.356 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.686      ;
; 0.357 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.231      ; 0.692      ;
; 0.359 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.231      ; 0.694      ;
; 0.359 ; add_32bit:u_add_32bit|add[18] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.479      ;
; 0.360 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.480      ;
; 0.361 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.231      ; 0.696      ;
; 0.361 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.481      ;
; 0.361 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.481      ;
; 0.362 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.698      ;
; 0.362 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.482      ;
; 0.363 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.483      ;
; 0.369 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.489      ;
; 0.370 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.706      ;
; 0.370 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.706      ;
; 0.371 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.707      ;
; 0.373 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.231      ; 0.708      ;
; 0.375 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.495      ;
; 0.378 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.714      ;
; 0.379 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.499      ;
; 0.384 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.720      ;
; 0.389 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.231      ; 0.724      ;
; 0.399 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.735      ;
; 0.442 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.035      ; 0.561      ;
; 0.442 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.562      ;
; 0.442 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.562      ;
; 0.442 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.562      ;
; 0.442 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.562      ;
; 0.443 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.563      ;
; 0.443 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.563      ;
; 0.452 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.572      ;
; 0.452 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.572      ;
; 0.452 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.572      ;
; 0.453 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.573      ;
; 0.453 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.573      ;
; 0.453 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.573      ;
; 0.454 ; add_32bit:u_add_32bit|add[27] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.574      ;
; 0.454 ; add_32bit:u_add_32bit|add[29] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.574      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.620  ; 9.620        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.622  ; 9.622        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.378 ; 10.378       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.380 ; 10.380       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d1                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cs_n_d2                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[0]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[10]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[11]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[12]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[13]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[14]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[15]                                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[1]                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[2]                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[3]                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[4]                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[5]                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[6]                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[7]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[8]                                                                                                       ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data[9]                                                                                                       ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_valid                                                                                                    ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.799 ; 25.029       ; 0.230          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.799 ; 25.015       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 1.287 ; 1.651 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 2.609 ; 3.239 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -0.966 ; -1.332 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -2.227 ; -2.854 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                  ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.519 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 5.841 ; 5.979 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 4.813 ; 4.911 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 4.955 ; 5.087 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 4.908 ; 5.021 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 4.905 ; 5.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 4.905 ; 5.023 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 5.665 ; 5.828 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 5.292 ; 5.404 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 5.726 ; 5.838 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 5.841 ; 5.979 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 5.644 ; 5.758 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 5.784 ; 5.899 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 5.514 ; 5.590 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 5.586 ; 5.651 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 5.575 ; 5.521 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 2.847 ; 3.004 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.847 ; 3.004 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.510 ; 2.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.353 ; 2.440 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.373 ; 2.465 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.377 ; 2.470 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.330 ; 2.409 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 2.551 ; 2.673 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 2.223 ; 2.290 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.405 ; 2.509 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.545 ; 2.662 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.530 ; 2.436 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.497 ; 2.394 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 2.920 ; 2.841 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.920 ; 2.747 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.274 ; 2.351 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.679 ; 2.841 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 2.259 ; 2.334 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 2.198 ; 2.265 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.584 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 2.840 ; 2.935 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 2.840 ; 2.935 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 2.976 ; 3.104 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 2.931 ; 3.041 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 2.928 ; 3.040 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 2.928 ; 3.043 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 3.695 ; 3.856 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 3.124 ; 3.226 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 3.417 ; 3.539 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 3.474 ; 3.622 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 3.319 ; 3.432 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 3.374 ; 3.500 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 3.127 ; 3.206 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 3.119 ; 3.198 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 3.070 ; 3.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 1.955 ; 2.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.554 ; 2.706 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.231 ; 2.337 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.080 ; 2.164 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.100 ; 2.188 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.103 ; 2.193 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.058 ; 2.134 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 2.270 ; 2.387 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 1.955 ; 2.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.130 ; 2.230 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.264 ; 2.377 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.250 ; 2.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.218 ; 2.119 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 1.990 ; 2.063 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.625 ; 2.458 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.004 ; 2.078 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.394 ; 2.549 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 1.990 ; 2.063 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 1.931 ; 1.995 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Fast 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                                ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Clock                                                  ; Setup  ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Worst-case Slack                                       ; 43.798 ; 0.148 ; N/A      ; N/A     ; 1.616               ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 1.616               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 9.594               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 43.798 ; 0.148 ; N/A      ; N/A     ; 24.716              ;
; Design-wide TNS                                        ; 0.0    ; 0.0   ; 0.0      ; 0.0     ; 0.0                 ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000  ; 0.000 ; N/A      ; N/A     ; 0.000               ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 2.574 ; 2.823 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 5.336 ; 5.595 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -0.966 ; -1.332 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -2.227 ; -2.854 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 3.199  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 13.155 ; 12.993 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 10.797 ; 10.632 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 11.180 ; 11.007 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 11.072 ; 10.881 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 11.039 ; 10.856 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 11.075 ; 10.870 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 12.013 ; 12.031 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 11.999 ; 11.797 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 13.144 ; 12.868 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 13.155 ; 12.993 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 12.926 ; 12.724 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 12.967 ; 12.849 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 12.637 ; 12.423 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 12.552 ; 12.402 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 12.441 ; 12.674 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 6.407  ; 6.203  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 6.407  ; 6.203  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.616  ; 5.443  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.250  ; 5.104  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.300  ; 5.161  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.278  ; 5.165  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.225  ; 5.059  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.694  ; 5.565  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 4.932  ; 4.824  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.348  ; 5.224  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.670  ; 5.518  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.305  ; 5.498  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.208  ; 5.352  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 5.835  ; 5.877  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.835  ; 5.877  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.028  ; 4.935  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.749  ; 5.707  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.997  ; 4.900  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 4.886  ; 4.771  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 3.157  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 2.840 ; 2.935 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 2.840 ; 2.935 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 2.976 ; 3.104 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 2.931 ; 3.041 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 2.928 ; 3.040 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 2.928 ; 3.043 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 3.695 ; 3.856 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 3.124 ; 3.226 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 3.417 ; 3.539 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 3.474 ; 3.622 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 3.319 ; 3.432 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 3.374 ; 3.500 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 3.127 ; 3.206 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 3.119 ; 3.198 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 3.070 ; 3.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 1.955 ; 2.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.554 ; 2.706 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.231 ; 2.337 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.080 ; 2.164 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.100 ; 2.188 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.103 ; 2.193 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.058 ; 2.134 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 2.270 ; 2.387 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 1.955 ; 2.020 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.130 ; 2.230 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.264 ; 2.377 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.250 ; 2.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.218 ; 2.119 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 1.990 ; 2.063 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.625 ; 2.458 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.004 ; 2.078 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.394 ; 2.549 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 1.990 ; 2.063 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 1.931 ; 1.995 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                          ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin                 ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; PD                  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_CLK             ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[0]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[1]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[2]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[3]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[4]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[5]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[6]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[7]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[8]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[9]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[10]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[11]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[12]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[13]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_spi_valid     ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[4]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[5]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[6]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[7]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[8]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[9]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[10] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[11] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; KEY_IN[0]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[1]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[2]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_RST                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_CLK                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_sclk                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_cs_n                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_mosi                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                                                           ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 2783     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                                            ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 2783     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths                            ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 1     ; 1    ;
; Unconstrained Input Ports       ; 3     ; 3    ;
; Unconstrained Input Port Paths  ; 87    ; 87   ;
; Unconstrained Output Ports      ; 32    ; 32   ;
; Unconstrained Output Port Paths ; 382   ; 382  ;
+---------------------------------+-------+------+


+------------------------------------+
; TimeQuest Timing Analyzer Messages ;
+------------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Fri Aug 01 03:12:53 2025
Info: Command: quartus_sta DAC904 -c DAC904
Info: qsta_default_script.tcl version: #1
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Info (332104): Reading SDC File: '../doc/SDC1.sdc'
Info (332110): Deriving PLL clocks
    Info (332110): create_generated_clock -source {u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]} {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]}
Info (332151): Clock uncertainty is not calculated until you update the timing netlist.
Warning (332060): Node: spi_sclk was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info: Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Info (332146): Worst-case setup slack is 43.798
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    43.798               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.439
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.439               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.934               0.000 CLK_50M 
    Info (332119):    24.716               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Warning (332060): Node: spi_sclk was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 44.420
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    44.420               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.422
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.422               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.943               0.000 CLK_50M 
    Info (332119):    24.716               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Fast 1200mV 0C Model
Warning (332060): Node: spi_sclk was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 47.164
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    47.164               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.148
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.148               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 2.060
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     2.060               0.000 CLK_165M 
    Info (332119):     9.594               0.000 CLK_50M 
    Info (332119):    24.735               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 4 warnings
    Info: Peak virtual memory: 4648 megabytes
    Info: Processing ended: Fri Aug 01 03:12:54 2025
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


