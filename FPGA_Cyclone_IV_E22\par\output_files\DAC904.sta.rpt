TimeQuest Timing Analyzer report for DAC904
Fri Aug 01 10:31:22 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. TimeQuest Timing Analyzer Summary
  3. Parallel Compilation
  4. SDC File List
  5. Clocks
  6. Slow 1200mV 85C Model Fmax Summary
  7. Timing Closure Recommendations
  8. Slow 1200mV 85C Model Setup Summary
  9. Slow 1200mV 85C Model Hold Summary
 10. Slow 1200mV 85C Model Recovery Summary
 11. Slow 1200mV 85C Model Removal Summary
 12. Slow 1200mV 85C Model Minimum Pulse Width Summary
 13. Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 14. Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 15. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'
 16. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'
 17. Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 18. Setup Times
 19. Hold Times
 20. Clock to Output Times
 21. Minimum Clock to Output Times
 22. Slow 1200mV 85C Model Metastability Report
 23. Slow 1200mV 0C Model Fmax Summary
 24. Slow 1200mV 0C Model Setup Summary
 25. Slow 1200mV 0C Model Hold Summary
 26. Slow 1200mV 0C Model Recovery Summary
 27. Slow 1200mV 0C Model Removal Summary
 28. Slow 1200mV 0C Model Minimum Pulse Width Summary
 29. Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 30. Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 31. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 32. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 33. Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 34. Setup Times
 35. Hold Times
 36. Clock to Output Times
 37. Minimum Clock to Output Times
 38. Slow 1200mV 0C Model Metastability Report
 39. Fast 1200mV 0C Model Setup Summary
 40. Fast 1200mV 0C Model Hold Summary
 41. Fast 1200mV 0C Model Recovery Summary
 42. Fast 1200mV 0C Model Removal Summary
 43. Fast 1200mV 0C Model Minimum Pulse Width Summary
 44. Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 45. Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 46. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 47. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 48. Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 49. Setup Times
 50. Hold Times
 51. Clock to Output Times
 52. Minimum Clock to Output Times
 53. Fast 1200mV 0C Model Metastability Report
 54. Multicorner Timing Analysis Summary
 55. Setup Times
 56. Hold Times
 57. Clock to Output Times
 58. Minimum Clock to Output Times
 59. Board Trace Model Assignments
 60. Input Transition Times
 61. Signal Integrity Metrics (Slow 1200mv 0c Model)
 62. Signal Integrity Metrics (Slow 1200mv 85c Model)
 63. Signal Integrity Metrics (Fast 1200mv 0c Model)
 64. Setup Transfers
 65. Hold Transfers
 66. Report TCCS
 67. Report RSKM
 68. Unconstrained Paths
 69. TimeQuest Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+-------------------------------------------------------------------------+
; TimeQuest Timing Analyzer Summary                                       ;
+--------------------+----------------------------------------------------+
; Quartus II Version ; Version 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name      ; DAC904                                             ;
; Device Family      ; Cyclone IV E                                       ;
; Device Name        ; EP4CE6E22C8                                        ;
; Timing Models      ; Final                                              ;
; Delay Model        ; Combined                                           ;
; Rise/Fall Delays   ; Enabled                                            ;
+--------------------+----------------------------------------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-----------------------------------------------------+
; SDC File List                                       ;
+-----------------+--------+--------------------------+
; SDC File Path   ; Status ; Read at                  ;
+-----------------+--------+--------------------------+
; ../doc/SDC1.sdc ; OK     ; Fri Aug 01 10:31:21 2025 ;
+-----------------+--------+--------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clock Name                                            ; Type      ; Period ; Frequency  ; Rise  ; Fall   ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master  ; Source                                                  ; Targets                                                                                                                                                                                 ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; CLK_50M                                               ; Base      ; 20.000 ; 50.0 MHz   ; 0.000 ; 10.000 ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { SYS_CLK }                                                                                                                                                                             ;
; CLK_165M                                              ; Base      ; 6.060  ; 165.02 MHz ; 0.000 ; 3.030  ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { DAC_CLK DAC_DATA[0] DAC_DATA[1] DAC_DATA[2] DAC_DATA[3] DAC_DATA[4] DAC_DATA[5] DAC_DATA[6] DAC_DATA[7] DAC_DATA[8] DAC_DATA[9] DAC_DATA[10] DAC_DATA[11] DAC_DATA[12] DAC_DATA[13] } ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Generated ; 50.000 ; 20.0 MHz   ; 0.000 ; 25.000 ; 50.00      ; 5         ; 2           ;       ;        ;           ;            ; false    ; CLK_50M ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0] ; { u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] }                                                                                                                               ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                                         ;
+-----------+-----------------+-------------------------------------------------------+------+
; Fmax      ; Restricted Fmax ; Clock Name                                            ; Note ;
+-----------+-----------------+-------------------------------------------------------+------+
; 179.4 MHz ; 179.4 MHz       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+-----------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                                            ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 44.426 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                                            ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.387 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                              ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.934  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.720 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                               ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node        ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 44.426 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.505      ;
; 44.456 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.475      ;
; 44.540 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.391      ;
; 44.572 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.359      ;
; 44.602 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.329      ;
; 44.648 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.283      ;
; 44.686 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.245      ;
; 44.718 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.213      ;
; 44.740 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.191      ;
; 44.748 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.183      ;
; 44.794 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.137      ;
; 44.825 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.106      ;
; 44.832 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.099      ;
; 44.864 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.067      ;
; 44.886 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.045      ;
; 44.894 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.037      ;
; 44.924 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 5.007      ;
; 44.937 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.994      ;
; 44.940 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.991      ;
; 44.971 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.960      ;
; 44.978 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.953      ;
; 45.010 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.921      ;
; 45.032 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.899      ;
; 45.040 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.891      ;
; 45.070 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.861      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.081 ; spi_cnt[0]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.819      ;
; 45.083 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.848      ;
; 45.086 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.845      ;
; 45.117 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.814      ;
; 45.124 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.807      ;
; 45.156 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.775      ;
; 45.178 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.753      ;
; 45.186 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.745      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.194 ; spi_cnt[2]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.706      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.205 ; spi_cnt[3]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.695      ;
; 45.216 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.715      ;
; 45.229 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.702      ;
; 45.232 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.699      ;
; 45.263 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.668      ;
; 45.270 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.661      ;
; 45.302 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.629      ;
; 45.324 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.607      ;
; 45.332 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.599      ;
; 45.362 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.569      ;
; 45.375 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.556      ;
; 45.378 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.553      ;
; 45.409 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.522      ;
; 45.416 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.515      ;
; 45.448 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.483      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.448 ; spi_cnt[1]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.452      ;
; 45.470 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.461      ;
; 45.478 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.453      ;
; 45.508 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.070     ; 4.423      ;
; 45.517 ; spi_cnt[0]       ; spi_data_reg[15]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.112     ; 4.372      ;
; 45.517 ; spi_cnt[0]       ; spi_data_reg[14]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.112     ; 4.372      ;
; 45.517 ; spi_cnt[0]       ; spi_data_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.112     ; 4.372      ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.387 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.123      ;
; 0.437 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.173      ;
; 0.452 ; spi_cnt[3]                    ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; spi_cnt[1]                    ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; spi_cnt[2]                    ; spi_cnt[2]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 0.746      ;
; 0.453 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.189      ;
; 0.462 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.198      ;
; 0.464 ; spi_cnt[0]                    ; spi_cnt[0]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 0.758      ;
; 0.466 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.202      ;
; 0.502 ; mosi_sync[0]                  ; mosi_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.794      ;
; 0.507 ; spi_shift[5]                  ; spi_shift[6]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.800      ;
; 0.511 ; spi_shift[3]                  ; spi_shift[4]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.804      ;
; 0.512 ; spi_shift[5]                  ; spi_data_reg[6]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.805      ;
; 0.512 ; spi_shift[4]                  ; spi_data_reg[5]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.805      ;
; 0.512 ; spi_shift[3]                  ; spi_data_reg[4]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.805      ;
; 0.513 ; spi_shift[4]                  ; spi_shift[5]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.806      ;
; 0.527 ; spi_shift[12]                 ; spi_shift[13]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.819      ;
; 0.527 ; spi_shift[1]                  ; spi_shift[2]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.819      ;
; 0.528 ; spi_shift[9]                  ; spi_shift[10]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.820      ;
; 0.528 ; spi_shift[8]                  ; spi_shift[9]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.820      ;
; 0.528 ; spi_shift[7]                  ; spi_shift[8]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.820      ;
; 0.528 ; mosi_sync[1]                  ; spi_shift[0]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 0.820      ;
; 0.542 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.278      ;
; 0.697 ; cs_n_sync[0]                  ; cs_n_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 0.991      ;
; 0.698 ; sclk_sync[0]                  ; sclk_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 0.992      ;
; 0.704 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.439      ;
; 0.705 ; spi_shift[11]                 ; spi_data_reg[12]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 0.998      ;
; 0.709 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.463      ;
; 0.724 ; spi_shift[13]                 ; spi_shift[14]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.016      ;
; 0.725 ; spi_shift[11]                 ; spi_shift[12]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.017      ;
; 0.725 ; spi_shift[10]                 ; spi_shift[11]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.017      ;
; 0.725 ; spi_shift[6]                  ; spi_data_reg[7]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.018      ;
; 0.725 ; spi_shift[0]                  ; spi_shift[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.017      ;
; 0.736 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.490      ;
; 0.737 ; add_32bit:u_add_32bit|add[18] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.738 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.030      ;
; 0.738 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.030      ;
; 0.738 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.030      ;
; 0.739 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.740 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.032      ;
; 0.740 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.032      ;
; 0.740 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.032      ;
; 0.743 ; cs_n_sync[1]                  ; spi_cnt[0]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.037      ;
; 0.750 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.504      ;
; 0.750 ; cs_n_sync[1]                  ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.044      ;
; 0.750 ; cs_n_sync[1]                  ; spi_cnt[2]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.044      ;
; 0.751 ; cs_n_sync[1]                  ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.045      ;
; 0.755 ; spi_cnt[2]                    ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.049      ;
; 0.757 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.049      ;
; 0.763 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.056      ;
; 0.763 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.056      ;
; 0.764 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.057      ;
; 0.766 ; add_32bit:u_add_32bit|add[25] ; add_32bit:u_add_32bit|add[25]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.059      ;
; 0.766 ; add_32bit:u_add_32bit|add[23] ; add_32bit:u_add_32bit|add[23]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.059      ;
; 0.769 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.523      ;
; 0.771 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.506      ;
; 0.777 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.531      ;
; 0.781 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.535      ;
; 0.782 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.517      ;
; 0.786 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.521      ;
; 0.786 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.079      ;
; 0.788 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.523      ;
; 0.795 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.530      ;
; 0.799 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.534      ;
; 0.800 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.535      ;
; 0.818 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.572      ;
; 0.821 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.556      ;
; 0.825 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.560      ;
; 0.827 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.581      ;
; 0.848 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.583      ;
; 0.875 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.500      ; 1.629      ;
; 0.899 ; spi_shift[14]                 ; spi_data_reg[15]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.192      ;
; 0.917 ; spi_shift[13]                 ; spi_data_reg[14]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.210      ;
; 0.918 ; sclk_sync[1]                  ; sclk_d1                                                                                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.212      ;
; 0.926 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.662      ;
; 0.943 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.235      ;
; 0.946 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.239      ;
; 0.955 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.248      ;
; 0.963 ; add_32bit:u_add_32bit|add[29] ; add_32bit:u_add_32bit|add[29]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.256      ;
; 0.968 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.261      ;
; 0.974 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.267      ;
; 0.980 ; spi_cnt[0]                    ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.274      ;
; 0.999 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.734      ;
; 1.011 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.746      ;
; 1.016 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.485      ; 1.755      ;
; 1.017 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.752      ;
; 1.019 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.481      ; 1.754      ;
; 1.027 ; spi_shift[2]                  ; spi_data_reg[3]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.093      ; 1.332      ;
; 1.028 ; spi_shift[2]                  ; spi_shift[3]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.093      ; 1.333      ;
; 1.045 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.485      ; 1.784      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'                                    ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'                                                                                               ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.954  ; 9.954        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.977  ; 9.977        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.022 ; 10.022       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.046 ; 10.046       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                 ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; mosi_sync[0]                                                                                                      ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; mosi_sync[1]                                                                                                      ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[12]                                                                                                  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[13]                                                                                                  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[14]                                                                                                  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[15]                                                                                                  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[0]                                                                                                      ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[10]                                                                                                     ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[11]                                                                                                     ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[12]                                                                                                     ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[13]                                                                                                     ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[14]                                                                                                     ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[1]                                                                                                      ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[2]                                                                                                      ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[7]                                                                                                      ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[8]                                                                                                      ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[9]                                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.721 ; 24.941       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.722 ; 24.957       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.723 ; 24.958       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.723 ; 24.958       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.724 ; 24.959       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.724 ; 24.959       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.725 ; 24.960       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.725 ; 24.960       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.725 ; 24.960       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.725 ; 24.960       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.725 ; 24.945       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.726 ; 24.961       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; cs_n_sync[0]                                                                                                      ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; cs_n_sync[1]                                                                                                      ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_d1                                                                                                           ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_sync[0]                                                                                                      ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_sync[1]                                                                                                      ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[0]                                                                                                        ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[1]                                                                                                        ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[2]                                                                                                        ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[3]                                                                                                        ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[0]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[10]                                                                                                  ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[11]                                                                                                  ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[1]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[2]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[3]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[4]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[5]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[6]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[7]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[8]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[9]                                                                                                   ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[3]                                                                                                      ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[4]                                                                                                      ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[5]                                                                                                      ;
; 24.726 ; 24.946       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[6]                                                                                                      ;
; 24.727 ; 24.962       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.727 ; 24.962       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.727 ; 24.962       ; 0.235          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.727 ; 24.947       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 3.033 ; 3.322 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 5.080 ; 5.296 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; 2.662 ; 2.821 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; 2.382 ; 2.520 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -2.308 ; -2.581 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -4.258 ; -4.474 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; -1.957 ; -2.099 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; -1.700 ; -1.835 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 3.199  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 13.428 ; 13.466 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 12.202 ; 12.075 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 12.176 ; 12.036 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 12.278 ; 12.094 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 12.396 ; 12.247 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 12.457 ; 12.307 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 13.428 ; 13.466 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 12.121 ; 11.805 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 11.936 ; 11.800 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 12.132 ; 12.027 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 12.108 ; 11.831 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 11.962 ; 11.848 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 11.736 ; 11.660 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 11.729 ; 11.710 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 11.236 ; 11.517 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 7.011  ; 6.938  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 6.073  ; 5.886  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.703  ; 5.525  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.654  ; 5.474  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.603  ; 5.424  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.869  ; 5.652  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.770  ; 5.683  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 7.011  ; 6.938  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.875  ; 5.646  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.358  ; 5.258  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.645  ; 5.469  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.334  ; 5.529  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.767  ; 5.979  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 5.802  ; 5.561  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.004  ; 5.178  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.802  ; 5.561  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.509  ; 5.323  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.880  ; 4.760  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 6.819  ; 6.653  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 3.157  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.703 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 6.983 ; 7.085 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 7.514 ; 7.198 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 7.505 ; 7.174 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 7.862 ; 7.558 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 7.754 ; 7.402 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 7.758 ; 7.420 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 8.899 ; 8.835 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 7.531 ; 7.374 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 7.533 ; 7.386 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 7.467 ; 7.201 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 7.569 ; 7.430 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 7.526 ; 7.189 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 7.374 ; 7.085 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 7.211 ; 7.090 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 6.983 ; 7.305 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 4.735 ; 4.667 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.451 ; 5.270 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.096 ; 4.924 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.049 ; 4.874 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.000 ; 4.827 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.256 ; 5.045 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.161 ; 5.075 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 6.410 ; 6.342 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.261 ; 5.039 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 4.765 ; 4.667 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.041 ; 4.869 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 4.735 ; 4.923 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.156 ; 5.361 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 4.307 ; 4.190 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 4.424 ; 4.593 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.191 ; 4.959 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 4.910 ; 4.730 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.307 ; 4.190 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 5.288 ; 5.024 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 2.662 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


----------------------------------------------
; Slow 1200mV 85C Model Metastability Report ;
----------------------------------------------
No synchronizer chains to report.


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                           ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 193.99 MHz ; 193.99 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 44.845 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.369 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.943  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.719 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node        ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 44.845 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 5.094      ;
; 44.895 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 5.044      ;
; 44.971 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.968      ;
; 45.021 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.918      ;
; 45.097 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.842      ;
; 45.147 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.792      ;
; 45.175 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.764      ;
; 45.176 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.763      ;
; 45.223 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.716      ;
; 45.273 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.666      ;
; 45.301 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.638      ;
; 45.301 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.638      ;
; 45.302 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.637      ;
; 45.318 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.621      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.322 ; spi_cnt[0]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.589      ;
; 45.349 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.590      ;
; 45.359 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.580      ;
; 45.399 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.540      ;
; 45.404 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.535      ;
; 45.427 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.512      ;
; 45.427 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.512      ;
; 45.428 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.511      ;
; 45.444 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.495      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.446 ; spi_cnt[2]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.465      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.457 ; spi_cnt[3]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.454      ;
; 45.475 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.464      ;
; 45.485 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.454      ;
; 45.525 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.414      ;
; 45.530 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.409      ;
; 45.553 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.386      ;
; 45.553 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.386      ;
; 45.554 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.385      ;
; 45.570 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.369      ;
; 45.601 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.338      ;
; 45.611 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.328      ;
; 45.651 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.288      ;
; 45.656 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.283      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.678 ; spi_cnt[1]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.091     ; 4.233      ;
; 45.679 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.260      ;
; 45.679 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.260      ;
; 45.680 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.259      ;
; 45.696 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.243      ;
; 45.727 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[17] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.212      ;
; 45.737 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.202      ;
; 45.748 ; spi_cnt[0]       ; spi_data_reg[15]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.153      ;
; 45.748 ; spi_cnt[0]       ; spi_data_reg[14]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.153      ;
; 45.748 ; spi_cnt[0]       ; spi_data_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.153      ;
; 45.748 ; spi_cnt[0]       ; spi_data_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.153      ;
; 45.777 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[16] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.162      ;
; 45.782 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.157      ;
; 45.805 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.134      ;
; 45.805 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.134      ;
; 45.806 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.133      ;
; 45.822 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.117      ;
; 45.863 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.063     ; 4.076      ;
; 45.872 ; spi_cnt[2]       ; spi_data_reg[15]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.101     ; 4.029      ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                 ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.369 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.023      ;
; 0.400 ; spi_cnt[3]                    ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; spi_cnt[1]                    ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; spi_cnt[2]                    ; spi_cnt[2]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.669      ;
; 0.415 ; spi_cnt[0]                    ; spi_cnt[0]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.684      ;
; 0.417 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.071      ;
; 0.429 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.083      ;
; 0.441 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.095      ;
; 0.442 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.096      ;
; 0.471 ; mosi_sync[0]                  ; mosi_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.738      ;
; 0.476 ; spi_shift[5]                  ; spi_shift[6]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.743      ;
; 0.480 ; spi_shift[5]                  ; spi_data_reg[6]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.747      ;
; 0.480 ; spi_shift[4]                  ; spi_data_reg[5]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.747      ;
; 0.480 ; spi_shift[3]                  ; spi_data_reg[4]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.747      ;
; 0.480 ; spi_shift[3]                  ; spi_shift[4]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.747      ;
; 0.481 ; spi_shift[4]                  ; spi_shift[5]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.748      ;
; 0.492 ; spi_shift[12]                 ; spi_shift[13]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.759      ;
; 0.492 ; spi_shift[9]                  ; spi_shift[10]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.759      ;
; 0.492 ; spi_shift[1]                  ; spi_shift[2]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.759      ;
; 0.493 ; spi_shift[8]                  ; spi_shift[9]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.760      ;
; 0.493 ; spi_shift[7]                  ; spi_shift[8]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.760      ;
; 0.493 ; mosi_sync[1]                  ; spi_shift[0]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.760      ;
; 0.511 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.165      ;
; 0.644 ; sclk_sync[0]                  ; sclk_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.913      ;
; 0.644 ; cs_n_sync[0]                  ; cs_n_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.913      ;
; 0.649 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.305      ;
; 0.659 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.330      ;
; 0.659 ; spi_shift[11]                 ; spi_data_reg[12]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.927      ;
; 0.661 ; cs_n_sync[1]                  ; spi_cnt[0]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.930      ;
; 0.667 ; spi_shift[13]                 ; spi_shift[14]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.934      ;
; 0.667 ; spi_shift[11]                 ; spi_shift[12]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.934      ;
; 0.667 ; spi_shift[0]                  ; spi_shift[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.934      ;
; 0.667 ; cs_n_sync[1]                  ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.936      ;
; 0.668 ; spi_shift[10]                 ; spi_shift[11]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.935      ;
; 0.668 ; spi_shift[6]                  ; spi_data_reg[7]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.935      ;
; 0.668 ; cs_n_sync[1]                  ; spi_cnt[2]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.937      ;
; 0.669 ; cs_n_sync[1]                  ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.938      ;
; 0.678 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.349      ;
; 0.684 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.685 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.686 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[18] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.955      ;
; 0.688 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.691 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.362      ;
; 0.703 ; spi_cnt[2]                    ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.972      ;
; 0.706 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.974      ;
; 0.708 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.975      ;
; 0.709 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.976      ;
; 0.709 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.976      ;
; 0.710 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.367      ;
; 0.710 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.977      ;
; 0.712 ; add_32bit:u_add_32bit|add[25] ; add_32bit:u_add_32bit|add[25]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.979      ;
; 0.712 ; add_32bit:u_add_32bit|add[23] ; add_32bit:u_add_32bit|add[23]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.979      ;
; 0.715 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.386      ;
; 0.721 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.392      ;
; 0.723 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.379      ;
; 0.724 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.381      ;
; 0.725 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.396      ;
; 0.726 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.382      ;
; 0.728 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.995      ;
; 0.729 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.386      ;
; 0.733 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.389      ;
; 0.733 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.389      ;
; 0.754 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.411      ;
; 0.756 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.427      ;
; 0.762 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.419      ;
; 0.769 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.440      ;
; 0.777 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.434      ;
; 0.812 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.441      ; 1.483      ;
; 0.829 ; spi_shift[14]                 ; spi_data_reg[15]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.097      ;
; 0.839 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.107      ;
; 0.841 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.424      ; 1.495      ;
; 0.848 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.115      ;
; 0.854 ; spi_shift[13]                 ; spi_data_reg[14]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 1.122      ;
; 0.858 ; sclk_sync[1]                  ; sclk_d1                                                                                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 1.127      ;
; 0.868 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.135      ;
; 0.872 ; spi_cnt[0]                    ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 1.141      ;
; 0.876 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.143      ;
; 0.880 ; add_32bit:u_add_32bit|add[29] ; add_32bit:u_add_32bit|add[29]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.147      ;
; 0.891 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 1.158      ;
; 0.911 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.568      ;
; 0.915 ; spi_shift[2]                  ; spi_data_reg[3]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.083      ; 1.193      ;
; 0.915 ; spi_shift[2]                  ; spi_shift[3]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.083      ; 1.193      ;
; 0.922 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.579      ;
; 0.931 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.588      ;
; 0.934 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.427      ; 1.591      ;
; 0.934 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.426      ; 1.590      ;
; 0.940 ; mosi_sync[1]                  ; spi_data_reg[0]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.083      ; 1.218      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.975  ; 9.975        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.991  ; 9.991        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.008 ; 10.008       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.025 ; 10.025       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; mosi_sync[0]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; mosi_sync[1]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[12]                                                                                                  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[13]                                                                                                  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[14]                                                                                                  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[15]                                                                                                  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[0]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[10]                                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[11]                                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[12]                                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[13]                                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[14]                                                                                                     ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[1]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[2]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[7]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[8]                                                                                                      ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[9]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; cs_n_sync[0]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; cs_n_sync[1]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_d1                                                                                                           ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_sync[0]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_sync[1]                                                                                                      ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[0]                                                                                                        ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[1]                                                                                                        ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[2]                                                                                                        ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[3]                                                                                                        ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[0]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[10]                                                                                                  ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[11]                                                                                                  ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[1]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[2]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[3]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[4]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[5]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[6]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[7]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[8]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[9]                                                                                                   ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[3]                                                                                                      ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[4]                                                                                                      ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[5]                                                                                                      ;
; 24.721 ; 24.937       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[6]                                                                                                      ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.722 ; 24.938       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.723 ; 24.953       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.724 ; 24.954       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.725 ; 24.955       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.726 ; 24.956       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.730 ; 24.960       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.730 ; 24.960       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.731 ; 24.961       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 2.689 ; 3.028 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 4.477 ; 4.514 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; 2.328 ; 2.551 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; 2.067 ; 2.270 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -2.046 ; -2.363 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -3.745 ; -3.787 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; -1.699 ; -1.904 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; -1.460 ; -1.656 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.980  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 12.447 ; 12.380 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 11.415 ; 11.178 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 11.394 ; 11.143 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 11.462 ; 11.206 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 11.576 ; 11.347 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 11.636 ; 11.400 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 12.447 ; 12.380 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 11.296 ; 10.925 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 11.155 ; 10.933 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 11.339 ; 11.139 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 11.289 ; 10.957 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 11.191 ; 10.977 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 10.965 ; 10.804 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 10.958 ; 10.857 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 10.262 ; 10.740 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 6.477  ; 6.266  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.695  ; 5.394  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.346  ; 5.068  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.301  ; 5.018  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.254  ; 4.975  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.501  ; 5.181  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.392  ; 5.216  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 6.477  ; 6.266  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.509  ; 5.176  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.020  ; 4.821  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.296  ; 5.019  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 4.892  ; 5.164  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.287  ; 5.605  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 5.443  ; 5.095  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 4.598  ; 4.846  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.443  ; 5.095  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.150  ; 4.883  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.555  ; 4.373  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 6.396  ; 6.073  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 2.903  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 2.523 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 6.375 ; 6.453 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 7.076 ; 6.558 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 7.068 ; 6.538 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 7.255 ; 6.893 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 7.279 ; 6.759 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 7.286 ; 6.772 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 8.130 ; 7.974 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 6.937 ; 6.729 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 6.960 ; 6.727 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 7.025 ; 6.564 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 6.995 ; 6.766 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 7.025 ; 6.559 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 6.801 ; 6.453 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 6.653 ; 6.461 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 6.375 ; 6.847 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 4.344 ; 4.278 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 5.119 ; 4.828 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 4.784 ; 4.516 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 4.741 ; 4.467 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 4.695 ; 4.426 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 4.933 ; 4.624 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 4.829 ; 4.658 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 5.920 ; 5.718 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 4.941 ; 4.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 4.471 ; 4.278 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 4.736 ; 4.469 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 4.344 ; 4.606 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 4.726 ; 5.033 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 4.024 ; 3.848 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 4.064 ; 4.303 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 4.877 ; 4.541 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 4.596 ; 4.337 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.024 ; 3.848 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 4.934 ; 4.617 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 2.447 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Slow 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 47.522 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.123 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 2.060  ; 0.000         ;
; CLK_50M                                               ; 9.594  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.735 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node        ; To Node                       ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 47.522 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.434      ;
; 47.526 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.430      ;
; 47.543 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.413      ;
; 47.590 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.366      ;
; 47.594 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.362      ;
; 47.607 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.349      ;
; 47.611 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.345      ;
; 47.649 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.307      ;
; 47.658 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.298      ;
; 47.662 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.294      ;
; 47.675 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.281      ;
; 47.679 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.277      ;
; 47.679 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.277      ;
; 47.701 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.255      ;
; 47.705 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.251      ;
; 47.717 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.239      ;
; 47.726 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.230      ;
; 47.730 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.226      ;
; 47.743 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.213      ;
; 47.747 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.209      ;
; 47.747 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.209      ;
; 47.769 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.187      ;
; 47.773 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.183      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.783 ; spi_cnt[0]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.155      ;
; 47.785 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.171      ;
; 47.794 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.162      ;
; 47.798 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.158      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.803 ; spi_cnt[2]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.135      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.806 ; spi_cnt[3]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.132      ;
; 47.811 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.145      ;
; 47.815 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.141      ;
; 47.815 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.141      ;
; 47.837 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[27] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.119      ;
; 47.841 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[26] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.115      ;
; 47.853 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.103      ;
; 47.862 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.094      ;
; 47.866 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.090      ;
; 47.879 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.077      ;
; 47.883 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.073      ;
; 47.883 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.073      ;
; 47.905 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[25] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.051      ;
; 47.909 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[24] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.047      ;
; 47.921 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.035      ;
; 47.930 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.026      ;
; 47.934 ; spi_data_reg[15] ; add_32bit:u_add_32bit|add[18] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.022      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.937 ; spi_cnt[1]       ; spi_data_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 2.001      ;
; 47.947 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[20] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.009      ;
; 47.951 ; spi_data_reg[13] ; add_32bit:u_add_32bit|add[19] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.005      ;
; 47.951 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 2.005      ;
; 47.973 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[23] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 1.983      ;
; 47.977 ; spi_data_reg[12] ; add_32bit:u_add_32bit|add[22] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 1.979      ;
; 47.978 ; spi_cnt[0]       ; spi_data_reg[15]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.054     ; 1.955      ;
; 47.978 ; spi_cnt[0]       ; spi_data_reg[14]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.054     ; 1.955      ;
; 47.978 ; spi_cnt[0]       ; spi_data_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.054     ; 1.955      ;
; 47.978 ; spi_cnt[0]       ; spi_data_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.054     ; 1.955      ;
; 47.989 ; spi_data_reg[14] ; add_32bit:u_add_32bit|add[21] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.031     ; 1.967      ;
+--------+------------------+-------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                 ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                     ; To Node                                                                                                           ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.123 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.451      ;
; 0.151 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.479      ;
; 0.158 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.486      ;
; 0.168 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.496      ;
; 0.173 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.501      ;
; 0.187 ; spi_cnt[3]                    ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; spi_cnt[1]                    ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; spi_cnt[2]                    ; spi_cnt[2]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.307      ;
; 0.194 ; mosi_sync[0]                  ; mosi_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.314      ;
; 0.194 ; spi_cnt[0]                    ; spi_cnt[0]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.314      ;
; 0.195 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.523      ;
; 0.196 ; spi_shift[5]                  ; spi_shift[6]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.316      ;
; 0.199 ; spi_shift[3]                  ; spi_shift[4]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.319      ;
; 0.200 ; spi_shift[4]                  ; spi_data_reg[5]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.320      ;
; 0.200 ; spi_shift[3]                  ; spi_data_reg[4]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.320      ;
; 0.201 ; spi_shift[5]                  ; spi_data_reg[6]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.321      ;
; 0.201 ; spi_shift[4]                  ; spi_shift[5]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.321      ;
; 0.205 ; spi_shift[12]                 ; spi_shift[13]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.325      ;
; 0.205 ; spi_shift[9]                  ; spi_shift[10]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.325      ;
; 0.205 ; spi_shift[8]                  ; spi_shift[9]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.325      ;
; 0.205 ; spi_shift[1]                  ; spi_shift[2]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.325      ;
; 0.206 ; mosi_sync[1]                  ; spi_shift[0]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.326      ;
; 0.207 ; spi_shift[7]                  ; spi_shift[8]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.327      ;
; 0.263 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.593      ;
; 0.267 ; cs_n_sync[0]                  ; cs_n_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.387      ;
; 0.268 ; sclk_sync[0]                  ; sclk_sync[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.388      ;
; 0.270 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.606      ;
; 0.275 ; spi_shift[11]                 ; spi_data_reg[12]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.396      ;
; 0.278 ; spi_shift[13]                 ; spi_shift[14]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.398      ;
; 0.278 ; spi_shift[11]                 ; spi_shift[12]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.398      ;
; 0.278 ; spi_shift[10]                 ; spi_shift[11]                                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.398      ;
; 0.279 ; spi_shift[0]                  ; spi_shift[1]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.399      ;
; 0.280 ; spi_shift[6]                  ; spi_data_reg[7]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.400      ;
; 0.280 ; cs_n_sync[1]                  ; spi_cnt[0]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.400      ;
; 0.283 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.619      ;
; 0.284 ; cs_n_sync[1]                  ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.404      ;
; 0.285 ; cs_n_sync[1]                  ; spi_cnt[2]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.405      ;
; 0.286 ; cs_n_sync[1]                  ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.406      ;
; 0.289 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.625      ;
; 0.293 ; add_32bit:u_add_32bit|add[15] ; add_32bit:u_add_32bit|add[15]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.294 ; add_32bit:u_add_32bit|add[18] ; add_32bit:u_add_32bit|add[18]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[17] ; add_32bit:u_add_32bit|add[17]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[16] ; add_32bit:u_add_32bit|add[16]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[13] ; add_32bit:u_add_32bit|add[13]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[12] ; add_32bit:u_add_32bit|add[12]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[11] ; add_32bit:u_add_32bit|add[11]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[9]  ; add_32bit:u_add_32bit|add[9]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[8]  ; add_32bit:u_add_32bit|add[8]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[7]  ; add_32bit:u_add_32bit|add[7]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[6]  ; add_32bit:u_add_32bit|add[6]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[5]  ; add_32bit:u_add_32bit|add[5]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[1]  ; add_32bit:u_add_32bit|add[1]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.295 ; add_32bit:u_add_32bit|add[14] ; add_32bit:u_add_32bit|add[14]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[4]  ; add_32bit:u_add_32bit|add[4]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[2]  ; add_32bit:u_add_32bit|add[2]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.296 ; add_32bit:u_add_32bit|add[10] ; add_32bit:u_add_32bit|add[10]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.416      ;
; 0.299 ; add_32bit:u_add_32bit|add[0]  ; add_32bit:u_add_32bit|add[0]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.419      ;
; 0.302 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.633      ;
; 0.304 ; add_32bit:u_add_32bit|add[31] ; add_32bit:u_add_32bit|add[31]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; spi_cnt[2]                    ; spi_cnt[3]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.305 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.636      ;
; 0.306 ; add_32bit:u_add_32bit|add[29] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.636      ;
; 0.306 ; add_32bit:u_add_32bit|add[30] ; add_32bit:u_add_32bit|add[30]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; add_32bit:u_add_32bit|add[24] ; add_32bit:u_add_32bit|add[24]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.426      ;
; 0.307 ; add_32bit:u_add_32bit|add[26] ; add_32bit:u_add_32bit|add[26]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.427      ;
; 0.307 ; add_32bit:u_add_32bit|add[25] ; add_32bit:u_add_32bit|add[25]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.427      ;
; 0.307 ; add_32bit:u_add_32bit|add[23] ; add_32bit:u_add_32bit|add[23]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.427      ;
; 0.311 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.641      ;
; 0.312 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.642      ;
; 0.313 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.226      ; 0.643      ;
; 0.314 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.645      ;
; 0.315 ; add_32bit:u_add_32bit|add[20] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.651      ;
; 0.315 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.651      ;
; 0.316 ; add_32bit:u_add_32bit|add[22] ; add_32bit:u_add_32bit|add[22]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.436      ;
; 0.318 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.654      ;
; 0.324 ; add_32bit:u_add_32bit|add[26] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.655      ;
; 0.328 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.659      ;
; 0.329 ; add_32bit:u_add_32bit|add[23] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.665      ;
; 0.332 ; spi_shift[14]                 ; spi_data_reg[15]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.453      ;
; 0.333 ; add_32bit:u_add_32bit|add[24] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.664      ;
; 0.343 ; spi_shift[13]                 ; spi_data_reg[14]                                                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.464      ;
; 0.346 ; sclk_sync[1]                  ; sclk_d1                                                                                                           ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.466      ;
; 0.348 ; add_32bit:u_add_32bit|add[22] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.684      ;
; 0.354 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.232      ; 0.690      ;
; 0.362 ; add_32bit:u_add_32bit|add[3]  ; add_32bit:u_add_32bit|add[3]                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.482      ;
; 0.364 ; add_32bit:u_add_32bit|add[27] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.692      ;
; 0.364 ; add_32bit:u_add_32bit|add[19] ; add_32bit:u_add_32bit|add[19]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.484      ;
; 0.370 ; add_32bit:u_add_32bit|add[28] ; add_32bit:u_add_32bit|add[28]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.490      ;
; 0.374 ; add_32bit:u_add_32bit|add[29] ; add_32bit:u_add_32bit|add[29]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.494      ;
; 0.376 ; add_32bit:u_add_32bit|add[21] ; add_32bit:u_add_32bit|add[21]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.496      ;
; 0.378 ; add_32bit:u_add_32bit|add[20] ; add_32bit:u_add_32bit|add[20]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.498      ;
; 0.382 ; spi_cnt[0]                    ; spi_cnt[1]                                                                                                        ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.502      ;
; 0.404 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.735      ;
; 0.411 ; add_32bit:u_add_32bit|add[21] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.742      ;
; 0.414 ; spi_shift[2]                  ; spi_data_reg[3]                                                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.042      ; 0.540      ;
; 0.415 ; add_32bit:u_add_32bit|add[25] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.746      ;
; 0.415 ; spi_shift[2]                  ; spi_shift[3]                                                                                                      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.042      ; 0.541      ;
; 0.416 ; add_32bit:u_add_32bit|add[31] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.227      ; 0.747      ;
; 0.421 ; add_32bit:u_add_32bit|add[30] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.225      ; 0.750      ;
; 0.423 ; add_32bit:u_add_32bit|add[28] ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.231      ; 0.758      ;
+-------+-------------------------------+-------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.620  ; 9.620        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.622  ; 9.622        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.378 ; 10.378       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.380 ; 10.380       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                  ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[0]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[10]                                                                                                  ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[11]                                                                                                  ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[1]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[2]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[3]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[4]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[5]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[6]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[7]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[8]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[9]                                                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[3]                                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[4]                                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[5]                                                                                                      ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[6]                                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[0]                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[10]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[11]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[12]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[13]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[14]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[15]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[16]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[17]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[18]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[19]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[1]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[20]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[21]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[22]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[23]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[24]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[25]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[26]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[27]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[28]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[29]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[2]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[30]                                                                                     ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[31]                                                                                     ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[3]                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[4]                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[5]                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[6]                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[7]                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[8]                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; add_32bit:u_add_32bit|add[9]                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; cs_n_sync[0]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; cs_n_sync[1]                                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; mosi_sync[0]                                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; mosi_sync[1]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_d1                                                                                                           ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_sync[0]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sclk_sync[1]                                                                                                      ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                 ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[0]                                                                                                        ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[1]                                                                                                        ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[2]                                                                                                        ;
; 24.798 ; 24.982       ; 0.184          ; Low Pulse Width  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_cnt[3]                                                                                                        ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[12]                                                                                                  ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[13]                                                                                                  ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[14]                                                                                                  ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_data_reg[15]                                                                                                  ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[0]                                                                                                      ;
; 24.798 ; 25.014       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; spi_shift[10]                                                                                                     ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 1.537 ; 1.864 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 2.450 ; 3.047 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; 1.388 ; 1.689 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; 1.205 ; 1.522 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -1.204 ; -1.538 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -2.072 ; -2.669 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; -1.066 ; -1.363 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; -0.894 ; -1.214 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                  ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.519 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 6.314 ; 6.396 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 5.435 ; 5.506 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 5.418 ; 5.480 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 5.461 ; 5.558 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 5.539 ; 5.619 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 5.571 ; 5.657 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 6.314 ; 6.396 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 5.309 ; 5.472 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 5.324 ; 5.423 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 5.445 ; 5.529 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 5.330 ; 5.481 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 5.354 ; 5.421 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 5.265 ; 5.322 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 5.283 ; 5.319 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 5.170 ; 5.034 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 3.415 ; 3.569 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.715 ; 2.847 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.555 ; 2.665 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.521 ; 2.621 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.497 ; 2.595 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.615 ; 2.716 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.635 ; 2.771 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 3.415 ; 3.569 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 2.609 ; 2.717 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.421 ; 2.525 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.531 ; 2.631 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.546 ; 2.451 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.784 ; 2.664 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 2.568 ; 2.666 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.376 ; 2.307 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.568 ; 2.666 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.459 ; 2.545 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 2.187 ; 2.247 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 2.981 ; 3.138 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.584 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 3.209 ; 3.161 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 3.246 ; 3.384 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 3.236 ; 3.364 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 3.420 ; 3.562 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 3.363 ; 3.513 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 3.373 ; 3.527 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 4.238 ; 4.350 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 3.343 ; 3.416 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 3.328 ; 3.440 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 3.262 ; 3.425 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 3.370 ; 3.457 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 3.269 ; 3.412 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 3.209 ; 3.347 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 3.212 ; 3.277 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 3.280 ; 3.161 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 2.146 ; 2.175 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.428 ; 2.554 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.274 ; 2.380 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.241 ; 2.338 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.219 ; 2.313 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.332 ; 2.429 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.351 ; 2.482 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 3.137 ; 3.288 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 2.326 ; 2.430 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.146 ; 2.245 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.252 ; 2.348 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.268 ; 2.175 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.495 ; 2.379 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 1.921 ; 1.978 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.102 ; 2.036 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.286 ; 2.381 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.182 ; 2.265 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 1.921 ; 1.978 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 2.361 ; 2.391 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Fast 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                                ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Clock                                                  ; Setup  ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Worst-case Slack                                       ; 44.426 ; 0.123 ; N/A      ; N/A     ; 1.616               ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 1.616               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 9.594               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 44.426 ; 0.123 ; N/A      ; N/A     ; 24.719              ;
; Design-wide TNS                                        ; 0.0    ; 0.0   ; 0.0      ; 0.0     ; 0.0                 ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000  ; 0.000 ; N/A      ; N/A     ; 0.000               ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+


+-------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                 ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; 3.033 ; 3.322 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; 5.080 ; 5.296 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; 2.662 ; 2.821 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; 2.382 ; 2.520 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+-------+-------+------------+-------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                    ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+
; SYS_RST   ; CLK_50M    ; -1.204 ; -1.538 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_cs_n  ; CLK_50M    ; -2.072 ; -2.669 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_mosi  ; CLK_50M    ; -1.066 ; -1.363 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; spi_sclk  ; CLK_50M    ; -0.894 ; -1.214 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+-----------+------------+--------+--------+------------+-------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                                    ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 3.199  ;        ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 13.428 ; 13.466 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 12.202 ; 12.075 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 12.176 ; 12.036 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 12.278 ; 12.094 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 12.396 ; 12.247 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 12.457 ; 12.307 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 13.428 ; 13.466 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 12.121 ; 11.805 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 11.936 ; 11.800 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 12.132 ; 12.027 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 12.108 ; 11.831 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 11.962 ; 11.848 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 11.736 ; 11.660 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 11.729 ; 11.710 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 11.236 ; 11.517 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 7.011  ; 6.938  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 6.073  ; 5.886  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 5.703  ; 5.525  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 5.654  ; 5.474  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 5.603  ; 5.424  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 5.869  ; 5.652  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 5.770  ; 5.683  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 7.011  ; 6.938  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 5.875  ; 5.646  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 5.358  ; 5.258  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 5.645  ; 5.469  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 5.334  ; 5.529  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 5.767  ; 5.979  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 5.802  ; 5.561  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 5.004  ; 5.178  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 5.802  ; 5.561  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 5.509  ; 5.323  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 4.880  ; 4.760  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 6.819  ; 6.653  ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;        ; 3.157  ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+--------+--------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                          ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port            ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK              ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]          ; CLK_50M    ; 3.209 ; 3.161 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]         ; CLK_50M    ; 3.246 ; 3.384 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]         ; CLK_50M    ; 3.236 ; 3.364 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]         ; CLK_50M    ; 3.420 ; 3.562 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]         ; CLK_50M    ; 3.363 ; 3.513 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]         ; CLK_50M    ; 3.373 ; 3.527 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]         ; CLK_50M    ; 4.238 ; 4.350 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]         ; CLK_50M    ; 3.343 ; 3.416 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]         ; CLK_50M    ; 3.328 ; 3.440 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]         ; CLK_50M    ; 3.262 ; 3.425 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]         ; CLK_50M    ; 3.370 ; 3.457 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10]        ; CLK_50M    ; 3.269 ; 3.412 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11]        ; CLK_50M    ; 3.209 ; 3.347 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12]        ; CLK_50M    ; 3.212 ; 3.277 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13]        ; CLK_50M    ; 3.280 ; 3.161 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_amplitude[*]   ; CLK_50M    ; 2.146 ; 2.175 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[0]  ; CLK_50M    ; 2.428 ; 2.554 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[1]  ; CLK_50M    ; 2.274 ; 2.380 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[2]  ; CLK_50M    ; 2.241 ; 2.338 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[3]  ; CLK_50M    ; 2.219 ; 2.313 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[4]  ; CLK_50M    ; 2.332 ; 2.429 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[5]  ; CLK_50M    ; 2.351 ; 2.482 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[6]  ; CLK_50M    ; 3.137 ; 3.288 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[7]  ; CLK_50M    ; 2.326 ; 2.430 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[8]  ; CLK_50M    ; 2.146 ; 2.245 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[9]  ; CLK_50M    ; 2.252 ; 2.348 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[10] ; CLK_50M    ; 2.268 ; 2.175 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_amplitude[11] ; CLK_50M    ; 2.495 ; 2.379 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_freq_sel[*]    ; CLK_50M    ; 1.921 ; 1.978 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[0]   ; CLK_50M    ; 2.102 ; 2.036 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[1]   ; CLK_50M    ; 2.286 ; 2.381 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[2]   ; CLK_50M    ; 2.182 ; 2.265 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  debug_freq_sel[3]   ; CLK_50M    ; 1.921 ; 1.978 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; debug_spi_valid      ; CLK_50M    ; 2.361 ; 2.391 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK              ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+----------------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                          ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin                 ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; PD                  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_CLK             ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[0]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[1]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[2]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[3]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[4]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[5]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[6]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[7]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[8]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[9]         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[10]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[11]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[12]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[13]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_spi_valid     ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[4]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[5]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[6]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[7]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[8]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[9]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[10] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; debug_amplitude[11] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; KEY_IN[0]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[1]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[2]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_RST                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_CLK                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_cs_n                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_sclk                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; spi_mosi                ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.33 V              ; -0.00425 V          ; 0.168 V                              ; 0.058 V                              ; 3.12e-09 s                  ; 2.87e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.33 V             ; -0.00425 V         ; 0.168 V                             ; 0.058 V                             ; 3.12e-09 s                 ; 2.87e-09 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.33 V              ; -0.00229 V          ; 0.111 V                              ; 0.057 V                              ; 3.78e-09 s                  ; 3.5e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.33 V             ; -0.00229 V         ; 0.111 V                             ; 0.057 V                             ; 3.78e-09 s                 ; 3.5e-09 s                  ; Yes                       ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin                 ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD                  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_CLK             ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[0]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; DAC_DATA[6]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[8]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[9]         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[10]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[11]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[12]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[13]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; debug_spi_valid     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_freq_sel[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[4]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[5]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[6]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.64 V              ; -0.0113 V           ; 0.208 V                              ; 0.179 V                              ; 2.38e-09 s                  ; 2.23e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.64 V             ; -0.0113 V          ; 0.208 V                             ; 0.179 V                             ; 2.38e-09 s                 ; 2.23e-09 s                 ; No                        ; Yes                       ;
; debug_amplitude[7]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[8]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[9]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[10] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; debug_amplitude[11] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
+---------------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                                                           ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 2974     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                                            ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 2974     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths                            ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 0     ; 0    ;
; Unconstrained Input Ports       ; 4     ; 4    ;
; Unconstrained Input Port Paths  ; 91    ; 91   ;
; Unconstrained Output Ports      ; 32    ; 32   ;
; Unconstrained Output Port Paths ; 386   ; 386  ;
+---------------------------------+-------+------+


+------------------------------------+
; TimeQuest Timing Analyzer Messages ;
+------------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Fri Aug 01 10:31:21 2025
Info: Command: quartus_sta DAC904 -c DAC904
Info: qsta_default_script.tcl version: #1
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Info (332104): Reading SDC File: '../doc/SDC1.sdc'
Info (332110): Deriving PLL clocks
    Info (332110): create_generated_clock -source {u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]} {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]}
Info (332151): Clock uncertainty is not calculated until you update the timing netlist.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info: Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Info (332146): Worst-case setup slack is 44.426
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    44.426               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.387
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.387               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.934               0.000 CLK_50M 
    Info (332119):    24.720               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 44.845
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    44.845               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.369
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.369               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.943               0.000 CLK_50M 
    Info (332119):    24.719               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Fast 1200mV 0C Model
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 47.522
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    47.522               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.123
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.123               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 2.060
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     2.060               0.000 CLK_165M 
    Info (332119):     9.594               0.000 CLK_50M 
    Info (332119):    24.735               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 1 warning
    Info: Peak virtual memory: 4633 megabytes
    Info: Processing ended: Fri Aug 01 10:31:22 2025
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


