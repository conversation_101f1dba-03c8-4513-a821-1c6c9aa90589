******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Fri Aug  1 03:25:06 2025

OUTPUT FILE NAME:   <DAC904_spi_controller_command_data_control.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000491


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000768  0001f898  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000768   00000768    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000680   00000680    r-x .text
  00000740    00000740    00000010   00000010    r-- .rodata
  00000750    00000750    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000680     
                  000000c0    000000ac     spi_controller_command_data_control.o (.text.send_dac_params)
                  0000016c    0000009c     spi_controller_command_data_control.o (.text.main)
                  00000208    0000008c     spi_controller_command_data_control.o (.text.delay_ms_safe)
                  00000294    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000002e4    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_0_init)
                  00000334    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00000378    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000003bc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000003fc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000438    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00000468    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00000490    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000004b8    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  000004dc    00000024     ti_msp_dl_config.o (.text.DL_SPI_setControllerCommandDataModeConfig)
                  00000500    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00000520    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000540    0000001e     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000055e    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000560    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000057c    0000001c     ti_msp_dl_config.o (.text.DL_SPI_enableControllerCommandDataMode)
                  00000598    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000005b4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000005cc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000005e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000005fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000614    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  0000062c    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00000644    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  0000065c    00000018     spi_controller_command_data_control.o (.text.DL_SPI_isBusy)
                  00000674    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  0000068c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000006a4    00000016     spi_controller_command_data_control.o (.text.DL_SPI_transmitData16)
                  000006ba    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000006d0    00000014     spi_controller_command_data_control.o (.text.DL_GPIO_clearPins)
                  000006e4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000006f8    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0000070a    00000002     --HOLE-- [fill = 0]
                  0000070c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000071c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000726    00000006     libc.a : exit.c.obj (.text:abort)
                  0000072c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000730    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000734    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000738    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000073c    00000004     --HOLE-- [fill = 0]

.cinit     0    00000750    00000018     
                  00000750    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000758    00000004     (__TI_handler_table)
                  0000075c    00000008     (__TI_cinit_table)
                  00000764    00000004     --HOLE-- [fill = 0]

.rodata    0    00000740    00000010     
                  00000740    0000000a     ti_msp_dl_config.o (.rodata.gSPI_0_config)
                  0000074a    00000002     ti_msp_dl_config.o (.rodata.gSPI_0_clockConfig)
                  0000074c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_0Backup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                                  code   ro data   rw data
       ------                                  ----   -------   -------
    .\
       ti_msp_dl_config.o                      818    12        40     
       spi_controller_command_data_control.o   534    0         0      
       startup_mspm0g350x_ticlang.o            8      192       0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1360   204       40     
                                                                       
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_spi.o                                86     0         0      
       dl_common.o                             10     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  96     0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                          60     0         0      
       boot_cortex_m.c.obj                     40     0         0      
       copy_zero_init.c.obj                    22     0         0      
       exit.c.obj                              6      0         0      
       pre_init.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  132    0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  4      0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj                    64     0         0      
       aeabi_div0.c.obj                        2      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  66     0         0      
                                                                       
       Stack:                                  0      0         512    
       Linker Generated:                       0      20        0      
    +--+---------------------------------------+------+---------+---------+
       Grand Total:                            1658   224       552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000075c records: 1, size/record: 8, table size: 8
	.bss: load addr=00000750, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000758 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000072d  ADC0_IRQHandler               
0000072d  ADC1_IRQHandler               
0000072d  AES_IRQHandler                
00000730  C$$EXIT                       
0000072d  CANFD0_IRQHandler             
0000072d  DAC0_IRQHandler               
0000071d  DL_Common_delayCycles         
00000335  DL_SPI_init                   
000006f9  DL_SPI_setClockConfig         
0000072d  DMA_IRQHandler                
0000072d  Default_Handler               
0000072d  GROUP0_IRQHandler             
0000072d  GROUP1_IRQHandler             
00000731  HOSTexit                      
0000072d  HardFault_Handler             
0000072d  I2C0_IRQHandler               
0000072d  I2C1_IRQHandler               
0000072d  NMI_Handler                   
0000072d  PendSV_Handler                
0000072d  RTC_IRQHandler                
00000735  Reset_Handler                 
0000072d  SPI0_IRQHandler               
0000072d  SPI1_IRQHandler               
0000072d  SVC_Handler                   
00000295  SYSCFG_DL_GPIO_init           
000002e5  SYSCFG_DL_SPI_0_init          
00000541  SYSCFG_DL_SYSCTL_init         
00000521  SYSCFG_DL_init                
00000379  SYSCFG_DL_initPower           
0000072d  SysTick_Handler               
0000072d  TIMA0_IRQHandler              
0000072d  TIMA1_IRQHandler              
0000072d  TIMG0_IRQHandler              
0000072d  TIMG12_IRQHandler             
0000072d  TIMG6_IRQHandler              
0000072d  TIMG7_IRQHandler              
0000072d  TIMG8_IRQHandler              
0000072d  UART0_IRQHandler              
0000072d  UART1_IRQHandler              
0000072d  UART2_IRQHandler              
0000072d  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
0000075c  __TI_CINIT_Base               
00000764  __TI_CINIT_Limit              
00000764  __TI_CINIT_Warm               
00000758  __TI_Handler_Table_Base       
0000075c  __TI_Handler_Table_Limit      
000003fd  __TI_auto_init_nobinit_nopinit
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
000006bb  __TI_zero_init_nomemset       
0000055f  __aeabi_idiv0                 
000003bd  __aeabi_uidiv                 
000003bd  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000491  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000739  _system_pre_init              
00000727  abort                         
ffffffff  binit                         
00000209  delay_ms_safe                 
20200000  gSPI_0Backup                  
00000000  interruptVectors              
0000016d  main                          
000000c1  send_dac_params               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  send_dac_params               
0000016d  main                          
00000200  __STACK_SIZE                  
00000209  delay_ms_safe                 
00000295  SYSCFG_DL_GPIO_init           
000002e5  SYSCFG_DL_SPI_0_init          
00000335  DL_SPI_init                   
00000379  SYSCFG_DL_initPower           
000003bd  __aeabi_uidiv                 
000003bd  __aeabi_uidivmod              
000003fd  __TI_auto_init_nobinit_nopinit
00000491  _c_int00_noargs               
00000521  SYSCFG_DL_init                
00000541  SYSCFG_DL_SYSCTL_init         
0000055f  __aeabi_idiv0                 
000006bb  __TI_zero_init_nomemset       
000006f9  DL_SPI_setClockConfig         
0000071d  DL_Common_delayCycles         
00000727  abort                         
0000072d  ADC0_IRQHandler               
0000072d  ADC1_IRQHandler               
0000072d  AES_IRQHandler                
0000072d  CANFD0_IRQHandler             
0000072d  DAC0_IRQHandler               
0000072d  DMA_IRQHandler                
0000072d  Default_Handler               
0000072d  GROUP0_IRQHandler             
0000072d  GROUP1_IRQHandler             
0000072d  HardFault_Handler             
0000072d  I2C0_IRQHandler               
0000072d  I2C1_IRQHandler               
0000072d  NMI_Handler                   
0000072d  PendSV_Handler                
0000072d  RTC_IRQHandler                
0000072d  SPI0_IRQHandler               
0000072d  SPI1_IRQHandler               
0000072d  SVC_Handler                   
0000072d  SysTick_Handler               
0000072d  TIMA0_IRQHandler              
0000072d  TIMA1_IRQHandler              
0000072d  TIMG0_IRQHandler              
0000072d  TIMG12_IRQHandler             
0000072d  TIMG6_IRQHandler              
0000072d  TIMG7_IRQHandler              
0000072d  TIMG8_IRQHandler              
0000072d  UART0_IRQHandler              
0000072d  UART1_IRQHandler              
0000072d  UART2_IRQHandler              
0000072d  UART3_IRQHandler              
00000730  C$$EXIT                       
00000731  HOSTexit                      
00000735  Reset_Handler                 
00000739  _system_pre_init              
00000758  __TI_Handler_Table_Base       
0000075c  __TI_CINIT_Base               
0000075c  __TI_Handler_Table_Limit      
00000764  __TI_CINIT_Limit              
00000764  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gSPI_0Backup                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[83 symbols]
