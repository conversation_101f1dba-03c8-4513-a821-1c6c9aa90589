******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Fri Aug  1 03:13:01 2025

OUTPUT FILE NAME:   <DAC904_spi_controller_command_data_control.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000004e9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000007f0  0001f810  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000007f0   000007f0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000708   00000708    r-x .text
  000007c8    000007c8    00000010   00000010    r-- .rodata
  000007d8    000007d8    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000708     
                  000000c0    000000e0     spi_controller_command_data_control.o (.text.send_dac_params)
                  000001a0    000000c0     spi_controller_command_data_control.o (.text.main)
                  00000260    0000008c     spi_controller_command_data_control.o (.text.delay_ms_safe)
                  000002ec    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000033c    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_0_init)
                  0000038c    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  000003d0    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000414    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000454    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000490    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  000004c0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000004e8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000510    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00000534    00000024     ti_msp_dl_config.o (.text.DL_SPI_setControllerCommandDataModeConfig)
                  00000558    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00000578    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000598    0000001e     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000005b6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000005d4    0000001c     ti_msp_dl_config.o (.text.DL_SPI_enableControllerCommandDataMode)
                  000005f0    0000001c     spi_controller_command_data_control.o (.text.DL_SPI_isTXFIFOFull)
                  0000060c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00000628    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000640    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000658    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00000670    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000688    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000006a0    00000018     spi_controller_command_data_control.o (.text.DL_GPIO_togglePins)
                  000006b8    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  000006d0    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  000006e8    00000018     spi_controller_command_data_control.o (.text.DL_SPI_isBusy)
                  00000700    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00000718    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00000730    00000016     spi_controller_command_data_control.o (.text.DL_SPI_transmitData8)
                  00000746    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000075c    00000014     spi_controller_command_data_control.o (.text.DL_GPIO_clearPins)
                  00000770    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00000784    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00000796    00000002     --HOLE-- [fill = 0]
                  00000798    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000007a8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000007b2    00000006     libc.a : exit.c.obj (.text:abort)
                  000007b8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000007bc    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000007c0    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000007c4    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000007d8    00000018     
                  000007d8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000007e0    00000004     (__TI_handler_table)
                  000007e4    00000008     (__TI_cinit_table)
                  000007ec    00000004     --HOLE-- [fill = 0]

.rodata    0    000007c8    00000010     
                  000007c8    0000000a     ti_msp_dl_config.o (.rodata.gSPI_0_config)
                  000007d2    00000002     ti_msp_dl_config.o (.rodata.gSPI_0_clockConfig)
                  000007d4    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_0Backup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                                  code   ro data   rw data
       ------                                  ----   -------   -------
    .\
       ti_msp_dl_config.o                      818    12        40     
       spi_controller_command_data_control.o   674    0         0      
       startup_mspm0g350x_ticlang.o            8      192       0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1500   204       40     
                                                                       
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_spi.o                                86     0         0      
       dl_common.o                             10     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  96     0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                          60     0         0      
       boot_cortex_m.c.obj                     40     0         0      
       copy_zero_init.c.obj                    22     0         0      
       exit.c.obj                              6      0         0      
       pre_init.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  132    0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  4      0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj                    64     0         0      
       aeabi_div0.c.obj                        2      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  66     0         0      
                                                                       
       Stack:                                  0      0         512    
       Linker Generated:                       0      20        0      
    +--+---------------------------------------+------+---------+---------+
       Grand Total:                            1798   224       552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000007e4 records: 1, size/record: 8, table size: 8
	.bss: load addr=000007d8, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000007e0 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
000007b9  ADC0_IRQHandler               
000007b9  ADC1_IRQHandler               
000007b9  AES_IRQHandler                
000007bc  C$$EXIT                       
000007b9  CANFD0_IRQHandler             
000007b9  DAC0_IRQHandler               
000007a9  DL_Common_delayCycles         
0000038d  DL_SPI_init                   
00000785  DL_SPI_setClockConfig         
000007b9  DMA_IRQHandler                
000007b9  Default_Handler               
000007b9  GROUP0_IRQHandler             
000007b9  GROUP1_IRQHandler             
000007bd  HOSTexit                      
000007b9  HardFault_Handler             
000007b9  I2C0_IRQHandler               
000007b9  I2C1_IRQHandler               
000007b9  NMI_Handler                   
000007b9  PendSV_Handler                
000007b9  RTC_IRQHandler                
000007c1  Reset_Handler                 
000007b9  SPI0_IRQHandler               
000007b9  SPI1_IRQHandler               
000007b9  SVC_Handler                   
000002ed  SYSCFG_DL_GPIO_init           
0000033d  SYSCFG_DL_SPI_0_init          
00000599  SYSCFG_DL_SYSCTL_init         
00000579  SYSCFG_DL_init                
000003d1  SYSCFG_DL_initPower           
000007b9  SysTick_Handler               
000007b9  TIMA0_IRQHandler              
000007b9  TIMA1_IRQHandler              
000007b9  TIMG0_IRQHandler              
000007b9  TIMG12_IRQHandler             
000007b9  TIMG6_IRQHandler              
000007b9  TIMG7_IRQHandler              
000007b9  TIMG8_IRQHandler              
000007b9  UART0_IRQHandler              
000007b9  UART1_IRQHandler              
000007b9  UART2_IRQHandler              
000007b9  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000007e4  __TI_CINIT_Base               
000007ec  __TI_CINIT_Limit              
000007ec  __TI_CINIT_Warm               
000007e0  __TI_Handler_Table_Base       
000007e4  __TI_Handler_Table_Limit      
00000455  __TI_auto_init_nobinit_nopinit
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000747  __TI_zero_init_nomemset       
000005b7  __aeabi_idiv0                 
00000415  __aeabi_uidiv                 
00000415  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000004e9  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000007c5  _system_pre_init              
000007b3  abort                         
ffffffff  binit                         
00000261  delay_ms_safe                 
20200000  gSPI_0Backup                  
00000000  interruptVectors              
000001a1  main                          
000000c1  send_dac_params               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  send_dac_params               
000001a1  main                          
00000200  __STACK_SIZE                  
00000261  delay_ms_safe                 
000002ed  SYSCFG_DL_GPIO_init           
0000033d  SYSCFG_DL_SPI_0_init          
0000038d  DL_SPI_init                   
000003d1  SYSCFG_DL_initPower           
00000415  __aeabi_uidiv                 
00000415  __aeabi_uidivmod              
00000455  __TI_auto_init_nobinit_nopinit
000004e9  _c_int00_noargs               
00000579  SYSCFG_DL_init                
00000599  SYSCFG_DL_SYSCTL_init         
000005b7  __aeabi_idiv0                 
00000747  __TI_zero_init_nomemset       
00000785  DL_SPI_setClockConfig         
000007a9  DL_Common_delayCycles         
000007b3  abort                         
000007b9  ADC0_IRQHandler               
000007b9  ADC1_IRQHandler               
000007b9  AES_IRQHandler                
000007b9  CANFD0_IRQHandler             
000007b9  DAC0_IRQHandler               
000007b9  DMA_IRQHandler                
000007b9  Default_Handler               
000007b9  GROUP0_IRQHandler             
000007b9  GROUP1_IRQHandler             
000007b9  HardFault_Handler             
000007b9  I2C0_IRQHandler               
000007b9  I2C1_IRQHandler               
000007b9  NMI_Handler                   
000007b9  PendSV_Handler                
000007b9  RTC_IRQHandler                
000007b9  SPI0_IRQHandler               
000007b9  SPI1_IRQHandler               
000007b9  SVC_Handler                   
000007b9  SysTick_Handler               
000007b9  TIMA0_IRQHandler              
000007b9  TIMA1_IRQHandler              
000007b9  TIMG0_IRQHandler              
000007b9  TIMG12_IRQHandler             
000007b9  TIMG6_IRQHandler              
000007b9  TIMG7_IRQHandler              
000007b9  TIMG8_IRQHandler              
000007b9  UART0_IRQHandler              
000007b9  UART1_IRQHandler              
000007b9  UART2_IRQHandler              
000007b9  UART3_IRQHandler              
000007bc  C$$EXIT                       
000007bd  HOSTexit                      
000007c1  Reset_Handler                 
000007c5  _system_pre_init              
000007e0  __TI_Handler_Table_Base       
000007e4  __TI_CINIT_Base               
000007e4  __TI_Handler_Table_Limit      
000007ec  __TI_CINIT_Limit              
000007ec  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gSPI_0Backup                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[83 symbols]
