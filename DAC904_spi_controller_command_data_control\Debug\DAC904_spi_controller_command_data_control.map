******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Fri Aug  1 03:28:30 2025

OUTPUT FILE NAME:   <DAC904_spi_controller_command_data_control.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000048d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000738  0001f8c8  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000738   00000738    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000650   00000650    r-x .text
  00000710    00000710    00000010   00000010    r-- .rodata
  00000720    00000720    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000650     
                  000000c0    000000c0     spi_controller_command_data_control.o (.text.send_dac_params)
                  00000180    0000009c     spi_controller_command_data_control.o (.text.main)
                  0000021c    0000008c     spi_controller_command_data_control.o (.text.delay_ms_safe)
                  000002a8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000002f0    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00000334    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000378    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_0_init)
                  000003b8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000003f8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000434    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00000464    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000048c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000004b4    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  000004d8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000004f8    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000518    0000001e     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000536    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000538    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00000554    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00000570    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000588    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000005a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000005b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000005d0    00000018     spi_controller_command_data_control.o (.text.DL_GPIO_setPins)
                  000005e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00000600    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00000618    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00000630    00000018     spi_controller_command_data_control.o (.text.DL_SPI_isBusy)
                  00000648    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00000660    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00000678    00000016     spi_controller_command_data_control.o (.text.DL_SPI_transmitData16)
                  0000068e    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000006a4    00000014     spi_controller_command_data_control.o (.text.DL_GPIO_clearPins)
                  000006b8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000006cc    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  000006de    00000002     --HOLE-- [fill = 0]
                  000006e0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000006f0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000006fa    00000006     libc.a : exit.c.obj (.text:abort)
                  00000700    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000704    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000708    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000070c    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00000720    00000018     
                  00000720    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000728    00000004     (__TI_handler_table)
                  0000072c    00000008     (__TI_cinit_table)
                  00000734    00000004     --HOLE-- [fill = 0]

.rodata    0    00000710    00000010     
                  00000710    0000000a     ti_msp_dl_config.o (.rodata.gSPI_0_config)
                  0000071a    00000002     ti_msp_dl_config.o (.rodata.gSPI_0_clockConfig)
                  0000071c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_0Backup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                                  code   ro data   rw data
       ------                                  ----   -------   -------
    .\
       ti_msp_dl_config.o                      730    12        40     
       spi_controller_command_data_control.o   578    0         0      
       startup_mspm0g350x_ticlang.o            8      192       0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1316   204       40     
                                                                       
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_spi.o                                86     0         0      
       dl_common.o                             10     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  96     0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                          60     0         0      
       boot_cortex_m.c.obj                     40     0         0      
       copy_zero_init.c.obj                    22     0         0      
       exit.c.obj                              6      0         0      
       pre_init.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  132    0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  4      0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj                    64     0         0      
       aeabi_div0.c.obj                        2      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  66     0         0      
                                                                       
       Stack:                                  0      0         512    
       Linker Generated:                       0      20        0      
    +--+---------------------------------------+------+---------+---------+
       Grand Total:                            1614   224       552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000072c records: 1, size/record: 8, table size: 8
	.bss: load addr=00000720, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000728 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000701  ADC0_IRQHandler               
00000701  ADC1_IRQHandler               
00000701  AES_IRQHandler                
00000704  C$$EXIT                       
00000701  CANFD0_IRQHandler             
00000701  DAC0_IRQHandler               
000006f1  DL_Common_delayCycles         
000002f1  DL_SPI_init                   
000006cd  DL_SPI_setClockConfig         
00000701  DMA_IRQHandler                
00000701  Default_Handler               
00000701  GROUP0_IRQHandler             
00000701  GROUP1_IRQHandler             
00000705  HOSTexit                      
00000701  HardFault_Handler             
00000701  I2C0_IRQHandler               
00000701  I2C1_IRQHandler               
00000701  NMI_Handler                   
00000701  PendSV_Handler                
00000701  RTC_IRQHandler                
00000709  Reset_Handler                 
00000701  SPI0_IRQHandler               
00000701  SPI1_IRQHandler               
00000701  SVC_Handler                   
000002a9  SYSCFG_DL_GPIO_init           
00000379  SYSCFG_DL_SPI_0_init          
00000519  SYSCFG_DL_SYSCTL_init         
000004f9  SYSCFG_DL_init                
00000335  SYSCFG_DL_initPower           
00000701  SysTick_Handler               
00000701  TIMA0_IRQHandler              
00000701  TIMA1_IRQHandler              
00000701  TIMG0_IRQHandler              
00000701  TIMG12_IRQHandler             
00000701  TIMG6_IRQHandler              
00000701  TIMG7_IRQHandler              
00000701  TIMG8_IRQHandler              
00000701  UART0_IRQHandler              
00000701  UART1_IRQHandler              
00000701  UART2_IRQHandler              
00000701  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
0000072c  __TI_CINIT_Base               
00000734  __TI_CINIT_Limit              
00000734  __TI_CINIT_Warm               
00000728  __TI_Handler_Table_Base       
0000072c  __TI_Handler_Table_Limit      
000003f9  __TI_auto_init_nobinit_nopinit
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
0000068f  __TI_zero_init_nomemset       
00000537  __aeabi_idiv0                 
000003b9  __aeabi_uidiv                 
000003b9  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
0000048d  _c_int00_noargs               
UNDEFED   _system_post_cinit            
0000070d  _system_pre_init              
000006fb  abort                         
ffffffff  binit                         
0000021d  delay_ms_safe                 
20200000  gSPI_0Backup                  
00000000  interruptVectors              
00000181  main                          
000000c1  send_dac_params               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  send_dac_params               
00000181  main                          
00000200  __STACK_SIZE                  
0000021d  delay_ms_safe                 
000002a9  SYSCFG_DL_GPIO_init           
000002f1  DL_SPI_init                   
00000335  SYSCFG_DL_initPower           
00000379  SYSCFG_DL_SPI_0_init          
000003b9  __aeabi_uidiv                 
000003b9  __aeabi_uidivmod              
000003f9  __TI_auto_init_nobinit_nopinit
0000048d  _c_int00_noargs               
000004f9  SYSCFG_DL_init                
00000519  SYSCFG_DL_SYSCTL_init         
00000537  __aeabi_idiv0                 
0000068f  __TI_zero_init_nomemset       
000006cd  DL_SPI_setClockConfig         
000006f1  DL_Common_delayCycles         
000006fb  abort                         
00000701  ADC0_IRQHandler               
00000701  ADC1_IRQHandler               
00000701  AES_IRQHandler                
00000701  CANFD0_IRQHandler             
00000701  DAC0_IRQHandler               
00000701  DMA_IRQHandler                
00000701  Default_Handler               
00000701  GROUP0_IRQHandler             
00000701  GROUP1_IRQHandler             
00000701  HardFault_Handler             
00000701  I2C0_IRQHandler               
00000701  I2C1_IRQHandler               
00000701  NMI_Handler                   
00000701  PendSV_Handler                
00000701  RTC_IRQHandler                
00000701  SPI0_IRQHandler               
00000701  SPI1_IRQHandler               
00000701  SVC_Handler                   
00000701  SysTick_Handler               
00000701  TIMA0_IRQHandler              
00000701  TIMA1_IRQHandler              
00000701  TIMG0_IRQHandler              
00000701  TIMG12_IRQHandler             
00000701  TIMG6_IRQHandler              
00000701  TIMG7_IRQHandler              
00000701  TIMG8_IRQHandler              
00000701  UART0_IRQHandler              
00000701  UART1_IRQHandler              
00000701  UART2_IRQHandler              
00000701  UART3_IRQHandler              
00000704  C$$EXIT                       
00000705  HOSTexit                      
00000709  Reset_Handler                 
0000070d  _system_pre_init              
00000728  __TI_Handler_Table_Base       
0000072c  __TI_CINIT_Base               
0000072c  __TI_Handler_Table_Limit      
00000734  __TI_CINIT_Limit              
00000734  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gSPI_0Backup                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[83 symbols]
