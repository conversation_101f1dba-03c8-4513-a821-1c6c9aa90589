******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Fri Aug  1 07:54:58 2025

OUTPUT FILE NAME:   <DAC904_spi_controller_command_data_control.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000045d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000006e8  0001f918  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000006e8   000006e8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000600   00000600    r-x .text
  000006c0    000006c0    00000010   00000010    r-- .rodata
  000006d0    000006d0    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000600     
                  000000c0    000000c0     spi_controller_command_data_control.o (.text.send_dac_params)
                  00000180    0000008c     spi_controller_command_data_control.o (.text.delay_ms_safe)
                  0000020c    00000074     spi_controller_command_data_control.o (.text.main)
                  00000280    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  000002c4    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000308    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000348    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_0_init)
                  00000388    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000003c8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000404    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00000434    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000045c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000484    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  000004a8    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000004c8    0000001e     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000004e6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000004e8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00000504    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00000520    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000538    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000550    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00000568    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000580    00000018     spi_controller_command_data_control.o (.text.DL_GPIO_setPins)
                  00000598    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000005b0    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  000005c8    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  000005e0    00000018     spi_controller_command_data_control.o (.text.DL_SPI_isBusy)
                  000005f8    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00000610    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00000628    00000016     spi_controller_command_data_control.o (.text.DL_SPI_transmitData16)
                  0000063e    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000654    00000014     spi_controller_command_data_control.o (.text.DL_GPIO_clearPins)
                  00000668    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000067c    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0000068e    00000002     --HOLE-- [fill = 0]
                  00000690    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000006a0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000006aa    00000006     libc.a : exit.c.obj (.text:abort)
                  000006b0    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000006b4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000006b8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000006bc    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000006d0    00000018     
                  000006d0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000006d8    00000004     (__TI_handler_table)
                  000006dc    00000008     (__TI_cinit_table)
                  000006e4    00000004     --HOLE-- [fill = 0]

.rodata    0    000006c0    00000010     
                  000006c0    0000000a     ti_msp_dl_config.o (.rodata.gSPI_0_config)
                  000006ca    00000002     ti_msp_dl_config.o (.rodata.gSPI_0_clockConfig)
                  000006cc    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_0Backup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                                  code   ro data   rw data
       ------                                  ----   -------   -------
    .\
       ti_msp_dl_config.o                      690    12        40     
       spi_controller_command_data_control.o   538    0         0      
       startup_mspm0g350x_ticlang.o            8      192       0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1236   204       40     
                                                                       
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_spi.o                                86     0         0      
       dl_common.o                             10     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  96     0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                          60     0         0      
       boot_cortex_m.c.obj                     40     0         0      
       copy_zero_init.c.obj                    22     0         0      
       exit.c.obj                              6      0         0      
       pre_init.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  132    0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  4      0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj                    64     0         0      
       aeabi_div0.c.obj                        2      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  66     0         0      
                                                                       
       Stack:                                  0      0         512    
       Linker Generated:                       0      20        0      
    +--+---------------------------------------+------+---------+---------+
       Grand Total:                            1534   224       552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000006dc records: 1, size/record: 8, table size: 8
	.bss: load addr=000006d0, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000006d8 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
000006b1  ADC0_IRQHandler               
000006b1  ADC1_IRQHandler               
000006b1  AES_IRQHandler                
000006b4  C$$EXIT                       
000006b1  CANFD0_IRQHandler             
000006b1  DAC0_IRQHandler               
000006a1  DL_Common_delayCycles         
00000281  DL_SPI_init                   
0000067d  DL_SPI_setClockConfig         
000006b1  DMA_IRQHandler                
000006b1  Default_Handler               
000006b1  GROUP0_IRQHandler             
000006b1  GROUP1_IRQHandler             
000006b5  HOSTexit                      
000006b1  HardFault_Handler             
000006b1  I2C0_IRQHandler               
000006b1  I2C1_IRQHandler               
000006b1  NMI_Handler                   
000006b1  PendSV_Handler                
000006b1  RTC_IRQHandler                
000006b9  Reset_Handler                 
000006b1  SPI0_IRQHandler               
000006b1  SPI1_IRQHandler               
000006b1  SVC_Handler                   
00000309  SYSCFG_DL_GPIO_init           
00000349  SYSCFG_DL_SPI_0_init          
000004c9  SYSCFG_DL_SYSCTL_init         
000004a9  SYSCFG_DL_init                
000002c5  SYSCFG_DL_initPower           
000006b1  SysTick_Handler               
000006b1  TIMA0_IRQHandler              
000006b1  TIMA1_IRQHandler              
000006b1  TIMG0_IRQHandler              
000006b1  TIMG12_IRQHandler             
000006b1  TIMG6_IRQHandler              
000006b1  TIMG7_IRQHandler              
000006b1  TIMG8_IRQHandler              
000006b1  UART0_IRQHandler              
000006b1  UART1_IRQHandler              
000006b1  UART2_IRQHandler              
000006b1  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000006dc  __TI_CINIT_Base               
000006e4  __TI_CINIT_Limit              
000006e4  __TI_CINIT_Warm               
000006d8  __TI_Handler_Table_Base       
000006dc  __TI_Handler_Table_Limit      
000003c9  __TI_auto_init_nobinit_nopinit
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
0000063f  __TI_zero_init_nomemset       
000004e7  __aeabi_idiv0                 
00000389  __aeabi_uidiv                 
00000389  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
0000045d  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000006bd  _system_pre_init              
000006ab  abort                         
ffffffff  binit                         
00000181  delay_ms_safe                 
20200000  gSPI_0Backup                  
00000000  interruptVectors              
0000020d  main                          
000000c1  send_dac_params               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  send_dac_params               
00000181  delay_ms_safe                 
00000200  __STACK_SIZE                  
0000020d  main                          
00000281  DL_SPI_init                   
000002c5  SYSCFG_DL_initPower           
00000309  SYSCFG_DL_GPIO_init           
00000349  SYSCFG_DL_SPI_0_init          
00000389  __aeabi_uidiv                 
00000389  __aeabi_uidivmod              
000003c9  __TI_auto_init_nobinit_nopinit
0000045d  _c_int00_noargs               
000004a9  SYSCFG_DL_init                
000004c9  SYSCFG_DL_SYSCTL_init         
000004e7  __aeabi_idiv0                 
0000063f  __TI_zero_init_nomemset       
0000067d  DL_SPI_setClockConfig         
000006a1  DL_Common_delayCycles         
000006ab  abort                         
000006b1  ADC0_IRQHandler               
000006b1  ADC1_IRQHandler               
000006b1  AES_IRQHandler                
000006b1  CANFD0_IRQHandler             
000006b1  DAC0_IRQHandler               
000006b1  DMA_IRQHandler                
000006b1  Default_Handler               
000006b1  GROUP0_IRQHandler             
000006b1  GROUP1_IRQHandler             
000006b1  HardFault_Handler             
000006b1  I2C0_IRQHandler               
000006b1  I2C1_IRQHandler               
000006b1  NMI_Handler                   
000006b1  PendSV_Handler                
000006b1  RTC_IRQHandler                
000006b1  SPI0_IRQHandler               
000006b1  SPI1_IRQHandler               
000006b1  SVC_Handler                   
000006b1  SysTick_Handler               
000006b1  TIMA0_IRQHandler              
000006b1  TIMA1_IRQHandler              
000006b1  TIMG0_IRQHandler              
000006b1  TIMG12_IRQHandler             
000006b1  TIMG6_IRQHandler              
000006b1  TIMG7_IRQHandler              
000006b1  TIMG8_IRQHandler              
000006b1  UART0_IRQHandler              
000006b1  UART1_IRQHandler              
000006b1  UART2_IRQHandler              
000006b1  UART3_IRQHandler              
000006b4  C$$EXIT                       
000006b5  HOSTexit                      
000006b9  Reset_Handler                 
000006bd  _system_pre_init              
000006d8  __TI_Handler_Table_Base       
000006dc  __TI_CINIT_Base               
000006dc  __TI_Handler_Table_Limit      
000006e4  __TI_CINIT_Limit              
000006e4  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gSPI_0Backup                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[83 symbols]
