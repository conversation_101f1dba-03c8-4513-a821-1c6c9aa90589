******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Fri Aug  1 10:37:10 2025

OUTPUT FILE NAME:   <DAC904_spi_controller_command_data_control.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000489


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000738  0001f8c8  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000738   00000738    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000650   00000650    r-x .text
  00000710    00000710    00000010   00000010    r-- .rodata
  00000720    00000720    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000650     
                  000000c0    000000c0     spi_controller_command_data_control.o (.text.send_dac_params)
                  00000180    00000098     spi_controller_command_data_control.o (.text.main)
                  00000218    0000008c     spi_controller_command_data_control.o (.text.delay_ms_safe)
                  000002a4    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000002ec    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00000330    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000374    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_0_init)
                  000003b4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000003f4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000430    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00000460    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00000488    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000004b0    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  000004d4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000004f4    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000514    0000001e     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000532    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000534    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00000550    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000056c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000584    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000059c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000005b4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000005cc    00000018     spi_controller_command_data_control.o (.text.DL_GPIO_setPins)
                  000005e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000005fc    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00000614    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  0000062c    00000018     spi_controller_command_data_control.o (.text.DL_SPI_isBusy)
                  00000644    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  0000065c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00000674    00000016     spi_controller_command_data_control.o (.text.DL_SPI_transmitData16)
                  0000068a    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000006a0    00000014     spi_controller_command_data_control.o (.text.DL_GPIO_clearPins)
                  000006b4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000006c8    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  000006da    00000002     --HOLE-- [fill = 0]
                  000006dc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000006ec    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000006f6    00000006     libc.a : exit.c.obj (.text:abort)
                  000006fc    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000700    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000704    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000708    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000070c    00000004     --HOLE-- [fill = 0]

.cinit     0    00000720    00000018     
                  00000720    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000728    00000004     (__TI_handler_table)
                  0000072c    00000008     (__TI_cinit_table)
                  00000734    00000004     --HOLE-- [fill = 0]

.rodata    0    00000710    00000010     
                  00000710    0000000a     ti_msp_dl_config.o (.rodata.gSPI_0_config)
                  0000071a    00000002     ti_msp_dl_config.o (.rodata.gSPI_0_clockConfig)
                  0000071c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_0Backup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                                  code   ro data   rw data
       ------                                  ----   -------   -------
    .\
       ti_msp_dl_config.o                      730    12        40     
       spi_controller_command_data_control.o   574    0         0      
       startup_mspm0g350x_ticlang.o            8      192       0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1312   204       40     
                                                                       
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_spi.o                                86     0         0      
       dl_common.o                             10     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  96     0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                          60     0         0      
       boot_cortex_m.c.obj                     40     0         0      
       copy_zero_init.c.obj                    22     0         0      
       exit.c.obj                              6      0         0      
       pre_init.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  132    0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  4      0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj                    64     0         0      
       aeabi_div0.c.obj                        2      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  66     0         0      
                                                                       
       Stack:                                  0      0         512    
       Linker Generated:                       0      20        0      
    +--+---------------------------------------+------+---------+---------+
       Grand Total:                            1610   224       552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000072c records: 1, size/record: 8, table size: 8
	.bss: load addr=00000720, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000728 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
000006fd  ADC0_IRQHandler               
000006fd  ADC1_IRQHandler               
000006fd  AES_IRQHandler                
00000700  C$$EXIT                       
000006fd  CANFD0_IRQHandler             
000006fd  DAC0_IRQHandler               
000006ed  DL_Common_delayCycles         
000002ed  DL_SPI_init                   
000006c9  DL_SPI_setClockConfig         
000006fd  DMA_IRQHandler                
000006fd  Default_Handler               
000006fd  GROUP0_IRQHandler             
000006fd  GROUP1_IRQHandler             
00000701  HOSTexit                      
000006fd  HardFault_Handler             
000006fd  I2C0_IRQHandler               
000006fd  I2C1_IRQHandler               
000006fd  NMI_Handler                   
000006fd  PendSV_Handler                
000006fd  RTC_IRQHandler                
00000705  Reset_Handler                 
000006fd  SPI0_IRQHandler               
000006fd  SPI1_IRQHandler               
000006fd  SVC_Handler                   
000002a5  SYSCFG_DL_GPIO_init           
00000375  SYSCFG_DL_SPI_0_init          
00000515  SYSCFG_DL_SYSCTL_init         
000004f5  SYSCFG_DL_init                
00000331  SYSCFG_DL_initPower           
000006fd  SysTick_Handler               
000006fd  TIMA0_IRQHandler              
000006fd  TIMA1_IRQHandler              
000006fd  TIMG0_IRQHandler              
000006fd  TIMG12_IRQHandler             
000006fd  TIMG6_IRQHandler              
000006fd  TIMG7_IRQHandler              
000006fd  TIMG8_IRQHandler              
000006fd  UART0_IRQHandler              
000006fd  UART1_IRQHandler              
000006fd  UART2_IRQHandler              
000006fd  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
0000072c  __TI_CINIT_Base               
00000734  __TI_CINIT_Limit              
00000734  __TI_CINIT_Warm               
00000728  __TI_Handler_Table_Base       
0000072c  __TI_Handler_Table_Limit      
000003f5  __TI_auto_init_nobinit_nopinit
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
0000068b  __TI_zero_init_nomemset       
00000533  __aeabi_idiv0                 
000003b5  __aeabi_uidiv                 
000003b5  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000489  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000709  _system_pre_init              
000006f7  abort                         
ffffffff  binit                         
00000219  delay_ms_safe                 
20200000  gSPI_0Backup                  
00000000  interruptVectors              
00000181  main                          
000000c1  send_dac_params               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  send_dac_params               
00000181  main                          
00000200  __STACK_SIZE                  
00000219  delay_ms_safe                 
000002a5  SYSCFG_DL_GPIO_init           
000002ed  DL_SPI_init                   
00000331  SYSCFG_DL_initPower           
00000375  SYSCFG_DL_SPI_0_init          
000003b5  __aeabi_uidiv                 
000003b5  __aeabi_uidivmod              
000003f5  __TI_auto_init_nobinit_nopinit
00000489  _c_int00_noargs               
000004f5  SYSCFG_DL_init                
00000515  SYSCFG_DL_SYSCTL_init         
00000533  __aeabi_idiv0                 
0000068b  __TI_zero_init_nomemset       
000006c9  DL_SPI_setClockConfig         
000006ed  DL_Common_delayCycles         
000006f7  abort                         
000006fd  ADC0_IRQHandler               
000006fd  ADC1_IRQHandler               
000006fd  AES_IRQHandler                
000006fd  CANFD0_IRQHandler             
000006fd  DAC0_IRQHandler               
000006fd  DMA_IRQHandler                
000006fd  Default_Handler               
000006fd  GROUP0_IRQHandler             
000006fd  GROUP1_IRQHandler             
000006fd  HardFault_Handler             
000006fd  I2C0_IRQHandler               
000006fd  I2C1_IRQHandler               
000006fd  NMI_Handler                   
000006fd  PendSV_Handler                
000006fd  RTC_IRQHandler                
000006fd  SPI0_IRQHandler               
000006fd  SPI1_IRQHandler               
000006fd  SVC_Handler                   
000006fd  SysTick_Handler               
000006fd  TIMA0_IRQHandler              
000006fd  TIMA1_IRQHandler              
000006fd  TIMG0_IRQHandler              
000006fd  TIMG12_IRQHandler             
000006fd  TIMG6_IRQHandler              
000006fd  TIMG7_IRQHandler              
000006fd  TIMG8_IRQHandler              
000006fd  UART0_IRQHandler              
000006fd  UART1_IRQHandler              
000006fd  UART2_IRQHandler              
000006fd  UART3_IRQHandler              
00000700  C$$EXIT                       
00000701  HOSTexit                      
00000705  Reset_Handler                 
00000709  _system_pre_init              
00000728  __TI_Handler_Table_Base       
0000072c  __TI_CINIT_Base               
0000072c  __TI_Handler_Table_Limit      
00000734  __TI_CINIT_Limit              
00000734  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gSPI_0Backup                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[83 symbols]
