/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4150"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const SPI    = scripting.addModule("/ti/driverlib/SPI", {}, false);
const SPI1   = SPI.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                          = "GPIO_LEDS";
GPIO1.associatedPins[0].$name        = "USER_LED_1";
GPIO1.associatedPins[0].initialValue = "SET";
GPIO1.associatedPins[0].pin.$assign  = "PA0";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

SPI1.$name                      = "SPI_0";
SPI1.dataSize                   = 16;
SPI1.targetBitRate              = 100000;
SPI1.direction                  = "PICO";
SPI1.sclkPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
SPI1.mosiPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";
SPI1.cs0PinConfig.$name         = "ti_driverlib_gpio_GPIOPinGeneric3";
SPI1.peripheral.$assign         = "SPI1";
SPI1.peripheral.sclkPin.$assign = "PB9";
SPI1.peripheral.mosiPin.$assign = "PB8";
SPI1.peripheral.cs0Pin.$assign  = "PB6";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
