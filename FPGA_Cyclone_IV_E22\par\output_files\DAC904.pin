 -- Copyright (C) 1991-2013 Altera Corporation
 -- Your use of Altera Corporation's design tools, logic functions 
 -- and other software and tools, and its AMPP partner logic 
 -- functions, and any output files from any of the foregoing 
 -- (including device programming or simulation files), and any 
 -- associated documentation or information are expressly subject 
 -- to the terms and conditions of the Altera Program License 
 -- Subscription Agreement, Altera MegaCore Function License 
 -- Agreement, or other applicable license agreement, including, 
 -- without limitation, that your use is for the sole purpose of 
 -- programming logic devices manufactured by Altera and sold by 
 -- Altera or its authorized distributors.  Please refer to the 
 -- applicable agreement for further details.
 -- 
 -- This is a Quartus II output file. It is for reporting purposes only, and is
 -- not intended for use as a Quartus II input file. This file cannot be used
 -- to make Quartus II pin assignments - for instructions on how to make pin
 -- assignments, please see Quartus II help.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- NC            : No Connect. This pin has no internal connection to the device.
 -- DNU           : Do Not Use. This pin MUST NOT be connected.
 -- VCCINT        : Dedicated power pin, which MUST be connected to VCC  (1.2V).
 -- VCCIO         : Dedicated power pin, which MUST be connected to VCC
 --                 of its bank.
 --					Bank 1:		2.5V
 --					Bank 2:		2.5V
 --					Bank 3:		2.5V
 --					Bank 4:		2.5V
 --					Bank 5:		2.5V
 --					Bank 6:		2.5V
 --					Bank 7:		2.5V
 --					Bank 8:		2.5V
 -- GND           : Dedicated ground pin. Dedicated GND pins MUST be connected to GND.
 --					It can also be used to report unused dedicated pins. The connection
 --					on the board for unused dedicated pins depends on whether this will
 --					be used in a future design. One example is device migration. When
 --					using device migration, refer to the device pin-tables. If it is a
 --					GND pin in the pin table or if it will not be used in a future design
 --					for another purpose the it MUST be connected to GND. If it is an unused
 --					dedicated pin, then it can be connected to a valid signal on the board
 --					(low, high, or toggling) if that signal is required for a different
 --					revision of the design.
 -- GND+          : Unused input pin. It can also be used to report unused dual-purpose pins.
 --					This pin should be connected to GND. It may also be connected  to a
 --					valid signal  on the board  (low, high, or toggling)  if that signal
 --					is required for a different revision of the design.
 -- GND*          : Unused  I/O  pin. Connect each pin marked GND* directly to GND
 --           	    or leave it unconnected.
 -- RESERVED      : Unused I/O pin, which MUST be left unconnected.
 -- RESERVED_INPUT    : Pin is tri-stated and should be connected to the board.
 -- RESERVED_INPUT_WITH_WEAK_PULLUP    : Pin is tri-stated with internal weak pull-up resistor.
 -- RESERVED_INPUT_WITH_BUS_HOLD       : Pin is tri-stated with bus-hold circuitry.
 -- RESERVED_OUTPUT_DRIVEN_HIGH        : Pin is output driven high.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- Pin directions (input, output or bidir) are based on device operating in user mode.
 ---------------------------------------------------------------------------------

Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
CHIP  "DAC904"  ASSIGNED TO AN: EP4CE6E22C8

Pin Name/Usage               : Location  : Dir.   : I/O Standard      : Voltage : I/O Bank  : User Assignment
-------------------------------------------------------------------------------------------------------------
RESERVED_INPUT_WITH_WEAK_PULLUP : 1         :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 2         :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 3         :        :                   :         : 1         :                
GND                          : 4         : gnd    :                   :         :           :                
VCCINT                       : 5         : power  :                   : 1.2V    :           :                
~ALTERA_ASDO_DATA1~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 6         : input  : 2.5 V             :         : 1         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 7         :        :                   :         : 1         :                
~ALTERA_FLASH_nCE_nCSO~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 8         : input  : 2.5 V             :         : 1         : N              
nSTATUS                      : 9         :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 10        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 11        :        :                   :         : 1         :                
~ALTERA_DCLK~                : 12        : output : 2.5 V             :         : 1         : N              
~ALTERA_DATA0~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 13        : input  : 2.5 V             :         : 1         : N              
nCONFIG                      : 14        :        :                   :         : 1         :                
TDI                          : 15        : input  :                   :         : 1         :                
TCK                          : 16        : input  :                   :         : 1         :                
VCCIO1                       : 17        : power  :                   : 2.5V    : 1         :                
TMS                          : 18        : input  :                   :         : 1         :                
GND                          : 19        : gnd    :                   :         :           :                
TDO                          : 20        : output :                   :         : 1         :                
nCE                          : 21        :        :                   :         : 1         :                
GND                          : 22        : gnd    :                   :         :           :                
spi_sclk                     : 23        : input  : 2.5 V             :         : 1         : Y              
SYS_RST                      : 24        : input  : 2.5 V             :         : 2         : Y              
spi_mosi                     : 25        : input  : 2.5 V             :         : 2         : Y              
VCCIO2                       : 26        : power  :                   : 2.5V    : 2         :                
GND                          : 27        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 28        :        :                   :         : 2         :                
VCCINT                       : 29        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 30        :        :                   :         : 2         :                
spi_cs_n                     : 31        : input  : 2.5 V             :         : 2         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : 32        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 33        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 34        :        :                   :         : 2         :                
VCCA1                        : 35        : power  :                   : 2.5V    :           :                
GNDA1                        : 36        : gnd    :                   :         :           :                
VCCD_PLL1                    : 37        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 38        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 39        :        :                   :         : 3         :                
VCCIO3                       : 40        : power  :                   : 2.5V    : 3         :                
GND                          : 41        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 42        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 43        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 44        :        :                   :         : 3         :                
VCCINT                       : 45        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 46        :        :                   :         : 3         :                
VCCIO3                       : 47        : power  :                   : 2.5V    : 3         :                
GND                          : 48        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 49        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 50        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 51        :        :                   :         : 3         :                
debug_amplitude[3]           : 52        : output : 2.5 V             :         : 3         : N              
debug_amplitude[8]           : 53        : output : 2.5 V             :         : 3         : N              
debug_amplitude[2]           : 54        : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 55        :        :                   :         : 4         :                
VCCIO4                       : 56        : power  :                   : 2.5V    : 4         :                
GND                          : 57        : gnd    :                   :         :           :                
debug_amplitude[11]          : 58        : output : 2.5 V             :         : 4         : N              
debug_amplitude[0]           : 59        : output : 2.5 V             :         : 4         : N              
debug_amplitude[7]           : 60        : output : 2.5 V             :         : 4         : N              
VCCINT                       : 61        : power  :                   : 1.2V    :           :                
VCCIO4                       : 62        : power  :                   : 2.5V    : 4         :                
GND                          : 63        : gnd    :                   :         :           :                
debug_amplitude[1]           : 64        : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 65        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 66        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 67        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 68        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 69        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 70        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 71        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 72        :        :                   :         : 4         :                
DAC_DATA[0]                  : 73        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[1]                  : 74        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[2]                  : 75        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[3]                  : 76        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[4]                  : 77        : output : 2.5 V             :         : 5         : Y              
VCCINT                       : 78        : power  :                   : 1.2V    :           :                
GND                          : 79        : gnd    :                   :         :           :                
DAC_DATA[5]                  : 80        : output : 2.5 V             :         : 5         : Y              
VCCIO5                       : 81        : power  :                   : 2.5V    : 5         :                
GND                          : 82        : gnd    :                   :         :           :                
DAC_DATA[6]                  : 83        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[7]                  : 84        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[8]                  : 85        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[9]                  : 86        : output : 2.5 V             :         : 5         : Y              
DAC_DATA[10]                 : 87        : output : 2.5 V             :         : 5         : Y              
SYS_CLK                      : 88        : input  : 2.5 V             :         : 5         : Y              
KEY_IN[1]                    : 89        : input  : 2.5 V             :         : 5         : Y              
KEY_IN[0]                    : 90        : input  : 2.5 V             :         : 6         : Y              
KEY_IN[2]                    : 91        : input  : 2.5 V             :         : 6         : Y              
CONF_DONE                    : 92        :        :                   :         : 6         :                
VCCIO6                       : 93        : power  :                   : 2.5V    : 6         :                
MSEL0                        : 94        :        :                   :         : 6         :                
GND                          : 95        : gnd    :                   :         :           :                
MSEL1                        : 96        :        :                   :         : 6         :                
MSEL2                        : 97        :        :                   :         : 6         :                
DAC_DATA[11]                 : 98        : output : 2.5 V             :         : 6         : Y              
DAC_DATA[12]                 : 99        : output : 2.5 V             :         : 6         : Y              
DAC_DATA[13]                 : 100       : output : 2.5 V             :         : 6         : Y              
DAC_CLK                      : 101       : output : 2.5 V             :         : 6         : Y              
VCCINT                       : 102       : power  :                   : 1.2V    :           :                
PD                           : 103       : output : 2.5 V             :         : 6         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : 104       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 105       :        :                   :         : 6         :                
debug_freq_sel[0]            : 106       : output : 2.5 V             :         : 6         : N              
VCCA2                        : 107       : power  :                   : 2.5V    :           :                
GNDA2                        : 108       : gnd    :                   :         :           :                
VCCD_PLL2                    : 109       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 110       :        :                   :         : 7         :                
debug_freq_sel[3]            : 111       : output : 2.5 V             :         : 7         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 112       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 113       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 114       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 115       :        :                   :         : 7         :                
VCCINT                       : 116       : power  :                   : 1.2V    :           :                
VCCIO7                       : 117       : power  :                   : 2.5V    : 7         :                
GND                          : 118       : gnd    :                   :         :           :                
debug_freq_sel[2]            : 119       : output : 2.5 V             :         : 7         : N              
debug_amplitude[10]          : 120       : output : 2.5 V             :         : 7         : N              
debug_freq_sel[1]            : 121       : output : 2.5 V             :         : 7         : N              
VCCIO7                       : 122       : power  :                   : 2.5V    : 7         :                
GND                          : 123       : gnd    :                   :         :           :                
debug_amplitude[6]           : 124       : output : 2.5 V             :         : 7         : N              
debug_amplitude[5]           : 125       : output : 2.5 V             :         : 7         : N              
debug_amplitude[4]           : 126       : output : 2.5 V             :         : 7         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 127       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 128       :        :                   :         : 8         :                
debug_amplitude[9]           : 129       : output : 2.5 V             :         : 8         : N              
VCCIO8                       : 130       : power  :                   : 2.5V    : 8         :                
GND                          : 131       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 132       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 133       :        :                   :         : 8         :                
VCCINT                       : 134       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 135       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 136       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 137       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 138       :        :                   :         : 8         :                
VCCIO8                       : 139       : power  :                   : 2.5V    : 8         :                
GND                          : 140       : gnd    :                   :         :           :                
debug_spi_valid              : 141       : output : 2.5 V             :         : 8         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 142       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 143       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 144       :        :                   :         : 8         :                
GND                          : EPAD      :        :                   :         :           :                
