v1
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,lpm_mult:Mult0|mult_9dt:auto_generated|mac_out2,
DSP_BALANCING_IMPLEMENTATION,DSP_BLOCKS,lpm_mult:Mult0|mult_9dt:auto_generated|mac_out4,
PORT_SWAPPING,PORT_SWAP_TYPE_DSPMULT,lpm_mult:Mult0|mult_9dt:auto_generated|mac_mult3,
IO_RULES,NUM_PINS_NOT_EXCEED_LOC_AVAILABLE,PASS,IO_000001,Capacity Checks,Number of pins in an I/O bank should not exceed the number of locations available.,Critical,0 such failures found.,,I/O,,
IO_RULES,NUM_CLKS_NOT_EXCEED_CLKS_AVAILABLE,INAPPLICABLE,IO_000002,Capacity Checks,Number of clocks in an I/O bank should not exceed the number of clocks available.,Critical,No Global Signal assignments found.,,I/O,,
IO_RULES,NUM_VREF_NOT_EXCEED_LOC_AVAILABLE,PASS,IO_000003,Capacity Checks,Number of pins in a Vrefgroup should not exceed the number of locations available.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_BANK_SUPPORT_VCCIO,INAPPLICABLE,IO_000004,Voltage Compatibility Checks,The I/O bank should support the requested VCCIO.,Critical,No IOBANK_VCCIO assignments found.,,I/O,,
IO_RULES,IO_BANK_NOT_HAVE_COMPETING_VREF,INAPPLICABLE,IO_000005,Voltage Compatibility Checks,The I/O bank should not have competing VREF values.,Critical,No VREF I/O Standard assignments found.,,I/O,,
IO_RULES,IO_BANK_NOT_HAVE_COMPETING_VCCIO,PASS,IO_000006,Voltage Compatibility Checks,The I/O bank should not have competing VCCIO values.,Critical,0 such failures found.,,I/O,,
IO_RULES,CHECK_UNAVAILABLE_LOC,PASS,IO_000007,Valid Location Checks,Checks for unavailable locations.,Critical,0 such failures found.,,I/O,,
IO_RULES,CHECK_RESERVED_LOC,INAPPLICABLE,IO_000008,Valid Location Checks,Checks for reserved locations.,Critical,No reserved LogicLock region found.,,I/O,,
IO_RULES,LOC_SUPPORT_IO_STD,PASS,IO_000009,I/O Properties Checks for One I/O,The location should support the requested I/O standard.,Critical,0 such failures found.,,I/O,,
IO_RULES,LOC_SUPPORT_IO_DIR,PASS,IO_000010,I/O Properties Checks for One I/O,The location should support the requested I/O direction.,Critical,0 such failures found.,,I/O,,
IO_RULES,LOC_SUPPORT_CURRENT_STRENGTH,INAPPLICABLE,IO_000011,I/O Properties Checks for One I/O,The location should support the requested Current Strength.,Critical,No Current Strength assignments found.,,I/O,,
IO_RULES,LOC_SUPPORT_OCT_VALUE,PASS,IO_000012,I/O Properties Checks for One I/O,The location should support the requested On Chip Termination value.,Critical,0 such failures found.,,I/O,,
IO_RULES,LOC_SUPPORT_BUS_HOLD_VALUE,INAPPLICABLE,IO_000013,I/O Properties Checks for One I/O,The location should support the requested Bus Hold value.,Critical,No Enable Bus-Hold Circuitry assignments found.,,I/O,,
IO_RULES,LOC_SUPPORT_WEAK_PULL_UP_VALUE,INAPPLICABLE,IO_000014,I/O Properties Checks for One I/O,The location should support the requested Weak Pull Up value.,Critical,No Weak Pull-Up Resistor assignments found.,,I/O,,
IO_RULES,LOC_SUPPORT_PCI_CLAMP_DIODE,PASS,IO_000015,I/O Properties Checks for One I/O,The location should support the requested PCI Clamp Diode.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_CURRENT_STRENGTH,INAPPLICABLE,IO_000018,I/O Properties Checks for One I/O,The I/O standard should support the requested Current Strength.,Critical,No Current Strength assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_OCT_VALUE,PASS,IO_000019,I/O Properties Checks for One I/O,The I/O standard should support the requested On Chip Termination value.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_PCI_CLAMP_DIODE,PASS,IO_000020,I/O Properties Checks for One I/O,The I/O standard should support the requested PCI Clamp Diode.,Critical,0 such failures found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_WEAK_PULL_UP_VALUE,INAPPLICABLE,IO_000021,I/O Properties Checks for One I/O,The I/O standard should support the requested Weak Pull Up value.,Critical,No Weak Pull-Up Resistor assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_BUS_HOLD_VALUE,INAPPLICABLE,IO_000022,I/O Properties Checks for One I/O,The I/O standard should support the requested Bus Hold value.,Critical,No Enable Bus-Hold Circuitry assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORT_OPEN_DRAIN_VALUE,INAPPLICABLE,IO_000023,I/O Properties Checks for One I/O,The I/O standard should support the Open Drain value.,Critical,No open drain assignments found.,,I/O,,
IO_RULES,IO_DIR_SUPPORT_OCT_VALUE,PASS,IO_000024,I/O Properties Checks for One I/O,The I/O direction should support the On Chip Termination value.,Critical,0 such failures found.,,I/O,,
IO_RULES,OCT_AND_CURRENT_STRENGTH_NOT_USED_SIMULTANEOUSLY,INAPPLICABLE,IO_000026,I/O Properties Checks for One I/O,On Chip Termination and Current Strength should not be used at the same time.,Critical,No Current Strength assignments found.,,I/O,,
IO_RULES,WEAK_PULL_UP_AND_BUS_HOLD_NOT_USED_SIMULTANEOUSLY,INAPPLICABLE,IO_000027,I/O Properties Checks for One I/O,Weak Pull Up and Bus Hold should not be used at the same time.,Critical,No Enable Bus-Hold Circuitry or Weak Pull-Up Resistor assignments found.,,I/O,,
IO_RULES,IO_STD_SUPPORTS_SLEW_RATE,INAPPLICABLE,IO_000045,I/O Properties Checks for One I/O,The I/O standard should support the requested Slew Rate value.,Critical,No Slew Rate assignments found.,,I/O,,
IO_RULES,LOC_SUPPORTS_SLEW_RATE,INAPPLICABLE,IO_000046,I/O Properties Checks for One I/O,The location should support the requested Slew Rate value.,Critical,No Slew Rate assignments found.,,I/O,,
IO_RULES,OCT_SUPPORTS_SLEW_RATE,INAPPLICABLE,IO_000047,I/O Properties Checks for One I/O,On Chip Termination and Slew Rate should not be used at the same time.,Critical,No Slew Rate assignments found.,,I/O,,
IO_RULES,CURRENT_DENSITY_FOR_CONSECUTIVE_IO_NOT_EXCEED_CURRENT_VALUE,PASS,IO_000033,Electromigration Checks,Current density for consecutive I/Os should not exceed 240mA for row I/Os and 240mA for column I/Os.,Critical,0 such failures found.,,I/O,,
IO_RULES,SINGLE_ENDED_OUTPUTS_LAB_ROWS_FROM_DIFF_IO,INAPPLICABLE,IO_000034,SI Related Distance Checks,Single-ended outputs should be 5 LAB row(s) away from a differential I/O.,High,No Differential I/O Standard assignments found.,,I/O,,
IO_RULES,MAX_20_OUTPUTS_ALLOWED_IN_VREFGROUP,INAPPLICABLE,IO_000042,SI Related SSO Limit Checks,No more than 20 outputs are allowed in a VREF group when VREF is being read from.,High,No VREF I/O Standard assignments found.,,I/O,,
IO_RULES,DEV_IO_RULE_OCT_DISCLAIMER,,,,,,,,,,
IO_RULES_MATRIX,Pin/Rules,IO_000001;IO_000002;IO_000003;IO_000004;IO_000005;IO_000006;IO_000007;IO_000008;IO_000009;IO_000010;IO_000011;IO_000012;IO_000013;IO_000014;IO_000015;IO_000018;IO_000019;IO_000020;IO_000021;IO_000022;IO_000023;IO_000024;IO_000026;IO_000027;IO_000045;IO_000046;IO_000047;IO_000033;IO_000034;IO_000042,
IO_RULES_MATRIX,Total Pass,24;0;24;0;0;41;24;0;41;41;0;33;0;0;8;0;33;8;0;0;0;33;0;0;0;0;0;41;0;0,
IO_RULES_MATRIX,Total Unchecked,0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0,
IO_RULES_MATRIX,Total Inapplicable,17;41;17;41;41;0;17;41;0;0;41;8;41;41;33;41;8;33;41;41;41;8;41;41;41;41;41;0;41;41,
IO_RULES_MATRIX,Total Fail,0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0,
IO_RULES_MATRIX,KEY_IN[0],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,KEY_IN[1],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,KEY_IN[2],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,PD,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_CLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[0],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[1],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[2],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[3],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[4],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[5],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[6],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[7],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[8],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[9],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[10],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[11],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[12],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,DAC_DATA[13],Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_spi_valid,Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_freq_sel[0],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_freq_sel[1],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_freq_sel[2],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_freq_sel[3],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[0],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[1],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[2],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[3],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[4],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[5],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[6],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[7],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[8],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[9],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[10],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,debug_amplitude[11],Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,SYS_RST,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,SYS_CLK,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,spi_cs_n,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,spi_sclk,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_MATRIX,spi_mosi,Pass;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Pass;Inapplicable;Pass;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Inapplicable;Pass;Inapplicable;Inapplicable,
IO_RULES_SUMMARY,Total I/O Rules,30,
IO_RULES_SUMMARY,Number of I/O Rules Passed,12,
IO_RULES_SUMMARY,Number of I/O Rules Failed,0,
IO_RULES_SUMMARY,Number of I/O Rules Unchecked,0,
IO_RULES_SUMMARY,Number of I/O Rules Inapplicable,18,
