{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1754015480343 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus II 64-Bit " "Running Quartus II 64-Bit Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1754015480343 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 10:31:20 2025 " "Processing started: Fri Aug 01 10:31:20 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1754015480343 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Assembler" 0 -1 1754015480343 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_asm --read_settings_files=off --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Assembler" 0 -1 1754015480343 ""}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Writing out detailed assembly data for power analysis" {  } {  } 0 115031 "Writing out detailed assembly data for power analysis" 0 0 "Assembler" 0 -1 1754015480620 ""}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Assembler is generating device programming files" {  } {  } 0 115030 "Assembler is generating device programming files" 0 0 "Assembler" 0 -1 1754015480628 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 0 s Quartus II 64-Bit " "Quartus II 64-Bit Assembler was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4593 " "Peak virtual memory: 4593 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1754015480735 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 10:31:20 2025 " "Processing ended: Fri Aug 01 10:31:20 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1754015480735 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1754015480735 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1754015480735 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Assembler" 0 -1 1754015480735 ""}
