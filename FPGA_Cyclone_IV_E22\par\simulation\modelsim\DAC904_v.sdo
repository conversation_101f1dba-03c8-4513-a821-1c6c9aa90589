// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 85 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 03:21:18")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1301:1301:1301) (1323:1323:1323))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2145:2145:2145) (1949:1949:1949))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2110:2110:2110) (1957:1957:1957))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2229:2229:2229) (1962:1962:1962))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2389:2389:2389) (2085:2085:2085))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1839:1839:1839) (1671:1671:1671))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1393:1393:1393) (1258:1258:1258))
        (IOPATH i o (4518:4518:4518) (4585:4585:4585))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1696:1696:1696) (1511:1511:1511))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1749:1749:1749) (1550:1550:1550))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2030:2030:2030) (1796:1796:1796))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1187:1187:1187) (1124:1124:1124))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1802:1802:1802) (1636:1636:1636))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1675:1675:1675) (1488:1488:1488))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2021:2021:2021) (1786:1786:1786))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1525:1525:1525) (1712:1712:1712))
        (IOPATH i o (3029:3029:3029) (3068:3068:3068))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1424:1424:1424) (1518:1518:1518))
        (IOPATH i o (3135:3135:3135) (3158:3158:3158))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1440:1440:1440) (1352:1352:1352))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1194:1194:1194) (1154:1154:1154))
        (IOPATH i o (4611:4611:4611) (4659:4659:4659))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_freq_sel\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1844:1844:1844) (1707:1707:1707))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1822:1822:1822) (1685:1685:1685))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1857:1857:1857) (1726:1726:1726))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1894:1894:1894) (1787:1787:1787))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1570:1570:1570) (1520:1520:1520))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (3276:3276:3276) (3178:3178:3178))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2201:2201:2201) (2048:2048:2048))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2229:2229:2229) (2099:2099:2099))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1807:1807:1807) (1692:1692:1692))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2938:2938:2938) (2856:2856:2856))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2595:2595:2595) (2383:2383:2383))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2072:2072:2072) (2243:2243:2243))
        (IOPATH i o (3105:3105:3105) (3157:3157:3157))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE debug_amplitude\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1512:1512:1512) (1588:1588:1588))
        (IOPATH i o (3105:3105:3105) (3157:3157:3157))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (222:222:222) (208:208:208))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1313:1313:1313) (1313:1313:1313))
        (PORT inclk[0] (2313:2313:2313) (2313:2313:2313))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2338:2338:2338) (2305:2305:2305))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE spi_sclk\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (222:222:222) (208:208:208))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1069:1069:1069) (1157:1157:1157))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (555:555:555) (573:573:573))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5165:5165:5165) (4959:4959:4959))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT asdata (770:770:770) (845:845:845))
        (PORT ena (5165:5165:5165) (4959:4959:4959))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (304:304:304) (377:377:377))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5165:5165:5165) (4959:4959:4959))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (304:304:304) (377:377:377))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5165:5165:5165) (4959:4959:4959))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (510:510:510) (536:536:536))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT asdata (984:984:984) (998:998:998))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT asdata (787:787:787) (856:856:856))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (322:322:322) (393:393:393))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (323:323:323) (394:394:394))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[10\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datac (322:322:322) (400:400:400))
        (IOPATH datac combout (324:324:324) (315:315:315))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (322:322:322) (393:393:393))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (324:324:324) (395:395:395))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[13\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datad (326:326:326) (397:397:397))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_cs_n_d1\~0)
    (DELAY
      (ABSOLUTE
        (PORT datac (4154:4154:4154) (4325:4325:4325))
        (IOPATH datac combout (324:324:324) (315:315:315))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d1)
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1611:1611:1611))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_cs_n_d2)
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT asdata (986:986:986) (1008:1008:1008))
        (PORT clrn (1664:1664:1664) (1611:1611:1611))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (561:561:561) (596:596:596))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH datac combout (462:462:462) (482:482:482))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT asdata (2052:2052:2052) (1943:1943:1943))
        (PORT clrn (1668:1668:1668) (1618:1618:1618))
        (PORT ena (2308:2308:2308) (2153:2153:2153))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT asdata (787:787:787) (856:856:856))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT asdata (2088:2088:2088) (1985:1985:1985))
        (PORT clrn (1668:1668:1668) (1618:1618:1618))
        (PORT ena (2308:2308:2308) (2153:2153:2153))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_shift_reg\[15\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (323:323:323) (393:393:393))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_shift_reg\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1596:1596:1596) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (5120:5120:5120) (4924:4924:4924))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT asdata (1943:1943:1943) (1882:1882:1882))
        (PORT clrn (1668:1668:1668) (1618:1618:1618))
        (PORT ena (2308:2308:2308) (2153:2153:2153))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT asdata (2029:2029:2029) (1932:1932:1932))
        (PORT clrn (1668:1668:1668) (1618:1618:1618))
        (PORT ena (2308:2308:2308) (2153:2153:2153))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (482:482:482) (655:655:655))
        (PORT datab (476:476:476) (634:634:634))
        (PORT datac (406:406:406) (535:535:535))
        (PORT datad (649:649:649) (700:700:700))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (472:472:472) (462:462:462))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (483:483:483) (656:656:656))
        (PORT datab (475:475:475) (633:633:633))
        (PORT datac (406:406:406) (534:534:534))
        (PORT datad (650:650:650) (701:701:701))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1013:1013:1013) (1036:1036:1036))
        (PORT datab (1016:1016:1016) (1023:1023:1023))
        (PORT datac (936:936:936) (949:949:949))
        (PORT datad (1611:1611:1611) (1552:1552:1552))
        (IOPATH dataa combout (453:453:453) (418:418:418))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (483:483:483) (657:657:657))
        (PORT datab (474:474:474) (632:632:632))
        (PORT datac (405:405:405) (534:534:534))
        (PORT datad (651:651:651) (702:702:702))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (486:486:486) (660:660:660))
        (PORT datab (469:469:469) (625:625:625))
        (PORT datac (403:403:403) (531:531:531))
        (PORT datad (655:655:655) (706:706:706))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (484:484:484) (658:658:658))
        (PORT datab (472:472:472) (629:629:629))
        (PORT datac (405:405:405) (533:533:533))
        (PORT datad (653:653:653) (704:704:704))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (479:479:479) (650:650:650))
        (PORT datab (485:485:485) (646:646:646))
        (PORT datac (411:411:411) (540:540:540))
        (PORT datad (643:643:643) (694:694:694))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (472:472:472) (462:462:462))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (481:481:481) (653:653:653))
        (PORT datab (480:480:480) (639:639:639))
        (PORT datac (408:408:408) (537:537:537))
        (PORT datad (647:647:647) (698:698:698))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (479:479:479) (651:651:651))
        (PORT datab (482:482:482) (643:643:643))
        (PORT datac (410:410:410) (539:539:539))
        (PORT datad (645:645:645) (695:695:695))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (478:478:478) (649:649:649))
        (PORT datab (485:485:485) (647:647:647))
        (PORT datac (411:411:411) (541:541:541))
        (PORT datad (642:642:642) (693:693:693))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (472:472:472) (462:462:462))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (485:485:485) (659:659:659))
        (PORT datab (470:470:470) (628:628:628))
        (PORT datac (404:404:404) (532:532:532))
        (PORT datad (653:653:653) (704:704:704))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (479:479:479) (651:651:651))
        (PORT datab (483:483:483) (644:644:644))
        (PORT datac (410:410:410) (539:539:539))
        (PORT datad (644:644:644) (694:694:694))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (440:440:440) (462:462:462))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (481:481:481) (654:654:654))
        (PORT datab (478:478:478) (638:638:638))
        (PORT datac (408:408:408) (536:536:536))
        (PORT datad (648:648:648) (698:698:698))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (485:485:485) (659:659:659))
        (PORT datab (594:594:594) (642:642:642))
        (PORT datad (654:654:654) (705:705:705))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (692:692:692) (757:757:757))
        (PORT datab (597:597:597) (645:645:645))
        (PORT datad (436:436:436) (585:585:585))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (455:455:455) (436:436:436))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (480:480:480) (652:652:652))
        (PORT datab (597:597:597) (645:645:645))
        (PORT datad (437:437:437) (587:587:587))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (435:435:435) (424:424:424))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (483:483:483) (657:657:657))
        (PORT datab (473:473:473) (631:631:631))
        (PORT datad (651:651:651) (703:703:703))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (437:437:437) (436:436:436))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (759:759:759) (698:698:698))
        (PORT datab (331:331:331) (406:406:406))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (601:601:601) (610:610:610))
        (PORT datab (549:549:549) (516:516:516))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (800:800:800) (725:725:725))
        (PORT datab (331:331:331) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (762:762:762) (706:706:706))
        (PORT datab (547:547:547) (565:565:565))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (539:539:539) (575:575:575))
        (PORT datab (778:778:778) (706:706:706))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (548:548:548) (516:516:516))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (740:740:740) (665:665:665))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (761:761:761) (704:704:704))
        (PORT datab (333:333:333) (409:409:409))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (545:545:545) (574:574:574))
        (PORT datab (501:501:501) (490:490:490))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (494:494:494) (487:487:487))
        (PORT datab (333:333:333) (409:409:409))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (848:848:848) (819:819:819))
        (PORT datab (548:548:548) (513:513:513))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (495:495:495) (490:490:490))
        (PORT datab (533:533:533) (567:567:567))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1562:1562:1562) (1479:1479:1479))
        (PORT datab (500:500:500) (490:490:490))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (544:544:544) (507:507:507))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (495:495:495) (488:488:488))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (334:334:334) (417:417:417))
        (PORT datab (545:545:545) (505:505:505))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1638:1638:1638))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2268:2268:2268) (2216:2216:2216))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (795:795:795) (714:714:714))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (880:880:880) (838:838:838))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1129:1129:1129) (1063:1063:1063))
        (PORT datab (551:551:551) (519:519:519))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1204:1204:1204) (1097:1097:1097))
        (PORT datab (554:554:554) (575:575:575))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (910:910:910) (852:852:852))
        (PORT datab (359:359:359) (436:436:436))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (PORT datab (902:902:902) (843:843:843))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (363:363:363) (446:446:446))
        (PORT datab (553:553:553) (520:520:520))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1206:1206:1206) (1099:1099:1099))
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1013:1013:1013) (1035:1035:1035))
        (PORT datab (1015:1015:1015) (1022:1022:1022))
        (PORT datac (936:936:936) (949:949:949))
        (PORT datad (1611:1611:1611) (1552:1552:1552))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (493:493:493) (486:486:486))
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (572:572:572) (604:604:604))
        (PORT datab (888:888:888) (845:845:845))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1012:1012:1012) (1034:1034:1034))
        (PORT datab (1015:1015:1015) (1021:1021:1021))
        (PORT datac (937:937:937) (950:950:950))
        (PORT datad (1611:1611:1611) (1552:1552:1552))
        (IOPATH dataa combout (453:453:453) (413:413:413))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (736:736:736) (683:683:683))
        (PORT datab (385:385:385) (462:462:462))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1208:1208:1208) (1101:1101:1101))
        (PORT datab (560:560:560) (606:606:606))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (913:913:913) (856:856:856))
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1006:1006:1006) (1025:1025:1025))
        (PORT datab (1011:1011:1011) (1016:1016:1016))
        (PORT datac (940:940:940) (954:954:954))
        (PORT datad (1613:1613:1613) (1554:1554:1554))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (PORT datab (794:794:794) (712:712:712))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1011:1011:1011) (1033:1033:1033))
        (PORT datab (1015:1015:1015) (1021:1021:1021))
        (PORT datac (937:937:937) (951:951:951))
        (PORT datad (1611:1611:1611) (1552:1552:1552))
        (IOPATH dataa combout (453:453:453) (413:413:413))
        (IOPATH datab combout (455:455:455) (412:412:412))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (571:571:571) (610:610:610))
        (PORT datab (545:545:545) (508:508:508))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (364:364:364) (448:448:448))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1616:1616:1616) (1657:1657:1657))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (2666:2666:2666) (2580:2580:2580))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1456:1456:1456) (1414:1414:1414))
        (PORT d[1] (1396:1396:1396) (1353:1353:1353))
        (PORT d[2] (1794:1794:1794) (1718:1718:1718))
        (PORT d[3] (1436:1436:1436) (1383:1383:1383))
        (PORT d[4] (1413:1413:1413) (1381:1381:1381))
        (PORT d[5] (1412:1412:1412) (1375:1375:1375))
        (PORT d[6] (1398:1398:1398) (1342:1342:1342))
        (PORT d[7] (2086:2086:2086) (1968:1968:1968))
        (PORT d[8] (1388:1388:1388) (1352:1352:1352))
        (PORT d[9] (1738:1738:1738) (1633:1633:1633))
        (PORT d[10] (2111:2111:2111) (2015:2015:2015))
        (PORT d[11] (1084:1084:1084) (1075:1075:1075))
        (PORT clk (2031:2031:2031) (2090:2090:2090))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2031:2031:2031) (2090:2090:2090))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2032:2032:2032) (2091:2091:2091))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1983:1983:1983) (2043:2043:2043))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (984:984:984))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (985:985:985))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (985:985:985))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (985:985:985))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1635:1635:1635))
        (PORT asdata (1618:1618:1618) (1514:1514:1514))
        (PORT clrn (1665:1665:1665) (1612:1612:1612))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1206:1206:1206) (1089:1089:1089))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1597:1597:1597) (1635:1635:1635))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1665:1665:1665) (1612:1612:1612))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1001:1001:1001) (982:982:982))
        (PORT d[1] (1018:1018:1018) (1007:1007:1007))
        (PORT d[2] (1788:1788:1788) (1713:1713:1713))
        (PORT d[3] (1084:1084:1084) (1063:1063:1063))
        (PORT d[4] (1005:1005:1005) (1004:1004:1004))
        (PORT d[5] (1038:1038:1038) (1029:1029:1029))
        (PORT d[6] (1001:1001:1001) (983:983:983))
        (PORT d[7] (2081:2081:2081) (1962:1962:1962))
        (PORT d[8] (990:990:990) (991:991:991))
        (PORT d[9] (1003:1003:1003) (993:993:993))
        (PORT d[10] (963:963:963) (962:962:962))
        (PORT d[11] (998:998:998) (995:995:995))
        (PORT clk (2031:2031:2031) (2088:2088:2088))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2031:2031:2031) (2088:2088:2088))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2032:2032:2032) (2089:2089:2089))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1983:1983:1983) (2041:2041:2041))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (982:982:982))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (983:983:983))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (983:983:983))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (983:983:983))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1635:1635:1635))
        (PORT asdata (1880:1880:1880) (1754:1754:1754))
        (PORT clrn (1665:1665:1665) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1635:1635:1635))
        (PORT asdata (1895:1895:1895) (1752:1752:1752))
        (PORT clrn (1665:1665:1665) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2477:2477:2477) (2346:2346:2346))
        (PORT d[1] (2399:2399:2399) (2274:2274:2274))
        (PORT d[2] (2419:2419:2419) (2306:2306:2306))
        (PORT d[3] (2003:2003:2003) (1906:1906:1906))
        (PORT d[4] (2085:2085:2085) (2007:2007:2007))
        (PORT d[5] (2483:2483:2483) (2375:2375:2375))
        (PORT d[6] (2076:2076:2076) (1980:1980:1980))
        (PORT d[7] (2091:2091:2091) (2007:2007:2007))
        (PORT d[8] (2024:2024:2024) (1941:1941:1941))
        (PORT d[9] (2187:2187:2187) (2056:2056:2056))
        (PORT d[10] (1647:1647:1647) (1584:1584:1584))
        (PORT d[11] (2098:2098:2098) (2009:2009:2009))
        (PORT clk (2044:2044:2044) (2101:2101:2101))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2044:2044:2044) (2101:2101:2101))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2045:2045:2045) (2102:2102:2102))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1996:1996:1996) (2054:2054:2054))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (995:995:995))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (996:996:996))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (996:996:996))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (996:996:996))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1593:1593:1593) (1630:1630:1630))
        (PORT asdata (2214:2214:2214) (2094:2094:2094))
        (PORT clrn (1660:1660:1660) (1608:1608:1608))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1593:1593:1593) (1630:1630:1630))
        (PORT asdata (2224:2224:2224) (2105:2105:2105))
        (PORT clrn (1660:1660:1660) (1608:1608:1608))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1776:1776:1776) (1703:1703:1703))
        (PORT d[1] (1408:1408:1408) (1365:1365:1365))
        (PORT d[2] (1435:1435:1435) (1386:1386:1386))
        (PORT d[3] (1745:1745:1745) (1657:1657:1657))
        (PORT d[4] (1411:1411:1411) (1383:1383:1383))
        (PORT d[5] (1449:1449:1449) (1415:1415:1415))
        (PORT d[6] (1452:1452:1452) (1399:1399:1399))
        (PORT d[7] (1386:1386:1386) (1357:1357:1357))
        (PORT d[8] (1355:1355:1355) (1328:1328:1328))
        (PORT d[9] (1654:1654:1654) (1573:1573:1573))
        (PORT d[10] (1363:1363:1363) (1338:1338:1338))
        (PORT d[11] (1403:1403:1403) (1367:1367:1367))
        (PORT clk (2032:2032:2032) (2090:2090:2090))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2032:2032:2032) (2090:2090:2090))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2033:2033:2033) (2091:2091:2091))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1984:1984:1984) (2043:2043:2043))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (984:984:984))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (956:956:956) (985:985:985))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (956:956:956) (985:985:985))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (956:956:956) (985:985:985))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1637:1637:1637))
        (PORT asdata (1895:1895:1895) (1749:1749:1749))
        (PORT clrn (1667:1667:1667) (1618:1618:1618))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1496:1496:1496) (1331:1331:1331))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1637:1637:1637))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1667:1667:1667) (1618:1618:1618))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1394:1394:1394) (1359:1359:1359))
        (PORT d[1] (1702:1702:1702) (1624:1624:1624))
        (PORT d[2] (1402:1402:1402) (1359:1359:1359))
        (PORT d[3] (1380:1380:1380) (1326:1326:1326))
        (PORT d[4] (1379:1379:1379) (1349:1349:1349))
        (PORT d[5] (1361:1361:1361) (1333:1333:1333))
        (PORT d[6] (1410:1410:1410) (1355:1355:1355))
        (PORT d[7] (2140:2140:2140) (2015:2015:2015))
        (PORT d[8] (1348:1348:1348) (1319:1319:1319))
        (PORT d[9] (1642:1642:1642) (1559:1559:1559))
        (PORT d[10] (1376:1376:1376) (1346:1346:1346))
        (PORT d[11] (1353:1353:1353) (1323:1323:1323))
        (PORT clk (2017:2017:2017) (2069:2069:2069))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2017:2017:2017) (2069:2069:2069))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2018:2018:2018) (2070:2070:2070))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1969:1969:1969) (2022:2022:2022))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (940:940:940) (963:963:963))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (964:964:964))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (964:964:964))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (964:964:964))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1605:1605:1605) (1640:1640:1640))
        (PORT asdata (1620:1620:1620) (1515:1515:1515))
        (PORT clrn (1670:1670:1670) (1620:1620:1620))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1605:1605:1605) (1640:1640:1640))
        (PORT asdata (1621:1621:1621) (1522:1522:1522))
        (PORT clrn (1670:1670:1670) (1620:1620:1620))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2046:2046:2046) (1951:1951:1951))
        (PORT d[1] (2004:2004:2004) (1908:1908:1908))
        (PORT d[2] (2074:2074:2074) (1978:1978:1978))
        (PORT d[3] (2067:2067:2067) (1971:1971:1971))
        (PORT d[4] (1735:1735:1735) (1678:1678:1678))
        (PORT d[5] (1643:1643:1643) (1592:1592:1592))
        (PORT d[6] (1681:1681:1681) (1611:1611:1611))
        (PORT d[7] (1681:1681:1681) (1607:1607:1607))
        (PORT d[8] (1739:1739:1739) (1676:1676:1676))
        (PORT d[9] (1735:1735:1735) (1638:1638:1638))
        (PORT d[10] (1660:1660:1660) (1604:1604:1604))
        (PORT d[11] (1674:1674:1674) (1620:1620:1620))
        (PORT clk (2052:2052:2052) (2107:2107:2107))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2052:2052:2052) (2107:2107:2107))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2053:2053:2053) (2108:2108:2108))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (2004:2004:2004) (2060:2060:2060))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (975:975:975) (1001:1001:1001))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (976:976:976) (1002:1002:1002))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (976:976:976) (1002:1002:1002))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (976:976:976) (1002:1002:1002))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1600:1600:1600) (1634:1634:1634))
        (PORT asdata (2192:2192:2192) (2069:2069:2069))
        (PORT clrn (1664:1664:1664) (1615:1615:1615))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1690:1690:1690) (1579:1579:1579))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1600:1600:1600) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1615:1615:1615))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (2922:2922:2922) (2746:2746:2746))
        (PORT d[1] (2393:2393:2393) (2268:2268:2268))
        (PORT d[2] (2418:2418:2418) (2299:2299:2299))
        (PORT d[3] (1662:1662:1662) (1581:1581:1581))
        (PORT d[4] (2120:2120:2120) (2037:2037:2037))
        (PORT d[5] (2790:2790:2790) (2651:2651:2651))
        (PORT d[6] (2070:2070:2070) (1973:1973:1973))
        (PORT d[7] (1268:1268:1268) (1243:1243:1243))
        (PORT d[8] (1677:1677:1677) (1627:1627:1627))
        (PORT d[9] (2110:2110:2110) (1984:1984:1984))
        (PORT d[10] (1676:1676:1676) (1611:1611:1611))
        (PORT d[11] (1332:1332:1332) (1302:1302:1302))
        (PORT clk (2043:2043:2043) (2098:2098:2098))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2043:2043:2043) (2098:2098:2098))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2044:2044:2044) (2099:2099:2099))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1995:1995:1995) (2051:2051:2051))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (966:966:966) (992:992:992))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (993:993:993))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (993:993:993))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (993:993:993))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1544:1544:1544) (1419:1419:1419))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1635:1635:1635))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1665:1665:1665) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1606:1606:1606) (1463:1463:1463))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1635:1635:1635))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1665:1665:1665) (1616:1616:1616))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (554:554:554) (573:573:573))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT asdata (770:770:770) (845:845:845))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (305:305:305) (379:379:379))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[3\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (303:303:303) (377:377:377))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT asdata (983:983:983) (1009:1009:1009))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (521:521:521) (546:546:546))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT asdata (1047:1047:1047) (1052:1052:1052))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT asdata (1002:1002:1002) (1021:1021:1021))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT asdata (1048:1048:1048) (1054:1054:1054))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (569:569:569) (587:587:587))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (571:571:571) (588:588:588))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (569:569:569) (587:587:587))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1634:1634:1634))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1664:1664:1664) (1610:1610:1610))
        (PORT ena (1279:1279:1279) (1212:1212:1212))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (787:787:787) (770:770:770))
        (PORT dataa[1] (817:817:817) (797:797:797))
        (PORT dataa[2] (899:899:899) (864:864:864))
        (PORT dataa[3] (861:861:861) (838:838:838))
        (PORT dataa[4] (494:494:494) (511:511:511))
        (PORT dataa[5] (493:493:493) (497:497:497))
        (PORT dataa[6] (918:918:918) (919:919:919))
        (PORT dataa[7] (895:895:895) (886:886:886))
        (PORT dataa[8] (1187:1187:1187) (1146:1146:1146))
        (PORT dataa[9] (1205:1205:1205) (1164:1164:1164))
        (PORT dataa[10] (870:870:870) (841:841:841))
        (PORT dataa[11] (906:906:906) (891:891:891))
        (PORT dataa[12] (1173:1173:1173) (1102:1102:1102))
        (PORT dataa[13] (1140:1140:1140) (1189:1189:1189))
        (PORT dataa[14] (1007:1007:1007) (1013:1013:1013))
        (PORT dataa[15] (1017:1017:1017) (1019:1019:1019))
        (PORT dataa[16] (1140:1140:1140) (1189:1189:1189))
        (PORT dataa[17] (1007:1007:1007) (1013:1013:1013))
        (PORT datab[6] (556:556:556) (585:585:585))
        (PORT datab[7] (792:792:792) (760:760:760))
        (PORT datab[8] (549:549:549) (571:571:571))
        (PORT datab[9] (803:803:803) (769:769:769))
        (PORT datab[10] (776:776:776) (759:759:759))
        (PORT datab[11] (556:556:556) (573:573:573))
        (PORT datab[12] (521:521:521) (545:545:545))
        (PORT datab[13] (552:552:552) (573:573:573))
        (PORT datab[14] (512:512:512) (537:537:537))
        (PORT datab[15] (536:536:536) (552:552:552))
        (PORT datab[16] (575:575:575) (558:558:558))
        (PORT datab[17] (540:540:540) (529:529:529))
        (IOPATH dataa dataout (3928:3928:3928) (3928:3928:3928))
        (IOPATH datab dataout (3863:3863:3863) (3863:3863:3863))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (139:139:139) (148:148:148))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (933:933:933) (931:931:931))
        (PORT dataa[7] (1189:1189:1189) (1137:1137:1137))
        (PORT dataa[8] (889:889:889) (884:884:884))
        (PORT dataa[9] (1222:1222:1222) (1146:1146:1146))
        (PORT dataa[10] (1200:1200:1200) (1144:1144:1144))
        (PORT dataa[11] (1266:1266:1266) (1217:1217:1217))
        (PORT dataa[12] (936:936:936) (937:937:937))
        (PORT dataa[13] (1338:1338:1338) (1288:1288:1288))
        (PORT dataa[14] (895:895:895) (895:895:895))
        (PORT dataa[15] (1244:1244:1244) (1167:1167:1167))
        (PORT dataa[16] (1152:1152:1152) (1219:1219:1219))
        (PORT dataa[17] (1139:1139:1139) (1198:1198:1198))
        (PORT datab[10] (811:811:811) (833:833:833))
        (PORT datab[11] (612:612:612) (589:589:589))
        (PORT datab[12] (624:624:624) (594:594:594))
        (PORT datab[13] (811:811:811) (833:833:833))
        (PORT datab[14] (612:612:612) (589:589:589))
        (PORT datab[15] (624:624:624) (594:594:594))
        (PORT datab[16] (811:811:811) (833:833:833))
        (PORT datab[17] (612:612:612) (589:589:589))
        (IOPATH dataa dataout (3928:3928:3928) (3928:3928:3928))
        (IOPATH datab dataout (3863:3863:3863) (3863:3863:3863))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (139:139:139) (148:148:148))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (713:713:713) (646:646:646))
        (PORT datab (1144:1144:1144) (1025:1025:1025))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (824:824:824) (774:774:774))
        (PORT datab (424:424:424) (387:387:387))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1195:1195:1195) (1074:1074:1074))
        (PORT datab (470:470:470) (422:422:422))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (728:728:728) (653:653:653))
        (PORT datab (1142:1142:1142) (1037:1037:1037))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1194:1194:1194) (1073:1073:1073))
        (PORT datab (470:470:470) (422:422:422))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (481:481:481) (435:435:435))
        (PORT datab (1236:1236:1236) (1113:1113:1113))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1453:1453:1453) (1288:1288:1288))
        (PORT datab (468:468:468) (420:420:420))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (1223:1223:1223) (1097:1097:1097))
        (PORT datad (413:413:413) (375:375:375))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
)
