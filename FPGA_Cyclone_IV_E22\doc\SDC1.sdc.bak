create_clock -name CLK_50M -period 20.000 [get_ports {SYS_CLK}]
create_clock -name CLK_165M -period 6.0606 [get_ports {DAC_CLK DAC_DATA[0] DAC_DATA[1] DAC_DATA[3] DAC_DATA[2] DAC_DATA[4] DAC_DATA[5] DAC_DATA[6] DAC_DATA[7] DAC_DATA[8] DAC_DATA[9] DAC_DATA[10] DAC_DATA[11] DAC_DATA[12] DAC_DATA[13]}]

set_clock_uncertainty -setup -rise_from altera_reserved_tck -rise_to altera_reserved_tck 0.150
set_clock_uncertainty -hold -rise_from altera_reserved_tck -rise_to altera_reserved_tck 0.150
set_clock_uncertainty -setup -rise_from altera_reserved_tck -fall_to altera_reserved_tck 0.150
set_clock_uncertainty -hold -rise_from altera_reserved_tck -fall_to altera_reserved_tck 0.150
set_clock_uncertainty -setup -fall_from altera_reserved_tck -fall_to altera_reserved_tck 0.150
set_clock_uncertainty -hold -fall_from altera_reserved_tck -fall_to altera_reserved_tck 0.150

derive_pll_clocks
derive_clock_uncertainty
