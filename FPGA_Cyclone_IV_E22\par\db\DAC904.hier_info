|DAC904_TOP
SYS_CLK => SYS_CLK.IN1
SYS_RST => SYS_RST.IN2
KEY_IN[0] => ~NO_FANOUT~
KEY_IN[1] => ~NO_FANOUT~
KEY_IN[2] => ~NO_FANOUT~
spi_sclk => spi_shift_reg[0].CLK
spi_sclk => spi_shift_reg[1].CLK
spi_sclk => spi_shift_reg[2].CLK
spi_sclk => spi_shift_reg[3].CLK
spi_sclk => spi_shift_reg[4].CLK
spi_sclk => spi_shift_reg[5].CLK
spi_sclk => spi_shift_reg[6].CLK
spi_sclk => spi_shift_reg[7].CLK
spi_sclk => spi_shift_reg[8].CLK
spi_sclk => spi_shift_reg[9].CLK
spi_sclk => spi_shift_reg[10].CLK
spi_sclk => spi_shift_reg[11].CLK
spi_sclk => spi_shift_reg[12].CLK
spi_sclk => spi_shift_reg[13].CLK
spi_sclk => spi_shift_reg[14].CLK
spi_sclk => spi_shift_reg[15].CLK
spi_mosi => spi_shift_reg[0].DATAIN
spi_cs_n => spi_cs_n_d1.DATAIN
spi_cs_n => spi_shift_reg[0].ENA
spi_cs_n => spi_shift_reg[1].ENA
spi_cs_n => spi_shift_reg[2].ENA
spi_cs_n => spi_shift_reg[3].ENA
spi_cs_n => spi_shift_reg[4].ENA
spi_cs_n => spi_shift_reg[5].ENA
spi_cs_n => spi_shift_reg[6].ENA
spi_cs_n => spi_shift_reg[7].ENA
spi_cs_n => spi_shift_reg[8].ENA
spi_cs_n => spi_shift_reg[9].ENA
spi_cs_n => spi_shift_reg[10].ENA
spi_cs_n => spi_shift_reg[11].ENA
spi_cs_n => spi_shift_reg[12].ENA
spi_cs_n => spi_shift_reg[13].ENA
spi_cs_n => spi_shift_reg[14].ENA
spi_cs_n => spi_shift_reg[15].ENA
PD <= <GND>
DAC_CLK <= CLK_20M.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[0] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[1] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[2] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[3] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[4] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[5] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[6] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[7] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[8] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[9] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[10] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[11] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[12] <= Mult0.DB_MAX_OUTPUT_PORT_TYPE
DAC_DATA[13] <= Add1.DB_MAX_OUTPUT_PORT_TYPE
debug_spi_valid <= <GND>
debug_freq_sel[0] <= spi_data[12].DB_MAX_OUTPUT_PORT_TYPE
debug_freq_sel[1] <= spi_data[13].DB_MAX_OUTPUT_PORT_TYPE
debug_freq_sel[2] <= spi_data[14].DB_MAX_OUTPUT_PORT_TYPE
debug_freq_sel[3] <= spi_data[15].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[0] <= spi_data[0].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[1] <= spi_data[1].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[2] <= spi_data[2].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[3] <= spi_data[3].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[4] <= spi_data[4].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[5] <= spi_data[5].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[6] <= spi_data[6].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[7] <= spi_data[7].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[8] <= spi_data[8].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[9] <= spi_data[9].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[10] <= spi_data[10].DB_MAX_OUTPUT_PORT_TYPE
debug_amplitude[11] <= spi_data[11].DB_MAX_OUTPUT_PORT_TYPE


|DAC904_TOP|PLL_CLK:u_PLL_CLK
areset => areset.IN1
inclk0 => sub_wire3[0].IN1
c0 <= altpll:altpll_component.clk


|DAC904_TOP|PLL_CLK:u_PLL_CLK|altpll:altpll_component
inclk[0] => PLL_CLK_altpll:auto_generated.inclk[0]
inclk[1] => PLL_CLK_altpll:auto_generated.inclk[1]
fbin => ~NO_FANOUT~
pllena => ~NO_FANOUT~
clkswitch => ~NO_FANOUT~
areset => PLL_CLK_altpll:auto_generated.areset
pfdena => ~NO_FANOUT~
clkena[0] => ~NO_FANOUT~
clkena[1] => ~NO_FANOUT~
clkena[2] => ~NO_FANOUT~
clkena[3] => ~NO_FANOUT~
clkena[4] => ~NO_FANOUT~
clkena[5] => ~NO_FANOUT~
extclkena[0] => ~NO_FANOUT~
extclkena[1] => ~NO_FANOUT~
extclkena[2] => ~NO_FANOUT~
extclkena[3] => ~NO_FANOUT~
scanclk => ~NO_FANOUT~
scanclkena => ~NO_FANOUT~
scanaclr => ~NO_FANOUT~
scanread => ~NO_FANOUT~
scanwrite => ~NO_FANOUT~
scandata => ~NO_FANOUT~
phasecounterselect[0] => ~NO_FANOUT~
phasecounterselect[1] => ~NO_FANOUT~
phasecounterselect[2] => ~NO_FANOUT~
phasecounterselect[3] => ~NO_FANOUT~
phaseupdown => ~NO_FANOUT~
phasestep => ~NO_FANOUT~
configupdate => ~NO_FANOUT~
fbmimicbidir <> <GND>
clk[0] <= clk[0].DB_MAX_OUTPUT_PORT_TYPE
clk[1] <= clk[1].DB_MAX_OUTPUT_PORT_TYPE
clk[2] <= clk[2].DB_MAX_OUTPUT_PORT_TYPE
clk[3] <= clk[3].DB_MAX_OUTPUT_PORT_TYPE
clk[4] <= clk[4].DB_MAX_OUTPUT_PORT_TYPE
extclk[0] <= <GND>
extclk[1] <= <GND>
extclk[2] <= <GND>
extclk[3] <= <GND>
clkbad[0] <= <GND>
clkbad[1] <= <GND>
enable1 <= <GND>
enable0 <= <GND>
activeclock <= <GND>
clkloss <= <GND>
locked <= <GND>
scandataout <= <GND>
scandone <= <GND>
sclkout0 <= <GND>
sclkout1 <= <GND>
phasedone <= <GND>
vcooverrange <= <GND>
vcounderrange <= <GND>
fbout <= <GND>
fref <= <GND>
icdrclk <= <GND>


|DAC904_TOP|PLL_CLK:u_PLL_CLK|altpll:altpll_component|PLL_CLK_altpll:auto_generated
areset => pll1.ARESET
clk[0] <= pll1.CLK
clk[1] <= pll1.CLK1
clk[2] <= pll1.CLK2
clk[3] <= pll1.CLK3
clk[4] <= pll1.CLK4
inclk[0] => pll1.CLK
inclk[1] => pll1.CLK1


|DAC904_TOP|ROM_Sin:u_ROM_Sin
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
address[10] => address[10].IN1
address[11] => address[11].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|DAC904_TOP|ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_aj91:auto_generated.address_a[0]
address_a[1] => altsyncram_aj91:auto_generated.address_a[1]
address_a[2] => altsyncram_aj91:auto_generated.address_a[2]
address_a[3] => altsyncram_aj91:auto_generated.address_a[3]
address_a[4] => altsyncram_aj91:auto_generated.address_a[4]
address_a[5] => altsyncram_aj91:auto_generated.address_a[5]
address_a[6] => altsyncram_aj91:auto_generated.address_a[6]
address_a[7] => altsyncram_aj91:auto_generated.address_a[7]
address_a[8] => altsyncram_aj91:auto_generated.address_a[8]
address_a[9] => altsyncram_aj91:auto_generated.address_a[9]
address_a[10] => altsyncram_aj91:auto_generated.address_a[10]
address_a[11] => altsyncram_aj91:auto_generated.address_a[11]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_aj91:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_aj91:auto_generated.q_a[0]
q_a[1] <= altsyncram_aj91:auto_generated.q_a[1]
q_a[2] <= altsyncram_aj91:auto_generated.q_a[2]
q_a[3] <= altsyncram_aj91:auto_generated.q_a[3]
q_a[4] <= altsyncram_aj91:auto_generated.q_a[4]
q_a[5] <= altsyncram_aj91:auto_generated.q_a[5]
q_a[6] <= altsyncram_aj91:auto_generated.q_a[6]
q_a[7] <= altsyncram_aj91:auto_generated.q_a[7]
q_a[8] <= altsyncram_aj91:auto_generated.q_a[8]
q_a[9] <= altsyncram_aj91:auto_generated.q_a[9]
q_a[10] <= altsyncram_aj91:auto_generated.q_a[10]
q_a[11] <= altsyncram_aj91:auto_generated.q_a[11]
q_a[12] <= altsyncram_aj91:auto_generated.q_a[12]
q_a[13] <= altsyncram_aj91:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|DAC904_TOP|ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
address_a[10] => ram_block1a0.PORTAADDR10
address_a[10] => ram_block1a1.PORTAADDR10
address_a[10] => ram_block1a2.PORTAADDR10
address_a[10] => ram_block1a3.PORTAADDR10
address_a[10] => ram_block1a4.PORTAADDR10
address_a[10] => ram_block1a5.PORTAADDR10
address_a[10] => ram_block1a6.PORTAADDR10
address_a[10] => ram_block1a7.PORTAADDR10
address_a[10] => ram_block1a8.PORTAADDR10
address_a[10] => ram_block1a9.PORTAADDR10
address_a[10] => ram_block1a10.PORTAADDR10
address_a[10] => ram_block1a11.PORTAADDR10
address_a[10] => ram_block1a12.PORTAADDR10
address_a[10] => ram_block1a13.PORTAADDR10
address_a[11] => ram_block1a0.PORTAADDR11
address_a[11] => ram_block1a1.PORTAADDR11
address_a[11] => ram_block1a2.PORTAADDR11
address_a[11] => ram_block1a3.PORTAADDR11
address_a[11] => ram_block1a4.PORTAADDR11
address_a[11] => ram_block1a5.PORTAADDR11
address_a[11] => ram_block1a6.PORTAADDR11
address_a[11] => ram_block1a7.PORTAADDR11
address_a[11] => ram_block1a8.PORTAADDR11
address_a[11] => ram_block1a9.PORTAADDR11
address_a[11] => ram_block1a10.PORTAADDR11
address_a[11] => ram_block1a11.PORTAADDR11
address_a[11] => ram_block1a12.PORTAADDR11
address_a[11] => ram_block1a13.PORTAADDR11
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|DAC904_TOP|ROM_Tri:ROM_Tri
address[0] => address[0].IN1
address[1] => address[1].IN1
address[2] => address[2].IN1
address[3] => address[3].IN1
address[4] => address[4].IN1
address[5] => address[5].IN1
address[6] => address[6].IN1
address[7] => address[7].IN1
address[8] => address[8].IN1
address[9] => address[9].IN1
address[10] => address[10].IN1
address[11] => address[11].IN1
clock => clock.IN1
q[0] <= altsyncram:altsyncram_component.q_a
q[1] <= altsyncram:altsyncram_component.q_a
q[2] <= altsyncram:altsyncram_component.q_a
q[3] <= altsyncram:altsyncram_component.q_a
q[4] <= altsyncram:altsyncram_component.q_a
q[5] <= altsyncram:altsyncram_component.q_a
q[6] <= altsyncram:altsyncram_component.q_a
q[7] <= altsyncram:altsyncram_component.q_a
q[8] <= altsyncram:altsyncram_component.q_a
q[9] <= altsyncram:altsyncram_component.q_a
q[10] <= altsyncram:altsyncram_component.q_a
q[11] <= altsyncram:altsyncram_component.q_a
q[12] <= altsyncram:altsyncram_component.q_a
q[13] <= altsyncram:altsyncram_component.q_a


|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component
wren_a => ~NO_FANOUT~
rden_a => ~NO_FANOUT~
wren_b => ~NO_FANOUT~
rden_b => ~NO_FANOUT~
data_a[0] => ~NO_FANOUT~
data_a[1] => ~NO_FANOUT~
data_a[2] => ~NO_FANOUT~
data_a[3] => ~NO_FANOUT~
data_a[4] => ~NO_FANOUT~
data_a[5] => ~NO_FANOUT~
data_a[6] => ~NO_FANOUT~
data_a[7] => ~NO_FANOUT~
data_a[8] => ~NO_FANOUT~
data_a[9] => ~NO_FANOUT~
data_a[10] => ~NO_FANOUT~
data_a[11] => ~NO_FANOUT~
data_a[12] => ~NO_FANOUT~
data_a[13] => ~NO_FANOUT~
data_b[0] => ~NO_FANOUT~
address_a[0] => altsyncram_4aa1:auto_generated.address_a[0]
address_a[1] => altsyncram_4aa1:auto_generated.address_a[1]
address_a[2] => altsyncram_4aa1:auto_generated.address_a[2]
address_a[3] => altsyncram_4aa1:auto_generated.address_a[3]
address_a[4] => altsyncram_4aa1:auto_generated.address_a[4]
address_a[5] => altsyncram_4aa1:auto_generated.address_a[5]
address_a[6] => altsyncram_4aa1:auto_generated.address_a[6]
address_a[7] => altsyncram_4aa1:auto_generated.address_a[7]
address_a[8] => altsyncram_4aa1:auto_generated.address_a[8]
address_a[9] => altsyncram_4aa1:auto_generated.address_a[9]
address_a[10] => altsyncram_4aa1:auto_generated.address_a[10]
address_a[11] => altsyncram_4aa1:auto_generated.address_a[11]
address_b[0] => ~NO_FANOUT~
addressstall_a => ~NO_FANOUT~
addressstall_b => ~NO_FANOUT~
clock0 => altsyncram_4aa1:auto_generated.clock0
clock1 => ~NO_FANOUT~
clocken0 => ~NO_FANOUT~
clocken1 => ~NO_FANOUT~
clocken2 => ~NO_FANOUT~
clocken3 => ~NO_FANOUT~
aclr0 => ~NO_FANOUT~
aclr1 => ~NO_FANOUT~
byteena_a[0] => ~NO_FANOUT~
byteena_b[0] => ~NO_FANOUT~
q_a[0] <= altsyncram_4aa1:auto_generated.q_a[0]
q_a[1] <= altsyncram_4aa1:auto_generated.q_a[1]
q_a[2] <= altsyncram_4aa1:auto_generated.q_a[2]
q_a[3] <= altsyncram_4aa1:auto_generated.q_a[3]
q_a[4] <= altsyncram_4aa1:auto_generated.q_a[4]
q_a[5] <= altsyncram_4aa1:auto_generated.q_a[5]
q_a[6] <= altsyncram_4aa1:auto_generated.q_a[6]
q_a[7] <= altsyncram_4aa1:auto_generated.q_a[7]
q_a[8] <= altsyncram_4aa1:auto_generated.q_a[8]
q_a[9] <= altsyncram_4aa1:auto_generated.q_a[9]
q_a[10] <= altsyncram_4aa1:auto_generated.q_a[10]
q_a[11] <= altsyncram_4aa1:auto_generated.q_a[11]
q_a[12] <= altsyncram_4aa1:auto_generated.q_a[12]
q_a[13] <= altsyncram_4aa1:auto_generated.q_a[13]
q_b[0] <= <GND>
eccstatus[0] <= <GND>
eccstatus[1] <= <GND>
eccstatus[2] <= <GND>


|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated
address_a[0] => ram_block1a0.PORTAADDR
address_a[0] => ram_block1a1.PORTAADDR
address_a[0] => ram_block1a2.PORTAADDR
address_a[0] => ram_block1a3.PORTAADDR
address_a[0] => ram_block1a4.PORTAADDR
address_a[0] => ram_block1a5.PORTAADDR
address_a[0] => ram_block1a6.PORTAADDR
address_a[0] => ram_block1a7.PORTAADDR
address_a[0] => ram_block1a8.PORTAADDR
address_a[0] => ram_block1a9.PORTAADDR
address_a[0] => ram_block1a10.PORTAADDR
address_a[0] => ram_block1a11.PORTAADDR
address_a[0] => ram_block1a12.PORTAADDR
address_a[0] => ram_block1a13.PORTAADDR
address_a[1] => ram_block1a0.PORTAADDR1
address_a[1] => ram_block1a1.PORTAADDR1
address_a[1] => ram_block1a2.PORTAADDR1
address_a[1] => ram_block1a3.PORTAADDR1
address_a[1] => ram_block1a4.PORTAADDR1
address_a[1] => ram_block1a5.PORTAADDR1
address_a[1] => ram_block1a6.PORTAADDR1
address_a[1] => ram_block1a7.PORTAADDR1
address_a[1] => ram_block1a8.PORTAADDR1
address_a[1] => ram_block1a9.PORTAADDR1
address_a[1] => ram_block1a10.PORTAADDR1
address_a[1] => ram_block1a11.PORTAADDR1
address_a[1] => ram_block1a12.PORTAADDR1
address_a[1] => ram_block1a13.PORTAADDR1
address_a[2] => ram_block1a0.PORTAADDR2
address_a[2] => ram_block1a1.PORTAADDR2
address_a[2] => ram_block1a2.PORTAADDR2
address_a[2] => ram_block1a3.PORTAADDR2
address_a[2] => ram_block1a4.PORTAADDR2
address_a[2] => ram_block1a5.PORTAADDR2
address_a[2] => ram_block1a6.PORTAADDR2
address_a[2] => ram_block1a7.PORTAADDR2
address_a[2] => ram_block1a8.PORTAADDR2
address_a[2] => ram_block1a9.PORTAADDR2
address_a[2] => ram_block1a10.PORTAADDR2
address_a[2] => ram_block1a11.PORTAADDR2
address_a[2] => ram_block1a12.PORTAADDR2
address_a[2] => ram_block1a13.PORTAADDR2
address_a[3] => ram_block1a0.PORTAADDR3
address_a[3] => ram_block1a1.PORTAADDR3
address_a[3] => ram_block1a2.PORTAADDR3
address_a[3] => ram_block1a3.PORTAADDR3
address_a[3] => ram_block1a4.PORTAADDR3
address_a[3] => ram_block1a5.PORTAADDR3
address_a[3] => ram_block1a6.PORTAADDR3
address_a[3] => ram_block1a7.PORTAADDR3
address_a[3] => ram_block1a8.PORTAADDR3
address_a[3] => ram_block1a9.PORTAADDR3
address_a[3] => ram_block1a10.PORTAADDR3
address_a[3] => ram_block1a11.PORTAADDR3
address_a[3] => ram_block1a12.PORTAADDR3
address_a[3] => ram_block1a13.PORTAADDR3
address_a[4] => ram_block1a0.PORTAADDR4
address_a[4] => ram_block1a1.PORTAADDR4
address_a[4] => ram_block1a2.PORTAADDR4
address_a[4] => ram_block1a3.PORTAADDR4
address_a[4] => ram_block1a4.PORTAADDR4
address_a[4] => ram_block1a5.PORTAADDR4
address_a[4] => ram_block1a6.PORTAADDR4
address_a[4] => ram_block1a7.PORTAADDR4
address_a[4] => ram_block1a8.PORTAADDR4
address_a[4] => ram_block1a9.PORTAADDR4
address_a[4] => ram_block1a10.PORTAADDR4
address_a[4] => ram_block1a11.PORTAADDR4
address_a[4] => ram_block1a12.PORTAADDR4
address_a[4] => ram_block1a13.PORTAADDR4
address_a[5] => ram_block1a0.PORTAADDR5
address_a[5] => ram_block1a1.PORTAADDR5
address_a[5] => ram_block1a2.PORTAADDR5
address_a[5] => ram_block1a3.PORTAADDR5
address_a[5] => ram_block1a4.PORTAADDR5
address_a[5] => ram_block1a5.PORTAADDR5
address_a[5] => ram_block1a6.PORTAADDR5
address_a[5] => ram_block1a7.PORTAADDR5
address_a[5] => ram_block1a8.PORTAADDR5
address_a[5] => ram_block1a9.PORTAADDR5
address_a[5] => ram_block1a10.PORTAADDR5
address_a[5] => ram_block1a11.PORTAADDR5
address_a[5] => ram_block1a12.PORTAADDR5
address_a[5] => ram_block1a13.PORTAADDR5
address_a[6] => ram_block1a0.PORTAADDR6
address_a[6] => ram_block1a1.PORTAADDR6
address_a[6] => ram_block1a2.PORTAADDR6
address_a[6] => ram_block1a3.PORTAADDR6
address_a[6] => ram_block1a4.PORTAADDR6
address_a[6] => ram_block1a5.PORTAADDR6
address_a[6] => ram_block1a6.PORTAADDR6
address_a[6] => ram_block1a7.PORTAADDR6
address_a[6] => ram_block1a8.PORTAADDR6
address_a[6] => ram_block1a9.PORTAADDR6
address_a[6] => ram_block1a10.PORTAADDR6
address_a[6] => ram_block1a11.PORTAADDR6
address_a[6] => ram_block1a12.PORTAADDR6
address_a[6] => ram_block1a13.PORTAADDR6
address_a[7] => ram_block1a0.PORTAADDR7
address_a[7] => ram_block1a1.PORTAADDR7
address_a[7] => ram_block1a2.PORTAADDR7
address_a[7] => ram_block1a3.PORTAADDR7
address_a[7] => ram_block1a4.PORTAADDR7
address_a[7] => ram_block1a5.PORTAADDR7
address_a[7] => ram_block1a6.PORTAADDR7
address_a[7] => ram_block1a7.PORTAADDR7
address_a[7] => ram_block1a8.PORTAADDR7
address_a[7] => ram_block1a9.PORTAADDR7
address_a[7] => ram_block1a10.PORTAADDR7
address_a[7] => ram_block1a11.PORTAADDR7
address_a[7] => ram_block1a12.PORTAADDR7
address_a[7] => ram_block1a13.PORTAADDR7
address_a[8] => ram_block1a0.PORTAADDR8
address_a[8] => ram_block1a1.PORTAADDR8
address_a[8] => ram_block1a2.PORTAADDR8
address_a[8] => ram_block1a3.PORTAADDR8
address_a[8] => ram_block1a4.PORTAADDR8
address_a[8] => ram_block1a5.PORTAADDR8
address_a[8] => ram_block1a6.PORTAADDR8
address_a[8] => ram_block1a7.PORTAADDR8
address_a[8] => ram_block1a8.PORTAADDR8
address_a[8] => ram_block1a9.PORTAADDR8
address_a[8] => ram_block1a10.PORTAADDR8
address_a[8] => ram_block1a11.PORTAADDR8
address_a[8] => ram_block1a12.PORTAADDR8
address_a[8] => ram_block1a13.PORTAADDR8
address_a[9] => ram_block1a0.PORTAADDR9
address_a[9] => ram_block1a1.PORTAADDR9
address_a[9] => ram_block1a2.PORTAADDR9
address_a[9] => ram_block1a3.PORTAADDR9
address_a[9] => ram_block1a4.PORTAADDR9
address_a[9] => ram_block1a5.PORTAADDR9
address_a[9] => ram_block1a6.PORTAADDR9
address_a[9] => ram_block1a7.PORTAADDR9
address_a[9] => ram_block1a8.PORTAADDR9
address_a[9] => ram_block1a9.PORTAADDR9
address_a[9] => ram_block1a10.PORTAADDR9
address_a[9] => ram_block1a11.PORTAADDR9
address_a[9] => ram_block1a12.PORTAADDR9
address_a[9] => ram_block1a13.PORTAADDR9
address_a[10] => ram_block1a0.PORTAADDR10
address_a[10] => ram_block1a1.PORTAADDR10
address_a[10] => ram_block1a2.PORTAADDR10
address_a[10] => ram_block1a3.PORTAADDR10
address_a[10] => ram_block1a4.PORTAADDR10
address_a[10] => ram_block1a5.PORTAADDR10
address_a[10] => ram_block1a6.PORTAADDR10
address_a[10] => ram_block1a7.PORTAADDR10
address_a[10] => ram_block1a8.PORTAADDR10
address_a[10] => ram_block1a9.PORTAADDR10
address_a[10] => ram_block1a10.PORTAADDR10
address_a[10] => ram_block1a11.PORTAADDR10
address_a[10] => ram_block1a12.PORTAADDR10
address_a[10] => ram_block1a13.PORTAADDR10
address_a[11] => ram_block1a0.PORTAADDR11
address_a[11] => ram_block1a1.PORTAADDR11
address_a[11] => ram_block1a2.PORTAADDR11
address_a[11] => ram_block1a3.PORTAADDR11
address_a[11] => ram_block1a4.PORTAADDR11
address_a[11] => ram_block1a5.PORTAADDR11
address_a[11] => ram_block1a6.PORTAADDR11
address_a[11] => ram_block1a7.PORTAADDR11
address_a[11] => ram_block1a8.PORTAADDR11
address_a[11] => ram_block1a9.PORTAADDR11
address_a[11] => ram_block1a10.PORTAADDR11
address_a[11] => ram_block1a11.PORTAADDR11
address_a[11] => ram_block1a12.PORTAADDR11
address_a[11] => ram_block1a13.PORTAADDR11
clock0 => ram_block1a0.CLK0
clock0 => ram_block1a1.CLK0
clock0 => ram_block1a2.CLK0
clock0 => ram_block1a3.CLK0
clock0 => ram_block1a4.CLK0
clock0 => ram_block1a5.CLK0
clock0 => ram_block1a6.CLK0
clock0 => ram_block1a7.CLK0
clock0 => ram_block1a8.CLK0
clock0 => ram_block1a9.CLK0
clock0 => ram_block1a10.CLK0
clock0 => ram_block1a11.CLK0
clock0 => ram_block1a12.CLK0
clock0 => ram_block1a13.CLK0
q_a[0] <= ram_block1a0.PORTADATAOUT
q_a[1] <= ram_block1a1.PORTADATAOUT
q_a[2] <= ram_block1a2.PORTADATAOUT
q_a[3] <= ram_block1a3.PORTADATAOUT
q_a[4] <= ram_block1a4.PORTADATAOUT
q_a[5] <= ram_block1a5.PORTADATAOUT
q_a[6] <= ram_block1a6.PORTADATAOUT
q_a[7] <= ram_block1a7.PORTADATAOUT
q_a[8] <= ram_block1a8.PORTADATAOUT
q_a[9] <= ram_block1a9.PORTADATAOUT
q_a[10] <= ram_block1a10.PORTADATAOUT
q_a[11] <= ram_block1a11.PORTADATAOUT
q_a[12] <= ram_block1a12.PORTADATAOUT
q_a[13] <= ram_block1a13.PORTADATAOUT


|DAC904_TOP|add_32bit:u_add_32bit
clk => add[0].CLK
clk => add[1].CLK
clk => add[2].CLK
clk => add[3].CLK
clk => add[4].CLK
clk => add[5].CLK
clk => add[6].CLK
clk => add[7].CLK
clk => add[8].CLK
clk => add[9].CLK
clk => add[10].CLK
clk => add[11].CLK
clk => add[12].CLK
clk => add[13].CLK
clk => add[14].CLK
clk => add[15].CLK
clk => add[16].CLK
clk => add[17].CLK
clk => add[18].CLK
clk => add[19].CLK
clk => add[20].CLK
clk => add[21].CLK
clk => add[22].CLK
clk => add[23].CLK
clk => add[24].CLK
clk => add[25].CLK
clk => add[26].CLK
clk => add[27].CLK
clk => add[28].CLK
clk => add[29].CLK
clk => add[30].CLK
clk => add[31].CLK
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
rst => add.OUTPUTSELECT
fr_k[0] => Add0.IN32
fr_k[1] => Add0.IN31
fr_k[2] => Add0.IN30
fr_k[3] => Add0.IN29
fr_k[4] => Add0.IN28
fr_k[5] => Add0.IN27
fr_k[6] => Add0.IN26
fr_k[7] => Add0.IN25
fr_k[8] => Add0.IN24
fr_k[9] => Add0.IN23
fr_k[10] => Add0.IN22
fr_k[11] => Add0.IN21
fr_k[12] => Add0.IN20
fr_k[13] => Add0.IN19
fr_k[14] => Add0.IN18
fr_k[15] => Add0.IN17
fr_k[16] => Add0.IN16
fr_k[17] => Add0.IN15
fr_k[18] => Add0.IN14
fr_k[19] => Add0.IN13
fr_k[20] => Add0.IN12
fr_k[21] => Add0.IN11
fr_k[22] => Add0.IN10
fr_k[23] => Add0.IN9
fr_k[24] => Add0.IN8
fr_k[25] => Add0.IN7
fr_k[26] => Add0.IN6
fr_k[27] => Add0.IN5
fr_k[28] => Add0.IN4
fr_k[29] => Add0.IN3
fr_k[30] => Add0.IN2
fr_k[31] => Add0.IN1
adder[0] <= add[20].DB_MAX_OUTPUT_PORT_TYPE
adder[1] <= add[21].DB_MAX_OUTPUT_PORT_TYPE
adder[2] <= add[22].DB_MAX_OUTPUT_PORT_TYPE
adder[3] <= add[23].DB_MAX_OUTPUT_PORT_TYPE
adder[4] <= add[24].DB_MAX_OUTPUT_PORT_TYPE
adder[5] <= add[25].DB_MAX_OUTPUT_PORT_TYPE
adder[6] <= add[26].DB_MAX_OUTPUT_PORT_TYPE
adder[7] <= add[27].DB_MAX_OUTPUT_PORT_TYPE
adder[8] <= add[28].DB_MAX_OUTPUT_PORT_TYPE
adder[9] <= add[29].DB_MAX_OUTPUT_PORT_TYPE
adder[10] <= add[30].DB_MAX_OUTPUT_PORT_TYPE
adder[11] <= add[31].DB_MAX_OUTPUT_PORT_TYPE


|DAC904_TOP|sel_wave:u_sel_wave
clk => da_out_reg[0].CLK
clk => da_out_reg[1].CLK
clk => da_out_reg[2].CLK
clk => da_out_reg[3].CLK
clk => da_out_reg[4].CLK
clk => da_out_reg[5].CLK
clk => da_out_reg[6].CLK
clk => da_out_reg[7].CLK
clk => da_out_reg[8].CLK
clk => da_out_reg[9].CLK
clk => da_out_reg[10].CLK
clk => da_out_reg[11].CLK
clk => da_out_reg[12].CLK
clk => da_out_reg[13].CLK
rst_n => da_out_reg[0].ACLR
rst_n => da_out_reg[1].ACLR
rst_n => da_out_reg[2].ACLR
rst_n => da_out_reg[3].ACLR
rst_n => da_out_reg[4].ACLR
rst_n => da_out_reg[5].ACLR
rst_n => da_out_reg[6].ACLR
rst_n => da_out_reg[7].ACLR
rst_n => da_out_reg[8].ACLR
rst_n => da_out_reg[9].ACLR
rst_n => da_out_reg[10].ACLR
rst_n => da_out_reg[11].ACLR
rst_n => da_out_reg[12].ACLR
rst_n => da_out_reg[13].ACLR
sel[0] => Mux0.IN1
sel[0] => Mux1.IN1
sel[0] => Mux2.IN1
sel[0] => Mux3.IN1
sel[0] => Mux4.IN1
sel[0] => Mux5.IN1
sel[0] => Mux6.IN1
sel[0] => Mux7.IN1
sel[0] => Mux8.IN1
sel[0] => Mux9.IN1
sel[0] => Mux10.IN1
sel[0] => Mux11.IN1
sel[0] => Mux12.IN1
sel[0] => Mux13.IN1
sel[1] => Mux0.IN0
sel[1] => Mux1.IN0
sel[1] => Mux2.IN0
sel[1] => Mux3.IN0
sel[1] => Mux4.IN0
sel[1] => Mux5.IN0
sel[1] => Mux6.IN0
sel[1] => Mux7.IN0
sel[1] => Mux8.IN0
sel[1] => Mux9.IN0
sel[1] => Mux10.IN0
sel[1] => Mux11.IN0
sel[1] => Mux12.IN0
sel[1] => Mux13.IN0
da_ina[0] => Mux13.IN2
da_ina[0] => Mux13.IN3
da_ina[1] => Mux12.IN2
da_ina[1] => Mux12.IN3
da_ina[2] => Mux11.IN2
da_ina[2] => Mux11.IN3
da_ina[3] => Mux10.IN2
da_ina[3] => Mux10.IN3
da_ina[4] => Mux9.IN2
da_ina[4] => Mux9.IN3
da_ina[5] => Mux8.IN2
da_ina[5] => Mux8.IN3
da_ina[6] => Mux7.IN2
da_ina[6] => Mux7.IN3
da_ina[7] => Mux6.IN2
da_ina[7] => Mux6.IN3
da_ina[8] => Mux5.IN2
da_ina[8] => Mux5.IN3
da_ina[9] => Mux4.IN2
da_ina[9] => Mux4.IN3
da_ina[10] => Mux3.IN2
da_ina[10] => Mux3.IN3
da_ina[11] => Mux2.IN2
da_ina[11] => Mux2.IN3
da_ina[12] => Mux1.IN2
da_ina[12] => Mux1.IN3
da_ina[13] => Mux0.IN2
da_ina[13] => Mux0.IN3
da_inb[0] => Mux13.IN4
da_inb[1] => Mux12.IN4
da_inb[2] => Mux11.IN4
da_inb[3] => Mux10.IN4
da_inb[4] => Mux9.IN4
da_inb[5] => Mux8.IN4
da_inb[6] => Mux7.IN4
da_inb[7] => Mux6.IN4
da_inb[8] => Mux5.IN4
da_inb[9] => Mux4.IN4
da_inb[10] => Mux3.IN4
da_inb[11] => Mux2.IN4
da_inb[12] => Mux1.IN4
da_inb[13] => Mux0.IN4
da_inc[0] => Mux13.IN5
da_inc[1] => Mux12.IN5
da_inc[2] => Mux11.IN5
da_inc[3] => Mux10.IN5
da_inc[4] => Mux9.IN5
da_inc[5] => Mux8.IN5
da_inc[6] => Mux7.IN5
da_inc[7] => Mux6.IN5
da_inc[8] => Mux5.IN5
da_inc[9] => Mux4.IN5
da_inc[10] => Mux3.IN5
da_inc[11] => Mux2.IN5
da_inc[12] => Mux1.IN5
da_inc[13] => Mux0.IN5
da_out[0] <= da_out_reg[0].DB_MAX_OUTPUT_PORT_TYPE
da_out[1] <= da_out_reg[1].DB_MAX_OUTPUT_PORT_TYPE
da_out[2] <= da_out_reg[2].DB_MAX_OUTPUT_PORT_TYPE
da_out[3] <= da_out_reg[3].DB_MAX_OUTPUT_PORT_TYPE
da_out[4] <= da_out_reg[4].DB_MAX_OUTPUT_PORT_TYPE
da_out[5] <= da_out_reg[5].DB_MAX_OUTPUT_PORT_TYPE
da_out[6] <= da_out_reg[6].DB_MAX_OUTPUT_PORT_TYPE
da_out[7] <= da_out_reg[7].DB_MAX_OUTPUT_PORT_TYPE
da_out[8] <= da_out_reg[8].DB_MAX_OUTPUT_PORT_TYPE
da_out[9] <= da_out_reg[9].DB_MAX_OUTPUT_PORT_TYPE
da_out[10] <= da_out_reg[10].DB_MAX_OUTPUT_PORT_TYPE
da_out[11] <= da_out_reg[11].DB_MAX_OUTPUT_PORT_TYPE
da_out[12] <= da_out_reg[12].DB_MAX_OUTPUT_PORT_TYPE
da_out[13] <= da_out_reg[13].DB_MAX_OUTPUT_PORT_TYPE


