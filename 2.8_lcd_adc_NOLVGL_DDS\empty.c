#include "ti_msp_dl_config.h"
#include "board.h"
#include "stdio.h"
#include "lcd_init.h"
#include "lcd.h"
#include "pic.h"
#include "wave.h"
#include <math.h>
#include "arm_math.h"
#include "BSP/AD9850/ad9850.h"
#include "adc_channel.h"
// #include "button_control.h"  // 不需要复杂的按键模块

// The GPIO definitions are now self-contained within ad9850.c,
// so they are no longer needed here.

// The callback and delay functions are also no longer needed
// as the new driver is self-contained.

uint16_t gADCSamples[1024] = {0};
int adc0_done = 0;
// --- 新增：为第二路ADC创建独立的数组和标志位 ---
uint16_t gADCSamples_ch1[1024] = {0};
int adc1_done = 0;

// --- 内存安全优化：将浮点数组移到全局静态区域 ---
static float32_t g_float_samples_current[1024];
static float32_t g_float_samples_voltage[1024];

// --- 优化的FFT数组（1024点FFT，内存优化） ---
static float32_t g_fft_input[2048];    // 1024个复数 = 2048个float32_t (8KB)
static float32_t g_fft_output[1024];   // FFT幅度谱输出 (4KB)
static arm_cfft_instance_f32 g_fft_instance;


// --- 代码重构：使用通道管理结构替代重复代码 ---
static Point g_prev_points_ch0[32];
static Point g_prev_points_ch1[32];
static adc_channel_t g_channels[2];  // 两个ADC通道

// --- 新增：用于动态控制AD9850频率 ---
static uint32_t g_current_freq_hz = 4555; // 初始频率 10kHz

// --- 按键中断标志 ---
volatile uint32_t g_button_flags = 0;
#define BUTTON_UP_FLAG   0x01
#define BUTTON_DOWN_FLAG 0x02




int main(void)
{
    SYSCFG_DL_init();

    LCD_Init(); // LCD初始化

    // 初始化DDS模块
    dds_reset();

    // 验证DDS配置
    if (dds_verify_config()) {
        // 显示DDS参考时钟信息（调试用）
        uint32_t ref_clock = dds_get_ref_clock();
        // 可以通过串口输出或LCD显示，这里暂时注释
        // printf("DDS Reference Clock: %lu Hz\n", ref_clock);
    }

    // 初始化CMSIS-DSP FFT实例（1024点FFT，内存优化）
    arm_status fft_status = arm_cfft_init_f32(&g_fft_instance, 1024);
    if (fft_status != ARM_MATH_SUCCESS) {
        // FFT初始化失败，可以在LCD上显示错误
        LCD_ShowString(10, 65, (const u8 *)"FFT Init Failed", RED, GREEN, 16, 0);
    }

    dds_set(g_current_freq_hz); // Set initial frequency

    // 确保DDS持续输出
    delay_ms(100);  // 给DDS一些时间稳定

  	// dma ch0 - Corrected to use DriverLib API
  	DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_0_INST, ADC12_0_ADCMEM_0));
  	DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gADCSamples[0]);
  	DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    // --- 新增：配置和启用DMA通道1，遵循您的代码风格 ---
    // Corrected to use DriverLib API
  	DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_1_INST, ADC12_1_ADCMEM_0));
  	DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADCSamples_ch1[0]);
  	DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);
    
    LCD_Fill(0,0,LCD_W,LCD_H,GREEN);
    delay_ms(100);
    
    LCD_ShowString(10, 5, (const u8 *)"Voltage", BLACK, GREEN, 16, 0);
    LCD_ShowString(10, 25, (const u8 *)"Current", BLACK, GREEN, 16, 0);
    LCD_ShowString(10, 45, (const u8 *)"DDS Freq", BLACK, GREEN, 16, 0);
    // 只显示FFT频率标签
    LCD_ShowString(160, 5, (const u8 *)"FFT:", RED, GREEN, 16, 0);
	LCD_ShowChinese(100,220,(u8 *)"关注吸勾勾谢谢喵",YELLOW,GREEN,16,1);
      
    // Conversion factors based on 3.3V VREF and assumed hardware
    const float VREF = 3.3f;
    const int ADC_MAX = 4095;

    // Voltage conversion: Direct connection, ratio is 1:1
    const float VOLTAGE_CONVERSION_FACTOR = (VREF / ADC_MAX) * 1.0f;

    // Current conversion: Direct connection, ratio is 1:1 (measures voltage, not amps)
    const float CURRENT_CONVERSION_FACTOR = (VREF / ADC_MAX) * 1.0f;

    // --- 初始化ADC通道管理结构 ---
    adc_channel_init(&g_channels[0], gADCSamples, g_float_samples_current, g_prev_points_ch0,
                     &adc0_done, BLUE, "Current", "A", CURRENT_CONVERSION_FACTOR);
    adc_channel_init(&g_channels[1], gADCSamples_ch1, g_float_samples_voltage, g_prev_points_ch1,
                     &adc1_done, RED, "Voltage", "V", VOLTAGE_CONVERSION_FACTOR);

    // 初始显示频率
    LCD_ShowFloatNum1(85, 45, (float)g_current_freq_hz / 1000.0f, 4, BLACK, GREEN, 16);
    LCD_ShowString(135, 45, (const u8 *)"kHz", BLACK, GREEN, 16, 0);

	NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
    // --- 新增：启用ADC1的中断 ---
	NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);
    // --- 启用按键中断（改进版本） ---
    NVIC_EnableIRQ(BUTTONS_INT_IRQN);

  	DL_Timer_startCounter(TIMER_0_INST);

 	while(1)
 	{
		if (adc0_done == 1) {

            DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, 1024);
            // Corrected to use DriverLib API
            DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_0_INST, ADC12_0_ADCMEM_0));
            DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gADCSamples[0]);
            DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);
            adc0_done = 0;
        }

        // --- 新增：处理第二路ADC的数据 ---
		if (adc1_done == 1) {
            DL_DMA_setTransferSize(DMA, DMA_CH1_CHAN_ID, 1024);
            // Corrected to use DriverLib API
            DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_1_INST, ADC12_1_ADCMEM_0));
            DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADCSamples_ch1[0]);
            DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);
            adc1_done = 0;
        }
		
        // --- 使用通道管理结构处理数据 ---
        adc_channel_process_data(&g_channels[0], 1024);  // 处理电流通道
        adc_channel_process_data(&g_channels[1], 1024);  // 处理电压通道

        // 显示数值
        adc_channel_display_values(&g_channels[1], 85, 5, BLACK, GREEN);   // 电压
        adc_channel_display_values(&g_channels[0], 85, 25, BLACK, GREEN);  // 电流
		
        // --- 中断标志处理（安全快速） ---
        if (g_button_flags & BUTTON_UP_FLAG) {
            g_button_flags &= ~BUTTON_UP_FLAG;  // 清除标志
            if (g_current_freq_hz < 20000) {  // 调整为20kHz上限，匹配检测范围
                g_current_freq_hz += 1000;
                // 更新显示
                LCD_ShowFloatNum1(85, 45, (float)g_current_freq_hz / 1000.0f, 4, BLACK, GREEN, 16);
                LCD_ShowString(135, 45, (const u8 *)"kHz", BLACK, GREEN, 16, 0);
            }
        }

        if (g_button_flags & BUTTON_DOWN_FLAG) {
            g_button_flags &= ~BUTTON_DOWN_FLAG;  // 清除标志
            if (g_current_freq_hz > 1000) {
                g_current_freq_hz -= 1000;
                // 更新显示
                LCD_ShowFloatNum1(85, 45, (float)g_current_freq_hz / 1000.0f, 4, BLACK, GREEN, 16);
                LCD_ShowString(135, 45, (const u8 *)"kHz", BLACK, GREEN, 16, 0);
            }
        }

        // --- 精准FFT频率计算（使用CMSIS-DSP库） ---
        static uint32_t fft_counter = 0;
        fft_counter++;

        if (fft_counter % 10 == 0) {  // 每10次循环计算一次FFT（40kHz采样率下平衡性能）
            static float last_freq = 0.0f;

            // 检查ADC数据是否有效
            uint16_t min_val = 4095, max_val = 0;
            for (int i = 0; i < 1024; i++) {
                if (gADCSamples[i] < min_val) min_val = gADCSamples[i];
                if (gADCSamples[i] > max_val) max_val = gADCSamples[i];
            }

            bool has_valid_signal = (max_val - min_val) > 50;  // 至少50个ADC码的变化

            // // 强制显示调试信息
            // LCD_ShowIntNum(10, 65, min_val, 4, BLUE, GREEN, 16);
            // LCD_ShowString(50, 65, (const u8 *)"min", BLUE, GREEN, 16, 0);
            // LCD_ShowIntNum(80, 65, max_val, 4, BLUE, GREEN, 16);
            // LCD_ShowString(120, 65, (const u8 *)"max", BLUE, GREEN, 16, 0);

            if (has_valid_signal) {
                // 优化的FFT数据准备（1024点，但使用循环DMA数据）
                // 计算DC偏移
                float dc_offset = 0.0f;
                for (int i = 0; i < 1024; i++) {
                    dc_offset += (float)gADCSamples[i];
                }
                dc_offset /= 1024.0f;

                // 填充FFT输入数组，去除DC分量
                for (int i = 0; i < 1024; i++) {
                    g_fft_input[2*i] = (float)gADCSamples[i] - dc_offset;  // 实部
                    g_fft_input[2*i + 1] = 0.0f;  // 虚部设为0
                }

                // 执行1024点FFT
                arm_cfft_f32(&g_fft_instance, g_fft_input, 0, 1);  // 0=FFT, 1=bit reversal

                // 计算幅度谱
                arm_cmplx_mag_f32(g_fft_input, g_fft_output, 1024);

                // 寻找最大幅度对应的频率（忽略DC分量，从索引1开始）
                uint32_t max_index = 1;
                float max_magnitude = g_fft_output[1];

                // 优化的FFT分析（40kHz采样率，1024点FFT，适合M0性能）
                // 频率分辨率 = 采样率 / FFT点数 = 40000 / 1024 ≈ 39.06 Hz
                float freq_resolution = 40000.0f / 1024.0f;

                // 计算搜索范围：1kHz到20kHz对应的bin索引
                int start_bin = (int)(1000.0f / freq_resolution);   // ~26
                int end_bin = (int)(20000.0f / freq_resolution);    // ~512 (奈奎斯特频率)
                if (end_bin > 512) end_bin = 512;

                for (int i = start_bin; i < end_bin; i++) {
                    if (g_fft_output[i] > max_magnitude) {
                        max_magnitude = g_fft_output[i];
                        max_index = i;
                    }
                }

                // 计算对应的频率
                float detected_freq = max_index * freq_resolution;

                // 精确的DDS频率范围滤波（支持1kHz-20kHz，适合M0性能）
                if (detected_freq >= 800 && detected_freq <= 21000 && max_magnitude > 50) {
                    // 更精确的滤波，提高响应速度
                    last_freq = last_freq * 0.5f + detected_freq * 0.5f;
                }

                // 显示FFT频率和调试信息
                LCD_ShowFloatNum1(200, 5, last_freq / 1000.0f, 4, RED, GREEN, 16);
                LCD_ShowString(250, 5, (const u8 *)"kHz", RED, GREEN, 16, 0);

                // // 调试信息：显示最大bin索引和幅度
                // LCD_ShowIntNum(200, 25, max_index, 3, RED, GREEN, 16);
                // LCD_ShowString(230, 25, (const u8 *)"bin", RED, GREEN, 16, 0);
                // LCD_ShowIntNum(200, 45, (uint16_t)max_magnitude, 4, RED, GREEN, 16);
                // LCD_ShowString(240, 45, (const u8 *)"mag", RED, GREEN, 16, 0);
            } else {
                // 无有效信号时显示0
                LCD_ShowString(200, 5, (const u8 *)"0.000kHz", RED, GREEN, 16, 0);
            }
        }

        // --- DDS持续输出 ---
        dds_set(g_current_freq_hz);

		// --- 使用通道管理结构绘制波形 ---
		u16 x0 = 0;
		u16 y0 = 80;
		u16 width = 320;
		u16 height = 120;
		u16 data_len = 32;
		u16 bgcolor = GREEN;

		// 绘制两路波形
		adc_channel_draw_waveform(&g_channels[0], x0, y0, width, height, data_len, bgcolor);  // 电流
		adc_channel_draw_waveform(&g_channels[1], x0, y0, width, height, data_len, bgcolor);  // 电压

        // Add a delay to quiet down the main loop, reducing digital noise
        delay_ms(100);
	}
}


void ADC12_0_INST_IRQHandler() {
  if (DL_ADC12_getPendingInterrupt(ADC12_0_INST) == DL_ADC12_IIDX_DMA_DONE) {
    adc0_done = 1;
  }
}


// --- 新增：ADC1的中断服务函数 ---
void ADC12_1_INST_IRQHandler() {
  if (DL_ADC12_getPendingInterrupt(ADC12_1_INST) == DL_ADC12_IIDX_DMA_DONE) {
    adc1_done = 1;
  }
}

// --- 正确的MSPM0中断处理（使用GROUP1） ---
void GROUP1_IRQHandler(void) {
    // 检查是否是GPIOB中断
    uint32_t group_iidx = DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1);

    if (group_iidx == BUTTONS_INT_IIDX) {
        uint32_t gpio_iidx = DL_GPIO_getPendingInterrupt(BUTTONS_PORT);

        // 立即清除中断标志
        DL_GPIO_clearInterruptStatus(BUTTONS_PORT, gpio_iidx);

        // 只设置标志，不在中断中处理复杂逻辑
        if (gpio_iidx == BUTTONS_FREQ_UP_IIDX) {
            g_button_flags |= BUTTON_UP_FLAG;
        }
        if (gpio_iidx == BUTTONS_FREQ_DOWN_IIDX) {
            g_button_flags |= BUTTON_DOWN_FLAG;
        }
    }
}