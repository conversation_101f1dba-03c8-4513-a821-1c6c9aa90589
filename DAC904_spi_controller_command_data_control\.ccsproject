<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="50:Theia-based"/>
	<ccsVersion value="70.1.1"/>
	<deviceFamily value="TMS470"/>
	<connection value="common/targetdb/connections/TIXDS110_Connection.xml"/>
	<executableActions value=""/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=spi_controller_command_data_control_LP_MSPM0G3507_nortos_ticlang.projectspec.DAC904_spi_controller_command_data_control,buildProfile=release,isHybrid=true"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3507.ccxml"/>
	<isTargetConfigurationManual value="false"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="C:\TI\mspm0_sdk_2_05_01_00\examples\nortos\LP_MSPM0G3507\driverlib\spi_controller_command_data_control\ticlang\spi_controller_command_data_control_LP_MSPM0G3507_nortos_ticlang.projectspec"/>
	<filesToOpen value="README.md,spi_controller_command_data_control.syscfg"/>
</projectOptions>
